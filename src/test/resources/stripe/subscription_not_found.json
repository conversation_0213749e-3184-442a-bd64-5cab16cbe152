{"id": "sub_HYVHcKCkr4dSRR", "object": "subscription", "application_fee_percent": null, "billing_cycle_anchor": **********, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": null, "collection_method": "charge_automatically", "created": **********, "current_period_end": 1596034073, "current_period_start": **********, "customer": "cus_00000000000000", "days_until_due": null, "default_payment_method": "pm_1Gxrl4EuYPEMgXLiLEmDdYfP", "default_source": null, "default_tax_rates": [], "discount": null, "ended_at": null, "items": {"object": "list", "data": [{"id": "si_HYnd8YWjuKCq0k", "object": "subscription_item", "billing_thresholds": null, "created": **********, "metadata": {}, "plan": {"id": "price_HNwvIzcFl0bb08", "object": "plan", "active": true, "aggregate_usage": null, "amount": 1500, "amount_decimal": "1500", "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_HNwvIzcFl0bb08", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 1500, "unit_amount_decimal": "1500"}, "quantity": 1, "subscription": "sub_HYVHcKCkr4dSRk", "tax_rates": []}], "has_more": false, "total_count": 1, "url": "/v1/subscription_items?subscription=sub_HYVHcKCkr4dSRk"}, "latest_invoice": "in_1GzOGzEuYPEMgXLiZ40oZCpe", "livemode": false, "metadata": {}, "next_pending_invoice_item_invoice": null, "pause_collection": null, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"id": "price_HNwvIzcFl0bb08", "object": "plan", "active": true, "aggregate_usage": null, "amount": 1500, "amount_decimal": "1500", "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "quantity": 1, "schedule": null, "start_date": **********, "status": "active", "tax_percent": null, "transfer_data": null, "trial_end": null, "trial_start": null}