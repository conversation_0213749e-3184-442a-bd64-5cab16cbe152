{"id": "sub_HYVHcKCkr4dSRk", "object": "subscription", "status": "canceled", "application_fee_percent": null, "billing_cycle_anchor": 1593514439, "billing_thresholds": null, "cancel_at": 1596034073, "cancel_at_period_end": false, "canceled_at": 1596034073, "collection_method": "charge_automatically", "created": 1593514439, "current_period_end": 1596034073, "current_period_start": 1593442073, "customer": "cus_00000000000000", "days_until_due": null, "default_payment_method": "pm_1Gxrl4EuYPEMgXLiLEmDdYfP", "default_source": null, "default_tax_rates": [], "discount": null, "ended_at": 1593514554, "items": {"object": "list", "data": [{"id": "si_HYojWXsCBDgebY", "object": "subscription_item", "billing_thresholds": null, "created": **********, "metadata": {}, "plan": {"id": "price_HNZOkC5OM0nt71", "object": "plan", "active": true, "aggregate_usage": null, "amount": 500, "amount_decimal": "500", "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_HNZOkC5OM0nt71", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 500, "unit_amount_decimal": "500"}, "quantity": 1, "subscription": "sub_HYVHcKCkr4dSRk", "tax_rates": []}], "has_more": false, "total_count": 1, "url": "/v1/subscription_items?subscription=sub_HYVHcKCkr4dSRk"}, "latest_invoice": "in_1Gzh6BEuYPEMgXLiaovFuX3a", "livemode": false, "metadata": {}, "next_pending_invoice_item_invoice": null, "pause_collection": null, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"id": "price_HNZOkC5OM0nt71", "object": "plan", "active": true, "aggregate_usage": null, "amount": 500, "amount_decimal": "500", "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}}