package com.centuroglobal.costofliving.controller

import com.centuroglobal.costofliving.data.entity.CityEntity
import com.centuroglobal.costofliving.data.entity.ExchangeRateEntity
import com.centuroglobal.costofliving.data.payload.CityPriceDto
import com.centuroglobal.costofliving.data.payload.Currency
import com.centuroglobal.costofliving.data.payload.PriceDto
import com.centuroglobal.costofliving.service.CostOfLivingService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.metrics.AccessLogService
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [CostOfLivingController::class])
@AutoConfigureMockMvc(addFilters = false)
class CostOfLivingControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var costOfLivingService: CostOfLivingService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val mapper = jacksonObjectMapper()

    private val cityEntity = CityEntity(
        1,
        "US",
        "America",
        "New York",
        1,
        0.0,
        0.0,
    )

    private val currencies = Currency(
        "$",
        "USD"
    )

    private val priceDto = PriceDto(
        0.0,
        0.0,
        0.0
    )

    private val cityPriceDto = CityPriceDto(
        1,
        "itemName",
        priceDto,
        priceDto
    )

    private val exchangeRateEntity = ExchangeRateEntity(
        "USD",
        "1"
    )

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `get city list test`() {
        val countryCode = "US"
        every { costOfLivingService.getCityList("US") } returns listOf(cityEntity)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/costofliving/city")
                .param("countryCode", countryCode)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").isNotEmpty())
            .andExpect(jsonPath("$.payload[0].id").value(cityEntity.id))
            .andExpect(jsonPath("$.payload[0].countryCode").value(cityEntity.countryCode))
            .andExpect(jsonPath("$.payload[0].country").value(cityEntity.country))
            .andExpect(jsonPath("$.payload[0].city").value(cityEntity.city))
            .andExpect(jsonPath("$.payload[0].latitude").value(cityEntity.latitude))
            .andExpect(jsonPath("$.payload[0].longitude").value(cityEntity.longitude))
    }

    /*@Test
    fun `get currency list test`() {
        every { costOfLivingService.getCurrencies() } returns listOf(currencies)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/costofliving/currency"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").isNotEmpty())
            .andExpect(jsonPath("$.payload[0].symbol").value(currencies.symbol))
            .andExpect(jsonPath("$.payload[0].code").value(currencies.code))
    }*/

    @Test
    fun `get city price test`() {
        val str = "String"
        val sourceCountryCode = "US"
        val sourceCityId = "1"
        val currency = "USD"
        val targetCountryCode = "UK"
        val targetCityId = "1"

        every { costOfLivingService.getCityPrice("US", 1, "USD", "UK", 1) } returns mutableMapOf(
            Pair(
                "String",
                mutableListOf(cityPriceDto)
            ))
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/costofliving/cityPrice")
                .param("sourceCountryCode", sourceCountryCode)
                .param("sourceCityId", sourceCityId)
                .param("currency", currency)
                .param("targetCountryCode", targetCountryCode)
                .param("targetCityId", targetCityId)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").isNotEmpty())
            .andExpect(jsonPath("$.success").value(true))

    }

    @Test
    fun `get exchange rate list test`() {
        every { costOfLivingService.updateExchangeRatesList() } returns listOf(exchangeRateEntity)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            MockMvcRequestBuilders.post("/api/${AppConstant.API_VERSION}/costofliving/exchangeRates")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").isNotEmpty())
            .andExpect(jsonPath("$.success").value(true))
    }
}