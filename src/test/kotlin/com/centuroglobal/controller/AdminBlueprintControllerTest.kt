package com.centuroglobal.controller

import com.centuroglobal.data.payload.blueprint.UpdateBlueprintRequest
import com.centuroglobal.data.payload.blueprint.UpdateBlueprintStatusRequest
import com.centuroglobal.facade.AdminBlueprintFacade
import com.centuroglobal.service.BlueprintService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.pojo.Blueprint
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.StepExpertRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminBlueprintController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminBlueprintControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminBlueprintFacade: AdminBlueprintFacade

    @MockkBean
    private lateinit var blueprintService: BlueprintService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.isAuthenticated } returns true
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.name } returns "abc"
    }

    @Test
    fun `test retrieve`() {
        val blueprint = Blueprint(
            countryCode = "IN",
            countryName = "INDIA",
            status = BlueprintStatus.ACTIVE,
            lastPublishedDate = 1234,
            steps = listOf(),
            sessionId = "1234"
        )
        every { adminBlueprintFacade.retrieveBluePrintForAdmin("US") } returns CompletableFuture.completedFuture(blueprint)

        mockMvc.perform(get("/api/v1/admin/blueprint/US"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test update`() {
        val request = UpdateBlueprintRequest(
            action = "",
            steps = listOf()
        )
        every { blueprintService.updateBlueprint(any(), any(), any()) } returns true

        mockMvc.perform(put("/api/v1/admin/blueprint/US")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test updateStatus`() {
        val request = UpdateBlueprintStatusRequest(
            status = ""
        )
        every { blueprintService.updateBlueprintStatus(any(), any(), any()) } returns true

        mockMvc.perform(put("/api/v1/admin/blueprint/US/status")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test updateExpert`() {

        val request = StepExpertRequest()

        val expertProfiles = listOf(ExpertProfileSummary(
            id = 4,
            displayName = "abc",
            jobTitle = "title",
            contactEmail = "<EMAIL>",
            contactNumber = "*********0",
            contactWebsite = "a.com",
            bio = "bio",
            profilePictureFullUrl = "a",
            countryName = "IN",
            countryCode = "IN",
            regionName = "",
            expertiseName = listOf(),
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            infoVideoUrl = "",
            isCurrentCircleJoined = true
        ))
        every { adminBlueprintFacade.updateExpert(any(), any(), any(), any()) } returns CompletableFuture.completedFuture(expertProfiles)

        mockMvc.perform(put("/api/v1/admin/blueprint/US/expert/STEP_0")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    /*@Test
    fun `test uploadPdf`() {

        val pdfFile = MockMultipartFile("pdf", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "PDF content".toByteArray())

        every { adminBlueprintFacade.uploadPdf(any(), any(), any(), any()) } returns CompletableFuture.completedFuture("abc")

        mockMvc.perform(put("/api/v1/admin/blueprint/US/pdf/STEP_0")
            .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
            .content(pdfFile.bytes)
            .param("pdf", pdfFile.toString()))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value("http://example.com/test.pdf"))
    }*/

    @Test
    fun `test deletePdf`() {

        every { adminBlueprintFacade.deletePdf(any(), any(), any()) } returns CompletableFuture.completedFuture("abc")

        mockMvc.perform(delete("/api/v1/admin/blueprint/US/pdf/STEP_0"))
            .andExpect(status().isOk)

    }
}
