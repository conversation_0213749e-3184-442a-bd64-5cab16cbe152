package com.centuroglobal.controller

import com.centuroglobal.data.payload.ServiceDetails
import com.centuroglobal.data.payload.UpdateRfpRequest
import com.centuroglobal.service.RfpService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.query.QueryStats
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import java.time.LocalDateTime


@WebMvcTest(controllers = [AdminRFPController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminRfpControllerTest(@Autowired val mockMvc:MockMvc){

    @MockkBean
    lateinit var rfpService:RfpService

    @MockkBean
    lateinit var accessLogService:AccessLogService

    private val authenticatedUser:AuthenticatedUser= mockk()
    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }


    @Test
    fun `edit rfp test`(){

        val rfpRequest = UpdateRfpRequest(
            id=1,
            heading = "Test RFP-1",
            serviceDetails = listOf(
                ServiceDetails(
                serviceName= "BUSINESS_INCORPORATION",
                countries= listOf("IN","US", "CA"))),
            description = "This is test description for RFP-1",
            assignedExperts = listOf(1))



        every { authenticatedUser.principal } returns authenticatedUser
        every { rfpService.editRfp(rfpRequest,authenticatedUser) } returns 1L

        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/rfp/edit")
            .content(mapper.writeValueAsString(rfpRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8"))
                .andExpect(status().isOk)
    }


    @Test
    fun `fetch all the rfp`(){

        val search = "Test"
        val account = "1"
        val user = "23"
        val responses = "TestResponses"
        val categories = "Category1"
        val country = "US"
        val archive = false
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val corporateId = 2L
        val from = 1L
        val to = 1L

        val rfpSearchFilter = RfpSearchFilter.Builder.build(
            search,
            null,
            null,
            null,
            categories,
            country,
            if (archive) listOf(RfpStatus.RESOLVED, RfpStatus.CANCELLED) else listOf(RfpStatus.OPEN),
            corporateId,
            from,
            to,
            null,
            null,
            null)
        val pageRequest = PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
        val totalElements = 1L

        val userProfile = CorporateUserProfile(
            id = 2,
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            role = Role.ROLE_SUPER_ADMIN,
            status = AccountStatus.ACTIVE,
            countryCode = "AU",
            profilePictureFullUrl = "",
            companyName = "abc",
            userType = PartnerCaseType.CG,
            bandName = "abc"
        )

        val rfpCardDetails = RfpCardDetails(
            id = 1,
            heading = "Test Heading",
            description = "Test Description",
            raisedBy = userProfile,
            status = RfpStatus.OPEN.name,
            submittedOn = TimeUtil.toEpochMillis(LocalDateTime.now()),
            services= listOf(),
            isProposalApproved = true)

        val rfps = PagedResult(listOf(rfpCardDetails), totalElements, 0, 1)

        val queryStats = QueryStats(2,1,1,1,1)

        val rfpStatsResponse = RfpStatsResponse(rfps,queryStats)

        every { rfpService.listRfpForAdmin(rfpSearchFilter, pageRequest, authenticatedUser) } returns rfpStatsResponse

        every { authenticatedUser.partnerId } returns 5
        every { rfpService.listRfpForAdmin(any(), any(), any()) } returns rfpStatsResponse

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/rfp")
                .param("search", search)
                .param("corporate", corporateId.toString())
                .param("from", from.toString())
                .param("to", to.toString())
                .param("status",rfpCardDetails.status)
                .param("download",false.toString())
                .param("account", account)
                .param("user", user)
                .param("responses", responses)
                .param("categories", categories)
                .param("country", country)
                .param("archive", archive.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isMap)

    }
}
