package com.centuroglobal.controller

import com.centuroglobal.controller.AdminWorkLogController
import com.centuroglobal.service.WorkLogService
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.subscription.WorkLogCorporateStats
import com.centuroglobal.shared.data.pojo.subscription.WorkLogRequest
import com.centuroglobal.shared.data.pojo.subscription.WorkLogResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.core.context.SecurityContextHolder

class AdminWorkLogControllerTest {

    private val workLogService: WorkLogService = mockk()
    private val adminWorkLogController = AdminWorkLogController(workLogService)

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 123L
    }

    @Test
    fun `create should return Response with created work log ID`() {
        val request = mockk<WorkLogRequest>()

        val expectedId = 123L

        every { workLogService.create(any(), any()) } returns expectedId

        val response = adminWorkLogController.create(request, authenticatedUser)

        assertEquals(Response(true, expectedId), response)
    }

    @Test
    fun `plans should return Response with PagedResult of WorkLogResponse`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val pagedResult = mockk<PagedResult<WorkLogResponse>>()

        every { workLogService.list(any(), any(), any()) } returns pagedResult

        val response = adminWorkLogController.plans(
            authenticatedUser, null, null, null, null, null, null, 0, 20, "DESC", "lastUpdatedDate", false
        )

        assertEquals(Response(true, pagedResult), response)
        verify { workLogService.list(any(), any(), any()) }
    }

    @Test
    fun `delete should return Response with deletion result`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val workLogId = 123L
        val deletionResult = true

        every { workLogService.delete(workLogId, authenticatedUser) } returns deletionResult

        val response = adminWorkLogController.delete(workLogId, authenticatedUser)

        assertEquals(Response(true, deletionResult), response)
        verify { workLogService.delete(workLogId, authenticatedUser) }
    }

    @Test
    fun `usageStats should return Response with WorkLogCorporateStats`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val corporateId = 456L
        val stats = mockk<WorkLogCorporateStats>()

        every { workLogService.usageStats(corporateId, authenticatedUser) } returns stats

        val response = adminWorkLogController.usageStats(corporateId, authenticatedUser)

        assertEquals(Response(true, stats), response)
        verify { workLogService.usageStats(corporateId, authenticatedUser) }
    }

    @Test
    fun `referenceIdList should return Response with list of reference IDs`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val referenceType = ReferenceType.CASE
        val corporateId = 789L
        val referenceIds = listOf(1L, 2L, 3L)

        every { workLogService.referenceIdList(referenceType, corporateId, authenticatedUser) } returns referenceIds

        val response = adminWorkLogController.referenceIdList(referenceType, corporateId, authenticatedUser)

        assertEquals(Response(true, referenceIds), response)
        verify { workLogService.referenceIdList(referenceType, corporateId, authenticatedUser) }
    }



}
