package com.centuroglobal.controller

import com.centuroglobal.data.payload.TourRequest
import com.centuroglobal.service.TourService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [TourController::class])
@AutoConfigureMockMvc(addFilters = false)
class TourControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var tourService: TourService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
    }

    @Test
    fun `test get tour`() {
        val tourRequest = TourRequest(true)
        every { tourService.getTour(authenticatedUser) } returns true

        mockMvc.perform(get("/api/v1/tour/")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.enable").value(tourRequest.enable))
    }

    @Test
    fun `test update tour`() {
        val tourRequest = TourRequest(true)
        every { tourService.updateTour(authenticatedUser, tourRequest.enable) } returns true

        mockMvc.perform(post("/api/v1/tour/")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(tourRequest)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }
}
