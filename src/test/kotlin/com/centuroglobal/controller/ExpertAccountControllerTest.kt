package com.centuroglobal.controller

import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ExpertAccountController::class])
@AutoConfigureMockMvc(addFilters = false)
class ExpertAccountControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var expertFacade: ExpertFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val mapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `test list expert accounts`() {
        val accounts = mapOf("account1" to "details1", "account2" to "details2")
        val deferredResult = DeferredResult<Response<Map<String, Any>>>()
        deferredResult.setResult(Response(true, accounts))

        every { expertFacade.listAccounts(any()) } returns CompletableFuture.completedFuture(accounts)

        mockMvc.perform(get("/api/v1/expert/account")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }
}
