package com.centuroglobal.controller

import com.centuroglobal.facade.EventFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.EventSpeakerType
import com.centuroglobal.shared.data.enums.EventStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.event.EventClientProfile
import com.centuroglobal.shared.data.pojo.event.EventDetails
import com.centuroglobal.shared.data.pojo.event.EventSession
import com.centuroglobal.shared.data.pojo.event.EventSpeaker
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.context.request.async.DeferredResult
import java.util.*
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [EventController::class])
@AutoConfigureMockMvc(addFilters = false)
class EventControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var eventFacade: EventFacade

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService


    private val mapper = ObjectMapper()

    val eventDetails = EventDetails(
        id = 5,
        name = "abc",
        about = "abc",
        bannerImageFullUrl = "",
        startDate = 1234,
        endDate = 1234,
        startTime = Date(),
        endTime = Date(),
        duration = "3",
        timeZone = "TZ" ,
        status = EventStatus.PUBLISHED,
        location = "",
        inviteesExternal = "",
        locationDetails = "",
        sessions = 4,
        speakers = 4,
        invitees = 3,
        attending = 2,
        rsvpFlag = true,
        joinFlag = false,
        attendingFlag = false,
        attendedFlag = false,
        isInvitee = true
    )

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `test retrieve`() {
        val eventId = 1L

        val deferredResult = DeferredResult<Response<EventDetails>>()
        deferredResult.setResult(Response(true, eventDetails))

        every { eventFacade.retrieve(eventId, authenticatedUser) } returns CompletableFuture.completedFuture(eventDetails)

        mockMvc.perform(get("/api/v1/event/$eventId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list`() {
        val pagedResult = PagedResult(listOf(eventDetails), 1, 1, 1)
        val deferredResult = DeferredResult<Response<PagedResult<EventDetails>>>()
        deferredResult.setResult(Response(true, pagedResult))

        every { eventFacade.listEvents(any(), any(), any()) } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(get("/api/v1/event/event-listing")
            .param("pageIndex", "0")
            .param("pageSize", "20")
            .param("sort", "DESC")
            .param("sortBy", "createdDate"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test speakerList`() {
        val eventId = 1L
        val pagedResult = PagedResult(listOf(EventSpeaker(
            id = 4,
            type = EventSpeakerType.EXTERNAL,
            internalMemberRole = Role.ROLE_ADMIN,
            internalMemberId = 2,
            isHost = true,
            profile = null,
            expertProfile = null
        )), 1, 1, 1)
        val deferredResult = DeferredResult<Response<PagedResult<EventSpeaker>>>()
        deferredResult.setResult(Response(true, pagedResult))

        every { eventFacade.speakerList(eventId, any(), any()) } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(get("/api/v1/event/speaker-host/$eventId")
            .param("pageIndex", "0")
            .param("pageSize", "20"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test sessionList`() {
        val eventId = 1L
        val sessionList = listOf(listOf(EventSession(
            id = 6,
            name = "",
            about = "",
            date = 1234,
            timeZone = "TZ",
            startTime = Date(),
            endTime = Date(),
            duration = "12",
            speakers = listOf()
        )))
        val deferredResult = DeferredResult<Response<List<List<EventSession>>>>()
        deferredResult.setResult(Response(true, sessionList))

        every { eventFacade.sessionList(eventId, any(), any()) } returns CompletableFuture.completedFuture(sessionList)

        mockMvc.perform(get("/api/v1/event/session/$eventId")
            .param("pageIndex", "0"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test inviteeList`() {
        val eventId = 1L
        val pagedResult = PagedResult(listOf(EventClientProfile(
            id = 3,
            internalId = "id",
            userId = 2,
            firstName = "abc",
            lastName = "def",
            about = "",
            companyName = "",
            jobTitle = "",
            countryCode = "IN",
            emailId = "<EMAIL>",
            profilePictureKey = "",
            profilePictureFullUrl = "",
            isAttending = true
        )), 1, 1, 1)
        val deferredResult = DeferredResult<Response<PagedResult<EventClientProfile>>>()
        deferredResult.setResult(Response(true, pagedResult))

        every { eventFacade.inviteeList(eventId, any(), any()) } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(get("/api/v1/event/invitee/$eventId")
            .param("pageIndex", "0")
            .param("pageSize", "20"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test attendeeList`() {
        val eventId = 1L
        val pagedResult = PagedResult(listOf(EventClientProfile(
            id = 3,
            internalId = "id",
            userId = 2,
            firstName = "abc",
            lastName = "def",
            about = "",
            companyName = "",
            jobTitle = "",
            countryCode = "IN",
            emailId = "<EMAIL>",
            profilePictureKey = "",
            profilePictureFullUrl = "",
            isAttending = true
        )), 1, 1, 1)
        val deferredResult = DeferredResult<Response<PagedResult<EventClientProfile>>>()
        deferredResult.setResult(Response(true, pagedResult))

        every { eventFacade.attendeeList(eventId, any(), any()) } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(get("/api/v1/event/attendee/$eventId")
            .param("pageIndex", "0")
            .param("pageSize", "20"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test rsvp`() {
        val eventId = 1L
        val deferredResult = DeferredResult<Response<String>>()
        deferredResult.setResult(Response(true, "RSVP successful"))

        every { eventFacade.rsvp(eventId, authenticatedUser) } returns CompletableFuture.completedFuture("")

        mockMvc.perform(post("/api/v1/event/rsvp/$eventId"))
            .andExpect(status().isOk)
    }
}
