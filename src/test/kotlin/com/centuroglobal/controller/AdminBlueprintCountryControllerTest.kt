package com.centuroglobal.controller

import com.centuroglobal.facade.AdminBlueprintFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.blueprint.BluePrintCountryStats
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintCountry
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintCountryListing
import com.centuroglobal.shared.security.AuthenticatedUser
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminBlueprintCountryController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminBlueprintCountryControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminBlueprintFacade: AdminBlueprintFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test countryListing`() {
        val blueprintCountryListing = BlueprintCountryListing(
            data = PagedResult(
                listOf(BlueprintCountry(
                    countryCode = "IN",
                    status = BlueprintStatus.ACTIVE,
                    updatedBy = "123",
                    createdDate = 1234,
                    lastUpdatedAt = 1234,
                    isNoData = true,
                    countryName = "INDIA"
                )),
                totalElements = 11,
                currentPage = 1,
                totalPages = 10
            ),
            stats = BluePrintCountryStats(
                countries = 12,
                active = 2,
                inactive = 10
            )
        )
        every { adminBlueprintFacade.retrieveCountryList(any(), any()) } returns CompletableFuture.completedFuture(blueprintCountryListing)

        mockMvc.get("/api/v1/admin/country-listing")
            .andExpect {
                status().isOk
            }
    }
}
