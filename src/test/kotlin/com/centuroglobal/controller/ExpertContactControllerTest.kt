package com.centuroglobal.controller

import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ExpertContactController::class])
@AutoConfigureMockMvc(addFilters = false)
class ExpertContactControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var expertFacade: ExpertFacade

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `test notifyAdmin`() {
        val contactedExpertId = 123L
        val deferredResult = DeferredResult<Response<Boolean>>()
        deferredResult.setResult(Response(true, true))

        every { expertFacade.notifyAdmin(any(), any()) } returns CompletableFuture.completedFuture(true)

        mockMvc.perform(put("/api/v1/expert/profile/notify-admin/$contactedExpertId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }
}
