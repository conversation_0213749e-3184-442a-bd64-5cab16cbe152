package com.centuroglobal.controller

import com.centuroglobal.service.CaseService
import com.centuroglobal.service.DocRepoService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.payload.case.CaseFeesInvoiceRequest
import com.centuroglobal.shared.data.payload.case.CaseFeesRequest
import com.centuroglobal.shared.data.pojo.CaseReferenceData
import com.centuroglobal.shared.data.pojo.case.CaseDocumentActivityDetails
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [AdminCaseController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCaseControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var caseService: CaseService

    @MockkBean
    private lateinit var caseRepository: CaseRepository

    @MockkBean
    private lateinit var docRepoService: DocRepoService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test delete case`() {
        val caseId = "1"
        every { caseService.deleteCase(caseId.toLong(), authenticatedUser) } returns "Case deleted"

        mockMvc.delete("/api/v1/admin/case/$caseId")
            .andExpect {
                status().isOk
            }
    }

    @Test
    fun `test update case history`() {
        val caseHistoryId = 1L
        every { caseService.updateCaseHistory(caseHistoryId, any(), any()) } returns Unit


        mockMvc.perform(
            put("/api/v1/admin/case/history/$caseHistoryId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(mapOf(Pair("key", "value")))))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))

    }

    @Test
    fun `test list logs`() {
        val caseId = 1L
        val logType = "ACTIVITY"
        val docType = "type"
        val logs = listOf<CaseDocumentActivityDetails>()
        every { caseService.listDocumentActivityLogs(caseId, logType, docType, authenticatedUser) } returns logs

        mockMvc.get("/api/v1/admin/case/$caseId/document/logs") {
            param("logType", logType)
            param("docType", docType)
        }.andExpect {
            status().isOk
        }
    }

    @Test
    fun `test updatePartnerCg`() {
        val caseId = 1L
        val cgRequested = true

        every { caseService.updatePartnerCg(caseId, cgRequested, authenticatedUser) } returns true

        mockMvc.perform(put("/api/v1/admin/case/$caseId/request-cg")
            .param("cgRequested", cgRequested.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `test caseInvoiceUpdate`() {
        val caseId = 1L
        val request = CaseFeesInvoiceRequest()

        every { caseService.caseFeesInvoiceUpdate(caseId, request, authenticatedUser) } returns Unit

        mockMvc.perform(put("/api/v1/admin/case/$caseId/invoice")
            .content(objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `test caseFees`() {
        val caseId = 1L
        val request = CaseFeesRequest()

        every { caseService.createCaseFees(caseId, request, authenticatedUser) } returns Unit

        mockMvc.perform(put("/api/v1/admin/case/$caseId/fees")
            .content(objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `test linkCase`() {
        val caseId = 1L
        val linkCaseIds = listOf(2L, 3L)

        every { caseService.linkCase(caseId, linkCaseIds, authenticatedUser) } returns true

        mockMvc.perform(put("/api/v1/admin/case/$caseId/link")
            .content(objectMapper.writeValueAsString(linkCaseIds))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `test unlinkCase`() {
        val caseId = 1L
        val linkCaseIds = listOf(2L, 3L)
        every { caseService.unlinkCase(caseId, linkCaseIds) } returns true

        mockMvc.perform(put("/api/v1/admin/case/$caseId/unlink")
            .contentType(MediaType.APPLICATION_JSON)
            .content(ObjectMapper().writeValueAsString(linkCaseIds)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test caseIdsByUser`() {
        val userId = "1"
        val caseReferenceData = CaseReferenceData(
            id = 4,
            initiatedFor = "abcd"
        )
        every { caseService.getCaseList(userId.toLong()) } returns listOf(caseReferenceData)

        mockMvc.perform(
             get("/api/v1/admin/case/$userId/owner")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
    }


    @Test
    fun `test listOfUsers`() {
        val caseId = "1"
        every { caseService.retrieveCorporateUsersList(caseId.toLong(), authenticatedUser) } returns listOf()

        mockMvc.perform(get("/api/v1/admin/case/$caseId/corporate-users")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
    }

}
