package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.enums.BandStatus
import com.centuroglobal.shared.data.pojo.BandCardDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.BandService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.pojo.ReferenceData
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status


@WebMvcTest(controllers = [BandController::class])
@AutoConfigureMockMvc(addFilters = false)
class BandControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var bandService: BandService

    @MockkBean
    lateinit var accessLogService: AccessLogService


    private val authenticatedUser: AuthenticatedUser = mockk()

    private val mapper = jacksonObjectMapper()

    private val bandsEntity = BandsEntity(
        1,
        "Name",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null)

    private val bandCardDetails = BandCardDetails(
        bandsEntity.id!!,
        bandsEntity.name!!,
        null,
        null,
        bandsEntity.status,
        1L,
        null,
        null,
        null,
        null)

    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun ` bands For Corporate From Controller`(){

        every { authenticatedUser.companyId } returns 1
        every { bandService.listBands(1) } returns  listOf(ReferenceData(bandsEntity.id, bandsEntity.name))

        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${ AppConstant.API_VERSION}/band"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").isNotEmpty())
            .andExpect(jsonPath("$.payload[0].id").value(bandsEntity.id))
            .andExpect(jsonPath("$.payload[0].name").value(bandsEntity.name))
    }

    @Test
    fun `bands Listing for Corporate test`()
    {
        every { authenticatedUser.companyId } returns 1
        every { bandService.bandsListing(1)} returns  listOf(bandCardDetails)
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${ AppConstant.API_VERSION}/band/listing"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").isNotEmpty())
    }
}