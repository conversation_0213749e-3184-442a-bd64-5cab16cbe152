package com.centuroglobal.controller

import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.task.TaskGenerationStatus
import com.centuroglobal.shared.data.enums.task.TaskWorkflowStatus
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskWorkflowRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(controllers = [AdminTaskWorkflowController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminTaskWorkflowControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var taskWorkflowService: TaskWorkflowService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val mapper = jacksonObjectMapper()

        private val taskWorkflowDetails = TaskWorkflowDetails(
        id = 1,
        name = "Test Workflow",
        description = "Test Description",
        country = "US",
        status = TaskWorkflowStatus.ACTIVE,
        category = "Test Category",
        taskGenerationStatus = TaskGenerationStatus.COMPLETED,
        showToPartner = false,
        updatedBy = "abc",
        lastUpdated = 1L,
        task =1L,
        relatedCases = 1L,
        partnerId = 1L,
        partnerName = "ABC"
    )

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `create task workflow test`() {
        val request = CreateTaskWorkflowRequest(
            name = "Test Workflow",
            description = "Test Description",
            country = "US",
            status = TaskWorkflowStatus.ACTIVE,
            category = "Test Category",
            showToPartner = false
        )

        every { taskWorkflowService.create(request, authenticatedUser) } returns 1L

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/admin/task-workflow")
                .content(mapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(1))
    }

    @Test
    fun `get task workflow by id test`() {
        val workflowId = 1L

        every { taskWorkflowService.getWorkflow(workflowId, authenticatedUser) } returns taskWorkflowDetails

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.id").value(workflowId))
            .andExpect(jsonPath("$.payload.name").value(taskWorkflowDetails.name))
    }

    @Test
    fun `update task workflow test`() {
        val workflowId = 1L
        val request = CreateTaskWorkflowRequest(
            name = "Updated Workflow",
            description = "Updated Description",
            country = "US",
            status = TaskWorkflowStatus.ACTIVE,
            category = "Updated Category",
            showToPartner = false
        )

        every { taskWorkflowService.updateTaskWorkflow(workflowId, request, authenticatedUser) } returns workflowId

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId")
                .content(mapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(workflowId))
    }

    @Test
    fun `delete task workflow test`() {
        val workflowId = 1L

        every { taskWorkflowService.deleteWorkflow(workflowId, authenticatedUser) } returns true

        mockMvc.perform(
            delete("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `list task workflows test`() {
        val name = "Test"
        val country = "US"
        val category = "Test Category"
        val status = TaskWorkflowStatus.ACTIVE
        val partnerId = 1L
        val updatedBy = 1L
        val isPartner = false
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "lastUpdatedDate"

        val pagedResult = PagedResult(listOf(taskWorkflowDetails), 1, 0, 1)

        every { 
            taskWorkflowService.list(
                any(),
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task-workflow/listing")
                .param("name", name)
                .param("country", country)
                .param("category", category)
                .param("status", status.name)
                .param("partnerId", partnerId.toString())
                .param("updatedBy", updatedBy.toString())
                .param("isPartner", isPartner.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows").isNotEmpty)
            .andExpect(jsonPath("$.payload.totalElements").value(1))
    }

    @Test
    fun `update task workflow status test`() {
        val workflowId = 1L
        val status = TaskWorkflowStatus.ACTIVE

        every { taskWorkflowService.updateTaskStatus(workflowId, status, authenticatedUser) } returns true

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId/status")
                .param("status", status.name)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `copy task workflow test`() {
        val workflowId = 1L

        every { taskWorkflowService.copyTaskWorkflow(workflowId, authenticatedUser) } returns 2L

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId/copy")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(2))
    }

    @Test
    fun `link task workflow test`() {
        val workflowId = 1L
        val referenceId = 2L
        val referenceType = "CASE"

        every { taskWorkflowService.link(workflowId, referenceId, referenceType, authenticatedUser) } returns 3L

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/task-workflow/$workflowId/link/$referenceType/$referenceId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(3))
    }
}