package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.payload.ExtractDocDetailsRequest
import com.centuroglobal.shared.data.payload.PassportDocumentRequest
import com.centuroglobal.shared.data.payload.UserDocumentRequest
import com.centuroglobal.shared.data.payload.VisaDocumentRequest
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.passportvisa.DocumentMetadataGeneric
import com.centuroglobal.shared.data.pojo.passportvisa.PassportVisaResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@WebMvcTest(controllers = [UserProfileController::class])
@AutoConfigureMockMvc(addFilters = false)
class UserProfileControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var adminUserService: AdminUserService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var corporateService: CorporateService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `upload user documents`() {
        val request = UserDocumentRequest(
            docName = "abc",
            fileData = mutableListOf(),
            expiryDate = 123456,
            docSubType = "",
            issueCountry = "IN",
            userId = 1
        )

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.uploadDocumentUser(request, authenticatedUser, any()) } returns true

        mockMvc.perform(
            post("/api/v1/user-profile/doc")
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `upload user passport`() {
        val request = PassportDocumentRequest(
            birthDate = "",
            birthPlace = "",
            nationality = "IN",
            issueDate = "123456",
            issuePlace = "PN",
            expiryDate = "",
            gender = "M",
            docFiles = mutableListOf(),
            userId = 1234
        )

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.updatePassport(request, authenticatedUser, any()) } returns true

        mockMvc.perform(
            post("/api/v1/user-profile/passport")
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `upload user visa`() {
        val request = VisaDocumentRequest(
            name = "avc",
            visaType = "",
            country = "IN",
            issueDate = "",
            expiryDate = "",
            docFiles = mutableListOf(),
            userId = 1
        )

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.updateVisa(request, authenticatedUser, any()) } returns true

        mockMvc.perform(
            post("/api/v1/user-profile/visa")
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `test extractDocsData`() {
        val request = ExtractDocDetailsRequest(
            doc_type = "",
            bucket = "",
            s3Key = mutableListOf()
        )
        val response = DocumentMetadataGeneric()

        every { adminUserService.extractDocumentMetadataResponse(request) } returns response

        mockMvc.perform(post("/api/v1/user-profile/extract-doc")
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapper.writeValueAsString(request)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test docList`() {
        val response = PassportVisaResponse(
            passportList = listOf(),
            visaList = listOf()
        )

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.listPassportVisa(authenticatedUser, 1) } returns response

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/passport-visa/1"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    /*@Test
    fun `test retrievePassport`() {
        val passportId = 1L
        val response = mockk<GetPassportResponse>()

        every { adminUserService.retrievePassport(authenticatedUser, 1, null) } returns response

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/passport/$passportId"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test retrieveVisa`() {
        val visaId = 1L
        val response = mockk<GetVisaResponse>()

        every { adminUserService.retrieveVisa(authenticatedUser, visaId, null) } returns response

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/visa/$visaId"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }*/


    @Test
    fun `test docsList`() {
        val response = PagedResult<CorporateDocumentResponse>(listOf(), 0, 0, 0)

        every { corporateService.canUpdateUser(any(), any()) } returns true


        every { adminUserService.listDocuments(any(), any(), any(), any()) } returns response

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/docs/1"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test deleteDoc`() {

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.deleteDocumentUser(any(), any(), any()) } returns true

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/v1/user-profile/1/doc/1"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test viewUrl`() {
        val url = "http://example.com/document"

        every { corporateService.canUpdateUser(any(), any()) } returns true


        every { adminUserService.getDocUrlUser(any(), any(), any()) } returns url

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/1/view-doc/1"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(url))
    }

    @Test
    fun `test viewPassportVisa`() {
        val url = "http://example.com/passport-visa"

        every { corporateService.canUpdateUser(any(), any()) } returns true


        every { adminUserService.viewPassportVisa(any()) } returns url

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/view-passport-visa")
            .param("s3key", "some-key"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(url))
    }

    @Test
    fun `test downloadPassportVisa`() {
        val streamingResponseBody = StreamingResponseBody { outputStream -> outputStream.write("content".toByteArray()) }

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.downloadPassportVisaFiles(any(), any(), any()) } returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/1/passport-visa/1"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test downloadDoc`() {
        val streamingResponseBody = StreamingResponseBody { outputStream -> outputStream.write("content".toByteArray()) }

        every { corporateService.canUpdateUser(any(), any()) } returns true

        every { adminUserService.downloadDocUser(any(), any(), any()) } returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user-profile/download-doc/1")
            .param("fileId", "1"))
            .andExpect(status().isOk)
    }

}
