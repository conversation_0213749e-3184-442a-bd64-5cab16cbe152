package com.centuroglobal.controller

import com.centuroglobal.data.payload.TaskStatusUpdateRequest
import com.centuroglobal.service.TaskService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.TaskAssigneeType
import com.centuroglobal.shared.data.enums.task.*
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.task.dto.TaskAssigneeDto
import com.centuroglobal.shared.data.pojo.task.dto.TaskDetails
import com.centuroglobal.shared.data.pojo.task.dto.TaskReminderDto
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskCountResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import java.time.LocalDateTime

@WebMvcTest(controllers = [AdminTaskController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminTaskControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var taskService: TaskService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    private val mapper = jacksonObjectMapper()

    private val taskDetails = TaskDetails(
        id = 1,
        name = "Test Task",
        description = "Test Description",
        status = TaskStatus.NOT_STARTED,
        referenceId = 1,
        referenceType = ReferenceType.CASE,
        visibility = TaskVisibility.PUBLIC,
        dueDate = System.currentTimeMillis(),
        completedDate = null,
        priority = "HIGH",
        assignee = mutableListOf(
            TaskAssigneeDto(
                assigneeType = TaskAssigneeType.TEAM,
                userId = 1,
                userProfile = null,
                companyId = 1
            )
        ),
        createdBy = null,
        category = listOf("Test Category"),
        overDueDays = 0,
        createdAt = System.currentTimeMillis(),
        reminders = listOf(
            TaskReminderDto(System.currentTimeMillis())
        ),
        usefulLinks = "https://test.com",
        instruction = "Test instruction",
        progress = "0",
        caseMilestone = "Test milestone",
        plannedStartDate = System.currentTimeMillis(),
        startDate = System.currentTimeMillis(),
        expectedDueDate = System.currentTimeMillis()
    )

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId }  returns 2L
    }

    @Test
    fun `create task test`() {
        val request = CreateTaskRequest(
            name = "Test Task",
            description = "Test Description",
            referenceId = 1,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = System.currentTimeMillis(),
            completedDate = null,
            priority = "HIGH",
            status = TaskStatus.NOT_STARTED,
            taskAssignee = listOf(),
            taskReminder = listOf(),
            usefulLinks = "https://test.com",
            instruction = "Test instruction"
        )

        every { taskService.createTask(request, authenticatedUser) } returns 1L

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/admin/task")
                .content(mapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isCreated)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(1))
    }

    @Test
    fun `get tasks test`() {
        val name = "Test"
        val status = TaskStatus.NOT_STARTED.name
        val partnerId = 1L
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "dueDate"
        val createdBy = 1L
        val taskView = "ALL_TASK"
        val type = ReferenceType.CASE.name
        val priority = "HIGH"
        val referenceId = 1L
        val showBy = ShowBy.TODAY
        val assignedTo = 1L

        val pagedResult = PagedResult(listOf(taskDetails), 1, 0, 1)

        every { 
            taskService.listTasks(
                any(),
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            ) 
        } returns pagedResult

        every {
            taskService.getCompanyIdAndType(authenticatedUser)
        } returns Pair(partnerId, TaskCompanyType.CORPORATE)

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task/listing")
                .param("name", name)
                .param("status", status)
                .param("partnerId", partnerId.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .param("createdBy", createdBy.toString())
                .param("taskView", taskView)
                .param("type", type)
                .param("priority", priority)
                .param("referenceId", referenceId.toString())
                .param("showBy", showBy.name)
                .param("assignedTo", assignedTo.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows").isNotEmpty)
            .andExpect(jsonPath("$.payload.totalElements").value(1))
    }

    @Test
    fun `get tasks count test`() {
        val month = 1
        val year = 2024
        val taskView = TaskView.ALL_TASK

        val taskCountResponse = listOf(
            TaskCountResponse(
                LocalDateTime.now().toLocalDate(),
                1,
                2,
                3
            )
        )

        every { taskService.listTasksCount(any(), authenticatedUser) } returns taskCountResponse

        every {
            taskService.getCompanyIdAndType(authenticatedUser)
        } returns Pair(1L, TaskCompanyType.CORPORATE)

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task/countByStatus")
                .param("month", month.toString())
                .param("year", year.toString())
                .param("taskView", taskView.name)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$[0].completed").value(1))
            .andExpect(jsonPath("$[0].inProgress").value(2))
            .andExpect(jsonPath("$[0].notStarted").value(3))
    }

    @Test
    fun `get task details test`() {
        val day = 1
        val month = 1
        val year = 2024
        val taskView = TaskView.ALL_TASK

        val taskDetailsList = listOf(taskDetails)

        every { taskService.getTaskDetails(any(), authenticatedUser) } returns taskDetailsList

        every {
            taskService.getCompanyIdAndType(authenticatedUser)
        } returns Pair(1L, TaskCompanyType.CORPORATE)

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task/details")
                .param("day", day.toString())
                .param("month", month.toString())
                .param("year", year.toString())
                .param("taskView", taskView.name)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
            .andExpect(jsonPath("$.payload[0].id").value(taskDetails.id))
    }

    @Test
    fun `retrieve task test`() {
        val taskId = 1L

        every { taskService.retrieve(taskId, authenticatedUser) } returns taskDetails

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/task/$taskId")
                .param("taskId",1.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.id").value(taskId))
    }

    @Test
    fun `update task test`() {
        val taskId = 1L
        val request = CreateTaskRequest(
            name = "Updated Task",
            description = "Updated Description",
            referenceId = 1,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = System.currentTimeMillis(),
            completedDate = null,
            priority = "HIGH",
            status = TaskStatus.NOT_STARTED,
            taskAssignee = listOf(),
            taskReminder = listOf(),
            usefulLinks = "https://test.com",
            instruction = "Updated instruction"
        )

        every { taskService.updateTask(taskId, request, authenticatedUser) } returns taskId

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/task/$taskId")
                .content(mapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(taskId))
    }

    @Test
    fun `update task status test`() {
        val taskId = 1L
        val request = TaskStatusUpdateRequest(TaskStatus.IN_PROGRESS)

        every { taskService.updateStatus(taskId, request.status, authenticatedUser) } returns true

        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/task/$taskId/status")
                .content(mapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `delete task test`() {
        val taskId = 1L

        every { taskService.delete(taskId, authenticatedUser) } returns true

        mockMvc.perform(
            delete("/api/${AppConstant.API_VERSION}/admin/task/$taskId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }
}