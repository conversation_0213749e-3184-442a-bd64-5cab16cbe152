package com.centuroglobal.controller
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.dashboard.CaseCountSummary
import com.centuroglobal.shared.data.payload.dashboard.CaseStatistics
import com.centuroglobal.shared.data.payload.dashboard.KeyValuePair
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.metrics.AccessLogService
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.case.CaseStatusHistoryView
import com.centuroglobal.shared.data.pojo.case.CountByCountry
import com.centuroglobal.shared.data.pojo.case.DashboardCaseSearchFilter
import com.centuroglobal.service.CaseDashboardService
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.task.dto.TaskDetails
import org.junit.jupiter.api.BeforeEach
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
@WebMvcTest(controllers = [CaseDashboardController::class])
@AutoConfigureMockMvc(addFilters = false)
class CaseDashboardControllerTest(@Autowired val mockMvc:MockMvc){
    @MockkBean
    lateinit var caseDashboardService:CaseDashboardService
    @MockkBean
    lateinit var accessLogService:AccessLogService
    private val authenticatedUser:AuthenticatedUser= mockk()
    private val mapper = jacksonObjectMapper()
    private val clientView:ClientView= mockk()
    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }
    @Test
    fun `get aggregate data test`()
    {
        val caseCountSummary =  CaseCountSummary(0,0,0,0)
        val keyValuePair = KeyValuePair("stage1","String")
        val caseStatistics = CaseStatistics(caseCountSummary,listOf(keyValuePair),listOf(keyValuePair))
        val accountId = "1"
        val userId = "1"
        every { authenticatedUser.principal } returns authenticatedUser
        every { caseDashboardService.generateAggregateCaseDataForUser(authenticatedUser,1,1) } returns caseStatistics
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/case/statistics")
            .param("accountId",accountId)
            .param("userId",userId)
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }
    @Test
    fun `get pending task test`()
    {
        val accountId = "1"
        val userId = "1"
        val taskDetails = TaskDetails(
            id = 1L,
            name = "ABC",
            description = "ABC",
            createdBy = null,
            referenceType = ReferenceType.OTHER,
            referenceId = 1L,
            visibility = TaskVisibility.PUBLIC,
            dueDate = 123456,
            overDueDays = 5,
            completedDate = 234567,
            priority = "YES",
            status = TaskStatus.IN_PROGRESS,
            category = listOf(),
            assignee = mutableListOf(),
            createdAt = 123456,
            reminders = listOf(),
            usefulLinks = "ABC",
            instruction = "PQR",
            progress = "ABC",
            caseMilestone = "ABC",
            plannedStartDate = 123456,
            startDate = 123456,
            expectedDueDate = 234567
        )
        val recentUpdate = RecentUpdate(
            "String",
            "String",
            "String",
            1L,
            1L)
        val pendingTaskResponse = PendingTasksResponse(mutableListOf(taskDetails),mutableListOf(recentUpdate))
        every { authenticatedUser.principal } returns authenticatedUser
        every { caseDashboardService.getPendingTasks(authenticatedUser,1,1) } returns pendingTaskResponse
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/case/pending-tasks")
            .param("accountId",accountId)
            .param("userId",userId)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }
    @Test
    fun `get counts per country test`() {
        val accountId = "1"
        val userId = "1"
        val category = "DEPENDENT_VISA"
        val countByCountry =CountByCountry(
            "US",
            1L,
            1L,
            1L,
            1L,
            1L)
        val dashboardCaseSearchFilter = DashboardCaseSearchFilter(
            "DEPENDENT_VISA",
            1,
            1,
            null,
            null,
            null,
            null)
        every { authenticatedUser.principal } returns authenticatedUser
        every { caseDashboardService.casesByCountry(authenticatedUser,dashboardCaseSearchFilter) } returns listOf(countByCountry)
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/case/counts-by-country")
            .param("accountId",accountId)
            .param("userId",userId)
            .param("category",category)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }
    @Test
    fun `get case counts by status test`() {
        val userProfile = UserProfile(
            1,
            "<EMAIL>",
            "String",
            "String",
            AccountStatus.ACTIVE,
            Role.ROLE_EXPERT,
            "String",
            "String",
            "String")
        val caseStatusHistoryView = CaseStatusHistoryView(
            1L,
            "String",
            null,
            userProfile,
            1L,
            "String",
            "String",
            "String",
            false,
            1L,
            "String")
        val accountId = "1"
        val userId = "1"
        val actionFor = "String"
        val category = "String"
        val country = "String"
        val status = "String"
        val pageIndex = "1"
        val pageSize = "6"
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = "false"
        val dashboardCaseSearchFilter = DashboardCaseSearchFilter(
            "String",
            null,
            1,
            null,
            "String",
            "String",
            caseId = null)
        val pageRequest = PageRequest.of(1,6,SearchConstant.SORT_ORDER(sortBy, sort))
        every { authenticatedUser.principal } returns authenticatedUser
        every { caseDashboardService.caseStatusHistory(authenticatedUser,dashboardCaseSearchFilter,"String",pageRequest) } returns PagedResult(listOf( caseStatusHistoryView),1L,1,2)
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/case/case-status-history")
            .param("accountId",accountId)
            .param("userId",userId)
            .param("category",category)
            .param("actionFor",actionFor)
            .param("country",country)
            .param("status",status)
            .param("pageIndex",pageIndex)
            .param("pageSize",pageSize)
            .param("sort",sort)
            .param("sortBy",sortBy)
            .param("isDownload",isDownload)
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }
}