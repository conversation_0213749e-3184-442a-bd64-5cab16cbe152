package com.centuroglobal.controller

import com.centuroglobal.data.payload.query.QueryRequest
import com.centuroglobal.service.QueryService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.query.ProposalCardDetails
import com.centuroglobal.shared.data.pojo.query.QueryCardDetails
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.io.OutputStream
import java.time.LocalDateTime


@WebMvcTest(controllers = [QueryController::class])
@AutoConfigureMockMvc(addFilters = false)
class QueryControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var queryService: QueryService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    private val mapper = jacksonObjectMapper()

    private val userProfile = CorporateUserProfile(
        2, "<EMAIL>", "firstName",
        "lastName", role = Role.ROLE_CORPORATE, status = AccountStatus.ACTIVE,
        countryCode = "AU",
        profilePictureFullUrl = "",
        companyName = "ABC",
        userType = PartnerCaseType.CG,
        bandName = "ABC"
    )

    private val queryCardDetails = QueryCardDetails(
        id = 1,
        heading = "Test Heading",
        description = "Test Description",
        category = listOf("Category1"),
        country = "IN",
        isProposalApproved = false,
        status = QueryStatus.OPEN.name,
        raisedBy = userProfile,
        submittedOn = TimeUtil.toEpochMillis(LocalDateTime.now())
    )

    private val queryProposalsCardDetails = ProposalCardDetails(
        proposalId = 1,
        fileUploadDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
        isApproved = false,
        fileName = "name",
        fileType = "PDF",
        fileSize = 12345,
        createdBy = "ABC"
    )

    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `create Query from controller`(){

        val queryRequest = QueryRequest(
            heading = "Test Heading",
            description = "Test Description",
            queryCategory = listOf("CATEGORY1"),
            assignedExperts = listOf(),
            country = "US"
        )

        every { authenticatedUser.principal } returns authenticatedUser
        every { queryService.createQuery(queryRequest, authenticatedUser) } returns 1

        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(post("/api/${AppConstant.API_VERSION}/queries")
            .content(mapper.writeValueAsString(queryRequest))
            .contentType(
            MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.payload").value(1))
    }
    @Test
    fun `get all queries from controller`() {
        val search = "Test"
        val account = "1"
        val user = "23"
        val responses = "TestResponses"
        val categories = "TestCategories"
        val country = "US"
        val archive = false
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val querySearchFilter = QuerySearchFilter.Builder.build(
            search,
            account,
            user,
            responses,
            categories,
            country,
            listOf(QueryStatus.OPEN),
            null,
            null,
            null,
            null,
            null,
            null
        )
        val pageRequest = PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
        val totalElements = 1L
        val queries = PagedResult(listOf(QueryCardDetails(
            id=1,
            heading = "Test Heading",
            description = "Test Description",
            category = listOf("Category1"),
            country = "IN",
            isProposalApproved = false,
            status = QueryStatus.OPEN.name,
            raisedBy = userProfile,
            submittedOn = TimeUtil.toEpochMillis(LocalDateTime.now())
        )), totalElements, 0, 1)


        every { queryService.listQueries(querySearchFilter, pageRequest, authenticatedUser) } returns queries


        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/queries")
                .param("search", search)
                .param("account", account)
                .param("user", user)
                .param("responses", responses)
                .param("categories", categories)
                .param("country", country)
                .param("archive", archive.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isMap)
            .andExpect(jsonPath("$.payload.rows").isNotEmpty)
            .andExpect(jsonPath("$.payload.totalElements").value(totalElements))
    }

    @Test
    fun `get query by id test`() {
        val queryId = 1L
        every { queryService.getQueryById(queryId, authenticatedUser) } returns queryCardDetails
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/queries/$queryId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isMap)
            .andExpect(jsonPath("$.payload.id").value(queryId))
            .andExpect(jsonPath("$.payload.heading").value(queryCardDetails.heading))
    }
    @Test
    fun `update query status test`() {
        val queryId = 1L
        val status = QueryStatus.RESOLVED
        every { queryService.updateStatus(queryId, status, authenticatedUser) } returns true
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/queries/$queryId")
                .param("status", status.name)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `approve query proposals test`() {
        val queryId = 1L
        every { queryService.approveQueryProposals(queryId, authenticatedUser) } returns true
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/queries/$queryId/approve")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }
    @Test
    fun `get query proposals test`() {
        val queryId = 1L

        every { queryService.getProposalsByQuery(queryId, authenticatedUser) } returns listOf (queryProposalsCardDetails)
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/queries/$queryId/proposals")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
            .andExpect(jsonPath("$.payload[0].proposalId").value(queryProposalsCardDetails.proposalId))
    }
    @Test
    fun `download query proposals test`() {
        val queryId = 1L
        val proposalId = 1L

        val fileBytes = "some content".toByteArray()
        val streamingResponseBody = StreamingResponseBody { objectStream: OutputStream ->
            val mockInputStream = ByteArrayInputStream(fileBytes)
            mockInputStream.use {
                val bytes = mockInputStream.readAllBytes()
                objectStream.write(bytes, 0, bytes.size)
            }
        }

        every { queryService.downloadProposal(queryId, proposalId, authenticatedUser) } returns ResponseEntity.ok(streamingResponseBody)
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/queries/$queryId/proposals/$proposalId/download")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
    }
    @Test
    fun `view query proposals test`() {
        val queryId = 1L
        val proposalId = 1L
        val url = "https://s3.proposal-url"

        every { queryService.viewProposalUrl(queryId, proposalId, authenticatedUser) } returns url
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/queries/$queryId/proposals/$proposalId/view")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(url))
    }
}