package com.centuroglobal.controller

import com.centuroglobal.data.payload.lead.CreateLeadRequest
import com.centuroglobal.data.payload.lead.UpdateLeadRequest
import com.centuroglobal.facade.LeadFacade
import com.centuroglobal.service.LeadService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [LeadController::class])
@AutoConfigureMockMvc(addFilters = false)
class LeadControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var leadFacade: LeadFacade

    @MockkBean
    private lateinit var leadService: LeadService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.isAuthenticated } returns true
        every { authenticatedUser.partnerId } returns 3
        every { authenticatedUser.name } returns "a"
    }

    @Test
    fun `test create lead`() {
        val request = CreateLeadRequest(
            title = "abc",
            description = "ample",
            expertiseId = listOf(1,2),
            countryCode = "IN",
            regionId = 4
        )
        val leadDetail = LeadDetail(
            id = "1",
            title = "Sample Lead",
            description = "Sample Description",
            expertiseId = listOf(),
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryCode = "IN",
            countryName = "India",
            regionId = 4,
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responses = listOf(),
            creator = Creator(
                userId = 4,
                displayName = "",
                profilePictureFullUrl = "",
                jobTitle = "",
                companyName = "",
                countryName = "",
                regionName = ""
            )
        )
        val deferredResult = DeferredResult<Response<LeadDetail>>()
        deferredResult.setResult(Response(true, leadDetail))

        every { leadFacade.create(any(), authenticatedUser) } returns CompletableFuture.completedFuture(leadDetail)

        mockMvc.perform(post("/api/v1/lead")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test update lead`() {
        val leadId = "lead123"
        val request = UpdateLeadRequest(
            title = "abc",
            description = "abc",
            expertiseId = listOf(),
            countryCode = "IN",
            regionId = 4,
            leadStatus = LeadStatus.ACTIVE
        )
        val leadDetail = LeadDetail(
            id = "1",
            title = "Sample Lead",
            description = "Sample Description",
            expertiseId = listOf(),
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryCode = "IN",
            countryName = "India",
            regionId = 4,
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responses = listOf(),
            creator = Creator(
                userId = 4,
                displayName = "",
                profilePictureFullUrl = "",
                jobTitle = "",
                companyName = "",
                countryName = "",
                regionName = ""
            )
        )
        val deferredResult = DeferredResult<Response<LeadDetail>>()
        deferredResult.setResult(Response(true, leadDetail))

        every { leadFacade.update(any(), any(),any()) } returns CompletableFuture.completedFuture(leadDetail)

        mockMvc.perform(put("/api/v1/lead/$leadId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test search leads`() {
        val leadType = "type"
        val leadTypeId = 1L
        val searchCriteria = LeadsSearchCriteria()
        val pagedResult = PagedResult(listOf(LeadSummary(
            id = "4",
            title = "",
            description = "",
            expertiseName = listOf(),
            countryName = "IN",
            regionName = "",
            status = LeadStatus.ACTIVE,
            createdDate = 1234,
            responseCount = 23
        )), 1, 0, 1)
        val deferredResult = DeferredResult<Response<PagedResult<LeadSummary>>>()
        deferredResult.setResult(Response(true, pagedResult))

        every { leadFacade.search(any(), any(), any(), any()) } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(get("/api/v1/lead/$leadType/$leadTypeId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(searchCriteria)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test retrieve lead`() {
        val leadId = "lead123"
        val leadDetail = LeadDetail(
            id = "1",
            title = "Sample Lead",
            description = "Sample Description",
            expertiseId = listOf(),
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryCode = "IN",
            countryName = "India",
            regionId = 4,
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responses = listOf(),
            creator = Creator(
                userId = 4,
                displayName = "",
                profilePictureFullUrl = "",
                jobTitle = "",
                companyName = "",
                countryName = "",
                regionName = ""
            )
        )
        val deferredResult = DeferredResult<Response<LeadDetail>>()
        deferredResult.setResult(Response(true, leadDetail))

        every { leadFacade.retrieve(any(),any()) } returns CompletableFuture.completedFuture(leadDetail)

        mockMvc.perform(get("/api/v1/lead/$leadId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test delete lead`() {
        val leadId = "lead123"
        val deferredResult = DeferredResult<Response<String>>()
        deferredResult.setResult(Response(true, "Lead deleted successfully"))

        every { leadFacade.delete(any(), any()) } returns CompletableFuture.completedFuture("")

        mockMvc.perform(delete("/api/v1/lead/$leadId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test recent leads`() {
        val userId = 1L
        val recentLeads = listOf(RecentLeadSummary(
            id = "4",
            title = "",
            description = "",
            expertiseName = listOf(),
            countryName = "",
            regionName = "",
            status = LeadStatus.ACTIVE,
            createdDate = 1234
        ))
        every { leadService.recentForCorporate(userId) } returns recentLeads

        mockMvc.perform(get("/api/v1/lead/$userId/recent")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

}
