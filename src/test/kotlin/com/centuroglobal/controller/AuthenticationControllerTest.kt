package com.centuroglobal.controller

import com.centuroglobal.data.payload.auth.ChangePasswordRequest
import com.centuroglobal.data.payload.auth.RecoverPasswordRequest
import com.centuroglobal.data.payload.auth.ResetPasswordRequest
import com.centuroglobal.facade.PasswordFacade
import com.centuroglobal.facade.TokenVerificationFacade
import com.centuroglobal.service.ApiAuthService
import com.centuroglobal.service.PasswordService
import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AuthenticationController::class])
@AutoConfigureMockMvc(addFilters = false)
class AuthenticationControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var passwordFacade: PasswordFacade

    @MockkBean
    private lateinit var passwordService: PasswordService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @MockkBean
    private lateinit var apiAuthService: ApiAuthService

    @MockkBean
    private lateinit var tokenVerificationService: TokenVerificationService

    @MockkBean
    private lateinit var tokenVerificationFacade: TokenVerificationFacade


    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
    }

    @Test
    fun `test recoverPassword`() {
        val request = RecoverPasswordRequest(email = "<EMAIL>")
        val deferredResult = DeferredResult<Response<String>>()
        deferredResult.setResult(Response(true, "Password recovery email sent"))

        every { passwordFacade.recoverPassword(request) } returns CompletableFuture.completedFuture("abcd")

        mockMvc.perform(post("/api/v1/auth/recover-password")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test resetPassword`() {
        val request = ResetPasswordRequest(
            code = "1234",
            password = "abcs"
        )
        val tokenResult = TokenResult(
            tokenType = "type",
            refreshToken = "",
            accessToken = "",
            expiresIn = 3,
            scope = "",
            onboard = false,
            lastLoginDate = 1234,
            validationToken = "",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "",
            bandName = "",
            profilePhotoUrl = "",
            aiMessageCount = 123,
            userRoles = listOf(),
            loginToken = "",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = false,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "",
            isPartnerUser = true
        )
        val deferredResult = DeferredResult<Response<TokenResult>>()
        deferredResult.setResult(Response(true, tokenResult))

        every { passwordFacade.resetPassword(request) } returns CompletableFuture.completedFuture(tokenResult)

        mockMvc.perform(post("/api/v1/auth/reset-password")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test changePassword`() {
        val request = ChangePasswordRequest(
            oldPassword = "old",
            newPassword = "new"
        )
        val deferredResult = DeferredResult<Response<String>>()
        deferredResult.setResult(Response(true, "Password changed successfully"))

        every { passwordFacade.changePassword(any(), any()) } returns CompletableFuture.completedFuture("abcd")

        mockMvc.perform(post("/api/v1/auth/change-password")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test logout`() {
        every { passwordService.logout(any()) } returns true

        mockMvc.perform(post("/api/v1/auth/logout"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `test validateToken`() {
        val userId = 1L
        val type = "USER"
        val validationResult = mapOf("isValid" to true)

        every { apiAuthService.validateUser(userId, authenticatedUser, type, "USERS") } returns validationResult

        mockMvc.perform(get("/api/v1/auth/validate")
            .param("userId", userId.toString())
            .param("type", type))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.isValid").value(true))
    }

    @Test
    fun `test approveLoginAccountUuid`() {
        val uuid = "test-uuid"
        val deferredResult = DeferredResult<Response<Boolean>>()
        deferredResult.setResult(Response(true, true))

        every { passwordFacade.loginAccountUuidApproval(uuid) } returns CompletableFuture.completedFuture(true)

        mockMvc.perform(put("/api/v1/auth/approve/$uuid"))
            .andExpect(status().isOk)
    }

}
