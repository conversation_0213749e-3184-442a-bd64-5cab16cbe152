package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.MasterContentEntity
import com.centuroglobal.shared.data.entity.MasterDataEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.TermsAndCondition
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Value
import java.time.LocalDateTime
import java.util.*

class TermsAndConditionServiceTest {

    private lateinit var termsAndConditionService: TermsAndConditionService
    private val awsS3Service = mockk<AwsS3Service>()
    private val mailSendingService = mockk<MailSendingService>()
    private val masterContentRepository = mockk<MasterContentRepository>()
    private val loginAccountRepository = mockk<LoginAccountRepository>()
    private val masterDataRepository = mockk<MasterDataRepository>()
    private val expertUserRepository = mockk<ExpertUserRepository>()
    private val expertCompanyProfileRepository = mockk<ExpertCompanyProfileRepository>()
    private val tokenVerificationService = mockk<TokenVerificationService>()

    @Value("\${spring.mail.renewal-contract-acceptance.to}")
    private val adminEmail: String = "<EMAIL>"

    @BeforeEach
    fun setUp() {
        termsAndConditionService = TermsAndConditionService(
            adminEmail,
            awsS3Service,
            mailSendingService,
            masterContentRepository,
            loginAccountRepository,
            masterDataRepository,
            expertUserRepository,
            expertCompanyProfileRepository,
            tokenVerificationService
        )
    }

    @Test
    fun `retrieveTermsAndCondition should return terms and conditions map for corporate user`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)
        val masterData = mockk<MasterDataEntity>()

        every { authenticatedUser.email } returns "<EMAIL>"
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { loginAccountRepository.findByEmail(any()) } returns loginAccount
        every { loginAccount.getUserType() } returns UserType.CORPORATE
        every { masterDataRepository.findByDocType(any()) } returns masterData
        every { masterData.lastUploadDate } returns LocalDateTime.now().minusDays(1)
        every { loginAccount.lastTermsViewDate } returns LocalDateTime.now().minusDays(2)

        val result = termsAndConditionService.retrieveTermsAndCondition(authenticatedUser)

        assertNotNull(result)
        assertTrue(result["showTNC"] as Boolean)
        assertFalse(result["showContract"] as Boolean)
        assertFalse(result["blockAccess"] as Boolean)
        assertFalse(result["renewContract"] as Boolean)
        assertEquals(UserType.CORPORATE.name, result["userType"])
    }

    @Test
    fun `update should update terms and conditions acceptance`() {
        val email = "<EMAIL>"
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)
        val expertUser = mockk<ExpertUserEntity>(relaxed = true)
        val companyProfile = mockk<ExpertCompanyProfile>(relaxed = true)

        every { loginAccountRepository.findByEmail(email) } returns loginAccount
        every { expertUserRepository.findById(any()) } returns Optional.of(expertUser)
        every { expertCompanyProfileRepository.saveAndFlush(any()) } returns mockk()
        every { loginAccountRepository.save(any()) } returns loginAccount
        //every { expertUser.companyProfile } returns companyProfile
        every { companyProfile.renewContract } returns true

        termsAndConditionService.update(email, tncAccepted = true, renewAccepted = true, contractAccepted = true)

        //verify { companyProfile.renewContract = false }
        verify { loginAccount.tncView = true }
        verify { loginAccountRepository.save(loginAccount) }
    }

    @Test
    fun `retrieveTermsAndConditionData should return terms and condition for authenticated user`() {
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccount = mockk<LoginAccountEntity>()
        val termsAndCondition = TermsAndCondition(termsAndCondition = "content")

        every { authenticatedUser.email } returns "<EMAIL>"
        every { loginAccountRepository.findByEmail(any()) } returns loginAccount
        every { termsAndConditionService.retrieveTnCData(loginAccount) } returns termsAndCondition
        every { loginAccount.getUserType() } returns UserType.CORPORATE
        every { masterContentRepository.findByDocType(any()) } returns MasterContentEntity(
            docType  = "pdf",
            content = "content",
            lastUploadDate = LocalDateTime.now(),
            lastUpdateBy = 2
        )

        val result = termsAndConditionService.retrieveTermsAndConditionData(authenticatedUser)

        assertNotNull(result)
        assertEquals(termsAndCondition, result)
    }

    @Test
    fun `retrieveTermsAndConditionData should return terms and condition for code`() {
        val code = "someCode"
        val loginAccount = mockk<LoginAccountEntity>()
        val termsAndCondition = TermsAndCondition(termsAndCondition = "content")

        every { tokenVerificationService.getUserFromToken(code) } returns loginAccount
        every { termsAndConditionService.retrieveTnCData(loginAccount) } returns termsAndCondition

        every { loginAccount.getUserType() } returns UserType.CORPORATE
        every { masterContentRepository.findByDocType(any()) } returns MasterContentEntity(
            docType  = "pdf",
            content = "content",
            lastUploadDate = LocalDateTime.now(),
            lastUpdateBy = 2
        )

        val result = termsAndConditionService.retrieveTermsAndConditionData(code)

        assertNotNull(result)
        assertEquals(termsAndCondition, result)
    }

    @Test
    fun `retrieveTnCData should return terms and condition for corporate user`() {
        val loginAccount = mockk<LoginAccountEntity>()
        val masterData = mockk<MasterDataEntity>()
        val termsAndCondition = TermsAndCondition(termsAndCondition = "content")

        every { loginAccount.getUserType() } returns UserType.CORPORATE
        every { masterContentRepository.findByDocType(any()) } returns MasterContentEntity(
            docType  = "pdf",
            content = "content",
            lastUploadDate = LocalDateTime.now(),
            lastUpdateBy = 2
        )

        val result = termsAndConditionService.retrieveTnCData(loginAccount)

        assertNotNull(result)
        assertEquals(termsAndCondition, result)
    }
}
