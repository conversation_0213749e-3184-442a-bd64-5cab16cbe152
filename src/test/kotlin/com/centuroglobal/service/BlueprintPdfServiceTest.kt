package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.BlueprintPdfEntity
import com.centuroglobal.shared.data.enums.BlueprintActionStatus
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.CountryRegion
import com.centuroglobal.shared.repository.BlueprintPdfRepository
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import javax.print.DocFlavor.INPUT_STREAM

class BlueprintPdfServiceTest{

    private val blueprintPdfRepository:BlueprintPdfRepository = mockk()
    private val blueprintAuditService: BlueprintAuditService = mockk()
    private val countryService:CountryService = mockk()
    private val s3Service:AwsS3Service = mockk()
    private val serverUrl = "server-url"
    private val bluePrintFolder = "folder"

    private val blueprintPdfService = BlueprintPdfService(
        serverUrl,
        bluePrintFolder,
        blueprintPdfRepository,
        blueprintAuditService,
        countryService,
        s3Service)

    private val countryCode = "US"

    private val countryRegion=CountryRegion(
        1,
        "US",
        "007"
    )
    private val country=Country(
        "US",
        "US",
        "007",
        listOf(countryRegion)
    )
    private val stepName=StepName.STEP_1

    private val blueprintPdfEntity = BlueprintPdfEntity(
        1L,
        "1",
        stepName,
        true,
        1L)

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

//    @Test
//    fun validateBlueprintPdfTest(){
//
//        every { countryService.retrieveByCountryCode(countryCode) } returns country
//        every { blueprintPdfRepository.findByBlueprintIdAndStepNameAndUploaded(countryCode,stepName ,true) } returns blueprintPdfEntity
//
//        val result = blueprintPdfService.validateBlueprintPdf(countryCode,stepName)
//        assertNotNull(result)
//    }

    @Test
    fun listBlueprintStatusTest(){

        every { countryService.retrieveByCountryCode(countryCode) } returns country
        every { blueprintPdfRepository.findAllByBlueprintIdAndUploaded(countryCode,true) } returns listOf(blueprintPdfEntity)

        val result = blueprintPdfService.listBlueprintStatus(countryCode)
        assertNotNull(result)
    }

//@Test
//fun deleteBlueprintTest(){
//
//    every { countryService.retrieveByCountryCode(countryCode) } returns country
//    every { blueprintPdfRepository.findByBlueprintIdAndStepNameAndUploaded(countryCode,stepName,true) } returns blueprintPdfEntity
//    every { blueprintPdfRepository.save(any()) } returns blueprintPdfEntity
//    every { s3Service.deleteFile("")  } returns Unit
//    every { blueprintAuditService.log(countryCode,1,BlueprintActionStatus.ACTIVE) }
//
//
//    val result = blueprintPdfService.deleteBlueprint(countryCode,stepName,1)
//    assertNotNull(result)
//}
}