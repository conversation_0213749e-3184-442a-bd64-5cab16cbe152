package com.centuroglobal.service

import com.centuroglobal.repository.DependentVisaRepository
import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.costofliving.data.entity.CountryEntity
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.*
import com.centuroglobal.shared.data.entity.view.CaseView
import com.centuroglobal.shared.data.entity.view.CaseViewForResidenceCountry
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.case.ApproverDetailsRequest
import com.centuroglobal.shared.data.payload.case.GenericCaseRequest
import com.centuroglobal.shared.data.payload.case.UpdateCaseRequest
import com.centuroglobal.shared.data.pojo.CaseReferenceData
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.case.CaseSearchFilter
import com.centuroglobal.shared.data.pojo.case.CaseViewDetails
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.case.CaseFormRepository
import com.centuroglobal.shared.repository.view.CaseViewRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.repository.view.UserViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.CaseUtil
import com.centuroglobal.shared.util.PartnerEmailUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.json.JSONObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.test.util.ReflectionTestUtils
import java.time.LocalDateTime
import java.util.*


class CaseServiceTest {

    private val cleanupAfterSentInDays:Int=10
    private val partnerRepository: PartnerRepository = mockk()
    private val caseAssigneeRepository: CaseAssigneeRepository= mockk()
    private val caseManagerRepository: CaseManagerRepository= mockk()
    private val milestonesRepository:MilestonesRepository=mockk()
    private val caseMilestonesRepository:CaseMilestonesRepository=mockk()
    private val caseStatusHistoryRepository:CaseStatusHistoryRepository=mockk()
    private val caseRepository:CaseRepository=mockk()
    private val directorRepository:DirectorRepository=mockk()
    private val travellerRepository:TravellerRepository=mockk()
    private val applicantInfoRepository:ApplicantInfoRepository=mockk()
    private val shareholderRepository:ShareholderRepository=mockk()
    private val caseCategoryRepository:CaseCategoryRepository=mockk()
    private val clientViewRepository:ClientViewRepository=mockk()
    private val caseViewRepository:CaseViewRepository=mockk()
    private val sicCodeRepository:SICCodeRepository=mockk()
    private val expertCompanyProfileRepository:ExpertCompanyProfileRepository=mockk()
    private val caseDocumentsRepository:CaseDocumentsRepository=mockk()
    private val loginAccountRepository:LoginAccountRepository=mockk()
    private val expertUserRepository:ExpertUserRepository=mockk()
    private val validationTokenRepository:ValidationTokenRepository=mockk()
    private val adminUserService:AdminUserService=mockk()
    private val corporateService:CorporateService=mockk()
    private val userViewRepository:UserViewRepository=mockk()
    private val caseFeesApprovalHistoryRepository:CaseFeesApprovalHistoryRepository=mockk()
    private val countryRepository:CountryRepository=mockk()
    private val caseStatusMasterRepository:CaseStatusMasterRepository=mockk()
    private val corporateUserRepository:CorporateUserRepository=mockk()
    private val caseDashboardService:CaseDashboardService=mockk()
    private val accountEntityRepository:AccountEntityRepository=mockk()
    private val caseDocumentsStatusLogsRepository:CaseDocumentsStatusLogsRepository=mockk()
    private val caseDocumentsAuditRepository:CaseDocumentsAuditRepository=mockk()
    private val docRepoService:DocRepoService=mockk()
    private val caseUtil=CaseUtil(corporateUserRepository,expertUserRepository, partnerRepository)
    private val groupChatService:GroupChatService=mockk()
    private val corporateUserService:CorporateUserService=mockk()
    private val clientView:ClientView=mockk()
    private val authenticatedUser:AuthenticatedUser=mockk()
    private val corporateUserEntity:CorporateUserEntity=mockk()
    private val corporateEntity:CorporateEntity=mockk()

    private val mailSendingService:MailSendingService=mockk()
    private val awsS3Service:AwsS3Service=mockk()
    private val singleVisaRepository:SingleVisaRepository=mockk()
    private val rightToWorkCheckRepository:RightToWorkCheckRepository=mockk()
    private val dependentVisaRepository:DependentVisaRepository=mockk()
    private val expertUserService:ExpertUserService=mockk()
    private val partnerService: PartnerService= mockk()
    private val groupChatRepository: GroupChatRepository = mockk()
    private val partnerEmailUtil:PartnerEmailUtil = mockk()
    private val groupChatParticipantRepository : GroupChatParticipantRepository = mockk()
    private val reminderRepository:ReminderRepository = mockk()
    private val caseNotesRepository:CaseNotesRepository = mockk()
    private val employeeOfRecordApplicantRepository:EmployeeOfRecordApplicantRepository = mockk()
    private val travelAssessmentService:TravelAssessmentService = mockk()
    private val taskWorkflowService:TaskWorkflowService = mockk()
    private val userProfileUtil:UserProfileUtil = mockk()
    private val expertUserRepo: ExpertUserRepository= mockk()
    private val loginAccountEntity:LoginAccountEntity=mockk()
    private val caseFormRepository: CaseFormRepository= mockk()


    private val caseEmailService=CaseEmailService(
        "http://localhost","<EMAIL>",listOf(), listOf(),
        caseRepository,mailSendingService,
        awsS3Service,
        loginAccountRepository,
        validationTokenRepository,
        corporateUserRepository,
        singleVisaRepository,
        rightToWorkCheckRepository,
        dependentVisaRepository,
        partnerRepository,
        partnerEmailUtil
    )


    private val caseEntity:CaseEntity=mockk()

    private val caseService=CaseService(
        milestonesRepository,
        caseMilestonesRepository,
        caseStatusHistoryRepository,
        caseRepository,
        directorRepository,
        travellerRepository,
        applicantInfoRepository,
        shareholderRepository,
        caseCategoryRepository,
        clientViewRepository,
        caseViewRepository,
        sicCodeRepository,
        expertCompanyProfileRepository,
        caseDocumentsRepository,
        loginAccountRepository,
        expertUserRepository,
        validationTokenRepository,
        adminUserService,
        corporateService,
        userViewRepository,
        caseFeesApprovalHistoryRepository,
        countryRepository,
        caseStatusMasterRepository,
        corporateUserRepository,
        accountEntityRepository,
        caseEmailService,
        caseDocumentsStatusLogsRepository,
        caseDocumentsAuditRepository,
        docRepoService,
        groupChatService,
        corporateUserService,
        expertUserService,
        groupChatParticipantRepository,
        partnerRepository,
        caseAssigneeRepository,
        caseManagerRepository,
        reminderRepository = reminderRepository,
        caseNotesRepository = caseNotesRepository,
        employeeOfRecordApplicantRepository = employeeOfRecordApplicantRepository,
        travelAssessmentService = travelAssessmentService,
        taskWorkflowService = taskWorkflowService,
        userProfileUtil = userProfileUtil,
        caseFormRepository = caseFormRepository
    )

    private val caseCategoryEntity1=CaseCategoryEntity(
        1,
        "1",
        "SINGLE_VISA",
        "1",
        "IMMIGRATION_GM"
    )

    private val caseCategoryEntity2=CaseCategoryEntity(
        2,
        "2",
        "DEPENDENT_VISA",
        "1",
        "IMMIGRATION_GM"
    )

    val accountEntity=AccountEntity(
        1,
        "Name",
        AccountStatus.ACTIVE,
        "Description",
        "cName",
        corporateEntity,
        listOf(corporateUserEntity),
        null
    )

    private val userProfile = UserProfile(
        id = 3,
        email = "<EMAIL>",
        firstName = "",
        lastName = "",
        status = AccountStatus.ACTIVE,
        role = Role.ROLE_USER,
        countryCode = "IN",
        profilePictureFullUrl = "",
        companyName = "",
        userType = PartnerCaseType.CG
    )

    val caseManagerEntity = CaseManagerEntity(
        id = 1,
        case = caseEntity,
        user = loginAccountEntity,
        userType = PartnerCaseType.CG
    )

    private val signUpRequest=SignUpRequest(
        "<EMAIL>",
        "String",
        "String",
        null,
        "Corporate_1",
        "IN",
        null,
        false,
        "recaptchaResponse"
    )

    val validationTokenEntity=ValidationTokenEntity(
        1,
        1,
        ValidationType.CORPORATE_SIGNUP,
        "code",
        ValidationState.CREATED,
        LocalDateTime.now()
    )

    val dependentVisaEntity:DependentVisaEntity= mockk()

    val expertUserEntity = ExpertUserEntity(
        id = 1,
        jobTitle = "SDE",
        countryRegionId = 2,
        expertiseId = 3,
        expertises = listOf(),
        bio = "",
        displayName = "",
        infoVideoUrl = "",
        contactNumber = "123456",
        contactEmail = "<EMAIL>",
        contactWebsite = "a.com",
        companyProfile = ExpertCompanyProfileEntity(
            id = 5,
            name = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            users = mutableListOf(),
            lastUpdatedBy = 1,
            companyNumber = "123",
            companyAddress = "",
            aboutBusiness = "",
            effectiveDate = LocalDateTime.now(),
            effectiveEndDate = LocalDateTime.now(),
            contractAcceptedDate = LocalDateTime.now(),
            feesCurrency = "",
            feesAmount = "",
            specialTerms = "",
            membershipStatus = "",
            territory = "",
            renewContract = false,
            services = "",
            account = AccountEntity(
                id = 6,
                name = "",
                status = AccountStatus.ACTIVE,
                description = "",
                companyName = "",
                corporate = null,
                corporateUsers = listOf(),
                cases = mutableListOf()
            ),
            profileImage = "",
            expertContract =  null,
            status = AccountStatus.ACTIVE,
            questionsQuota = 13,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 6
        ),
        expertType = "",
        viewContract = false,
        case = mutableListOf(),
        profileImage = ""
    )

    private var mapper=ObjectMapper()

    private val visibilities=listOf(UserAccess("USER",mutableListOf("FULL","REPORTEES","OWN")))
    private val accesses=listOf(UserAccess("CASE",mutableListOf("VIEW_FEE","VIEW_FEE_APPROVED")))

    @BeforeEach
    fun setup() {
        every {authenticatedUser.userId} returns LOGGED_IN_USER_ID
        every {authenticatedUser.companyId} returns LOGGED_IN_USER_CORPORATE_ID
        every {authenticatedUser.email} returns LOGGED_IN_USER_EMAIL
        every { authenticatedUser.userType } returns UserType.EXPERT.toString()
        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name

        every {corporateUserEntity.id} returns LOGGED_IN_USER_ID
        every {corporateUserEntity.corporate} returns corporateEntity
        every {corporateEntity.name} returns "corporateName"
        every {corporateEntity.id} returns LOGGED_IN_USER_CORPORATE_ID
        every {corporateUserRepository.findById(LOGGED_IN_USER_ID)} returns Optional.of(corporateUserEntity)

        every {authenticatedUser.visibilities} returns visibilities
        every {authenticatedUser.accesses} returns accesses
        every {awsS3Service.getS3PublicUrl(any())} returns "https://s3.aws.com"

        every {mailSendingService.sendEmail(any())} returns true
        every {docRepoService.createCaseDocument(any(),any<Array<String>>(),any<Long>())} returns Unit

        every { expertUserRepo.findById(any()) } returns Optional.of(expertUserEntity)

        every {accountEntityRepository.getReferenceById(any())} returns accountEntity

        every { partnerRepository.findById(any()) } returns Optional.of(PartnerEntity(
            id = 6,
            name = "",
            createdFrom = PartnerType.NEW,
            contractFromDate = LocalDateTime.now(),
            contractToDate = LocalDateTime.now(),
            casesManaged = PartnerCaseType.CG,
            queriesManaged = PartnerCaseType.CG,
            companyLogo = "",
            themePrimaryColor = "",
            themeSecondaryColor = "",
            rootUserId = 6,
            status = CorporateStatus.ACTIVE,
            corporates = listOf(),
            partnerUsers = mutableListOf(),
            country = "IN",
            band = null,
            referenceId = 4,
            associatedCompanies = mutableListOf(),
            corporateAccess = ""
        ))

        every { caseRepository.getReferenceById(any()) } returns mockk()

        every { loginAccountEntity.id } returns LOGGED_IN_USER_ID

        every {loginAccountRepository.findById(LOGGED_IN_USER_ID)} returns Optional.of(corporateUserEntity)

        every { dependentVisaEntity.partnerId } returns 4

        every { corporateEntity.isTeamEmail } returns true

        every { caseRepository.findByIdForExpert(any(), any(), any()) } returns Optional.of(caseEntity)

        every { authenticatedUser.partnerId } returns 2
        every { partnerService.getPartnerId(any()) } returns 4
        ReflectionTestUtils.setField(caseService, "expertUserRepo", expertUserRepo)
        ReflectionTestUtils.setField(caseService, "corporateUserRepo", corporateUserRepository)

        every { caseEntity.caseForm } returns CaseFormEntity(
            id = 1,
            name = "name",
            description = "",
            countries = mutableListOf(),
            category = "category",
            fields = "",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = null,
            fieldCount = 12,
            defaultDocuments = ""
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    private fun getDependentVisaPayloadForSingleVisa():JSONObject {
        val payload=getDependentVisaPayload(1).getJSONArray("dependents").getJSONObject(0)
        payload
            .put("travelCountry","US")
            .put("travelPurpose","WORK")
        return payload
    }

    private fun getDependentVisaPayload(noOfDependents:Int):JSONObject {

        val dependent=JSONObject()
            .put("firstName","fName")
            .put("middleName","mName")
            .put("lastName","lName")
            .put("relationWithPrimary","relation")
            .put("childAge","5")
            .put("nationality","American")
            .put("residenceCountry","US")
            .put("contactNo","001")
            .put("emailAddress","<EMAIL>")
            .put("shareApplicantInfo",true)

        return JSONObject()
            .put("travelCountry","US")
            .put("travelPurpose","WORK")
            .put("applicantStayType","Months")
            .put("applicantStay",1)
            .put("dependents",(1..noOfDependents).map {dependent})

    }

    private fun getSingleVisaPayload():JSONObject {

        return JSONObject()
            .put("visaType","string")
            .put("nationality","string")
            .put("caseCountry","string")
            .put("fromCountry","string")
            .put("tripPurpose","string")
            .put("foreName","string")
            .put("middleName","string")
            .put("surname","string")
            .put("covidVaccinated","string")
            .put("dependantTravelling","string")
            .put("applicantStay",50)
            .put("estimatedSalary",500000)
            .put("paidCountry","string")
            .put("entityInHostCountry",false)
            .put("duties","string")
            .put("qualification","string")
            .put("applicantStayType","string")
            .put("currencySymbol","string")
            .put("accountName","string")
            .put("shareApplicantInfo",false)
            .put("contactNo","string")
            .put("emailAddress","string")
            .put("visaIssueDate",*************)
            .put("visaExpiryDate",*************)
            .put("accountName","Test")
            .put("caseCountry","IN")
            .put("contactNo","445454")
            .put("emailAddress","<EMAIL>")
    }

    private fun getAdditionalDataPayload():JSONObject {

        return JSONObject()
            .put("accountId",3)
            .put("caseOwner",authenticatedUser.userId)
            .put("parentCategoryId","IMMIGRATION_GM")
            .put("assessmentId",4)
            .put("notifyCaseOwner", false)
            .put(
                "personalDetails",JSONObject()
                    .put("firstName","String")
                    .put("lastName","String")
                    .put("email","<EMAIL>")
                    .put("country","IN")
            )

    }

    @Test
    fun `createCase test`() {

        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val caseId=15L

        val caseEntityData=JSONObject()
            .put("visaType","string")
            .put("nationality","string")
            .put("caseCountry","string")
            .put("fromCountry","string")
            .put("tripPurpose","string")
            .put("foreName","string")
            .put("middleName","string")
            .put("surname","string")
            .put("covidVaccinated","string")
            .put("dependantTravelling","string")
            .put("applicantStay",50)
            .put("estimatedSalary",500000)
            .put("paidCountry","string")
            .put("entityInHostCountry",false)
            .put("duties","string")
            .put("qualification","string")
            .put("applicantStayType","string")
            .put("currencySymbol","string")
            .put("accountName","string")
            .put("shareApplicantInfo",false)
            .put("contactNo","string")
            .put("emailAddress","string")
            .put("visaIssueDate",*************)
            .put("visaExpiryDate",*************)
            .put("accountName","Test")
            .put("caseCountry","IN")
            .put("contactNo","445454")
            .put("emailAddress","<EMAIL>")

        val additionalData=JSONObject()
            .put("accountId",3)
            .put("caseOwner",authenticatedUser.userId)
            .put("parentCategoryId","IMMIGRATION_GM")
            .put("assessmentId",4)
            .put("notifyCaseOwner", false)
            .put(
                "personalDetails",JSONObject()
                    .put("firstName","String")
                    .put("lastName","String")
                    .put("email","<EMAIL>")
                    .put("country","IN")
            )

        val userData=JSONObject()
            .put("firstName","String")
            .put("lastName","String")
            .put("email","<EMAIL>")
            .put("country","IN")
            .put("corporateName","Corporate_1")

        val caseRequest=GenericCaseRequest(
            caseEntityData=mapper.readTree(caseEntityData.toString()),
            additionalData=mapper.readTree(additionalData.toString()),
            userData=mapper.readTree(userData.toString())
        )

        val caseStatusMaster=mockk<CaseStatusMasterEntity>()
        val caseEntity=mockk<CaseEntity>()
        val createdBy=mockk<ClientView>()

        val caseCategoryEntity=CaseCategoryEntity(12L,caseCategory,caseCategory,parentCategoryId,parentCategoryId)

        every {caseStatusMaster.actionFor} returns "Centuro"
        every {caseEntity.id} returns caseId
        every {caseEntity.createdBy} returns createdBy
        every {caseEntity.category} returns caseCategoryEntity
        every { caseEntity.partnerId } returns  4
        every {createdBy.userId} returns authenticatedUser.userId
        every {createdBy.userType} returns UserType.CORPORATE
        every {createdBy.fullName} returns "full name"
        every {createdBy.email} returns "<EMAIL>"
        every { createdBy.company } returns "abc"
        every { clientView.company } returns "abc"

        every { caseEntity.caseForm } returns CaseFormEntity(
            id = 1,
            name = "name",
            description = "",
            countries = mutableListOf(),
            category = "category",
            fields = "",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = null,
            fieldCount = 12,
            defaultDocuments = ""
        )

        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory,
                parentCategoryId
            )
        } returns mockk()
        every {clientViewRepository.getReferenceById(LOGGED_IN_USER_ID)} returns mockk()
        every {caseStatusMasterRepository.findBySubCategoryAndStatus(caseCategory,any())} returns caseStatusMaster
        every {accountEntityRepository.findById(any())} returns Optional.of(mockk())
        every {caseRepository.save(any())} returns caseEntity
        every {corporateEntity.users} returns listOf()
        every {caseRepository.findById(caseId)} returns Optional.of(caseEntity)
        every {caseDocumentsRepository.existsByCase(caseEntity)} returns true
        every {corporateUserService.getUserManagers(LOGGED_IN_USER_ID)} returns listOf()
        every {groupChatService.createGroupChat(
            ChatType.CASE, caseId, any<List<Long>>(),
            virtualParticipants = listOf()
        )} returns mockk()

        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()


        every { accountEntityRepository.getReferenceById(any()) } returns accountEntity
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { caseRepository.getReferenceById(any()) } returns mockk()
        every { caseDocumentsRepository.existsByCase(any()) } returns false
        every { caseManagerRepository.findAllByCase(any()) } returns mutableListOf(
            CaseManagerEntity(
                id = 1,
                case = mockk<CaseEntity>(),
                user = loginAccountEntity,
                userType = PartnerCaseType.CG
            )
        )
        every { partnerRepository.findById(any()) } returns Optional.of(
            PartnerEntity(
                id = 1,
                name = "",
                createdFrom = PartnerType.NEW,
                contractFromDate = LocalDateTime.now(),
                contractToDate =  LocalDateTime.now(),
                casesManaged = PartnerCaseType.CG,
                queriesManaged = PartnerCaseType.CG,
                companyLogo = "",
                themePrimaryColor = "",
                themeSecondaryColor = "",
                rootUserId = 1,
                status = CorporateStatus.ACTIVE,
                corporates = listOf(),
                partnerUsers = mutableListOf(),
                country = "",
                band = BandsEntity(
                    id = 4,
                    name = "",
                    description = "",
                    status = BandStatus.ACTIVE,
                    color = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    bandAccesses = mutableListOf()
                ),
                referenceId = 5,
                associatedCompanies = mutableListOf(),
                corporateAccess = ""
            )
        )

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        every { caseManagerRepository.findAllByCase(any()) } returns mutableListOf(caseManagerEntity)
        every { caseManagerRepository.delete(any()) } returns Unit
        every { caseManagerRepository.saveAll(mutableListOf()) } returns mutableListOf()

        every { groupChatService.updateChatParticipants(any(), any(), any(), any() ) } returns Unit
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()

        val corporateTeamEntity = CorporateTeamEntity(
            id = 4,
            designation = "abc",
            user = loginAccountEntity,
            corporate = corporateEntity
        )
        every { corporateEntity.team } returns mutableListOf(corporateTeamEntity)
        every { loginAccountEntity.email } returns "<EMAIL>"
        every { caseEntity.country } returns  "IN"

        val groupChatEntity: GroupChatEntity = mockk()
        every{ groupChatService.createGroupChat(
            any(), any(), any(), any()
        ) } returns groupChatEntity

        val response=caseService.createCase(caseCategory,caseRequest,authenticatedUser)

        assertNotNull(response)
        assertNotNull(response.first)
        assertEquals(caseId,response.first[0])
        //assertTrue(response.second)
    }

    @Test
    fun `get case details test`() {
        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val caseId=14L

        val caseEntity=mockk<CaseEntity>()
        val createdBy=mockk<ClientView>()

        val caseCategoryEntity=CaseCategoryEntity(12L,caseCategory,caseCategory,parentCategoryId,parentCategoryId)

        every {caseEntity.id} returns caseId
        every {caseEntity.createdBy} returns createdBy
        every {caseEntity.category} returns caseCategoryEntity
        every {createdBy.userType} returns UserType.CORPORATE
        every {createdBy.userId} returns authenticatedUser.userId
        every { caseRepository.findByIdForExpert(any(), any(), any()) } returns Optional.of(caseEntity)
        every {caseRepository.findById(caseId)} returns Optional.of(caseEntity)


        val response=caseService.getDetails(caseId,authenticatedUser)
        assertNotNull(response)
        assertEquals(caseId,response.id)
        assertNotNull(response.category)
        assertEquals(caseCategory,response.category?.subCategoryName)
    }

    @Test
    fun `get case documents test`() {

        val caseEntity=mockk<CaseEntity>()
        val caseDocuments=(1..5).map {mockk<CaseDocumentsEntity>()}
        caseDocuments.forEachIndexed {index,caseDocumentsEntity-> every {caseDocumentsEntity.id} returns index.toLong()}
        every {caseDocumentsRepository.findAllByCase(caseEntity,any())} returns caseDocuments.toMutableList()

        val response=caseService.getCaseDocuments(caseEntity)
        assertNotNull(response)
        assertTrue(response.isNotEmpty())
        response.forEachIndexed {index,it->
            assertNotNull(it.id)
            assertEquals(index.toLong(),it.id)
        }
    }

    @Test
    fun `get case milestones test`() {
        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"

        val caseCategoryEntity=CaseCategoryEntity(12L,caseCategory,caseCategory,parentCategoryId,parentCategoryId)

        val caseEntity=mockk<CaseEntity>()
        val caseMilestones=(1..5).map {CaseMilestonesEntity(it.toLong(),caseEntity,"milestone-$it",0)}
        val milestones=(1..10).map {
            MilestonesEntity(
                it.toLong(),
                it.toLong()-1,
                "milestone-$it",
                "milestone-$it",
                "status",
                caseCategory
            )
        }

        every {caseMilestonesRepository.findAllByCase(caseEntity)} returns caseMilestones.toMutableList()
        every {milestonesRepository.findByCaseSubCategoryOrderBySequence(caseCategory)} returns milestones
        every {caseEntity.category} returns caseCategoryEntity
        every {caseEntity.status} returns "status"
        every { caseEntity.isLegacy } returns true
        every { userProfileUtil.retrieveProfile(any()) } returns userProfile

        val response=caseService.getCaseMilestones(caseEntity)
        assertNotNull(response)
        assertTrue(response.isNotEmpty())
        response.forEach {
            assertNotNull(it)
            assertNotNull(it.milestoneKey)
            assertNotNull(it.sequence)
        }
        // milestone with 0 sequence is ignored
        assertEquals(milestones.size-1,response.size)
    }

    @Test
    fun `can access case fees test`() {
        val caseEntity=mockk<CaseEntity>()
        val createdBy=mockk<ClientView>()

        every {caseEntity.createdBy} returns createdBy
        every {createdBy.userId} returns authenticatedUser.userId

        assertTrue(caseService.canAccessCaseFees(caseEntity,authenticatedUser))

        //check with only VIEW_FEE_APPROVED access
        every {authenticatedUser.accesses} returns listOf(UserAccess("CASE",mutableListOf("VIEW_FEE_APPROVED")))
        every {caseEntity.caseFees} returns mockk<CaseFees>()
        every {caseEntity.caseFees!!.isApproved} returns true
        assertTrue(caseService.canAccessCaseFees(caseEntity,authenticatedUser))

        //check with no access
        every {authenticatedUser.accesses} returns listOf()
        assertFalse(caseService.canAccessCaseFees(caseEntity,authenticatedUser))

        //check with admin role
        every {authenticatedUser.role} returns Role.ROLE_ADMIN.name
        assertTrue(caseService.canAccessCaseFees(caseEntity,authenticatedUser))
    }

    @Test
    fun `update case test`() {
        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val caseId=14L

        val caseEntity=SingleVisaEntity(
            applicantStayType="",
            caseCountry="US",
            currencySymbol="$",
            foreName="forName",
            surname="surname",
            nationality="US"
        )
        val createdBy=mockk<ClientView>()

        val caseCategoryEntity=CaseCategoryEntity(12L,caseCategory,caseCategory,parentCategoryId,parentCategoryId)
        caseEntity.category=caseCategoryEntity
        caseEntity.createdBy=createdBy
        caseEntity.id=caseId
        caseEntity.country=caseEntity.caseCountry
        caseEntity.initiatedFor="initiated for"
        caseEntity.parentCategoryId=parentCategoryId


        val updateCaseRequest=UpdateCaseRequest(

            status="IN_PROGRESS",
            statusUpdate="",
            startDate=null,
            assignCompany=1,
            notifyPrimaryExpert=false,
            isPriorityCase=false,
            visaType=null,
            visaIssueDate=null,
            visaExpiryDate=null,
            estimatedTimeline=null,
            caseOwner=authenticatedUser.userId,
            accountId=3,
            isStatusChange=true,
            actionFor="CENTURO",
            experts=mutableListOf(),
            managers=mutableListOf(),
            accountManager=null,
            notes=null,
            notifyCaseOwner=true,
            notifyApplicant=true
        )

        caseEntity.status=updateCaseRequest.status
        val countryEntity=mockk<CountryEntity>()

        val milestone=MilestonesEntity(1,1,"milestone-1","milestone-key-1",updateCaseRequest.status,caseCategory)
        val caseMilestoneEntity=CaseMilestonesEntity(1,caseEntity,"milestone-key-1",0)
        val caseStatusMasterEntity=
            CaseStatusMasterEntity(
                1, caseCategory, caseEntity.status, caseEntity.status, 50L, "CENTURO", ********,
                showStatus = true
            )

        every {countryEntity.country} returns caseEntity.country!!
        every {createdBy.userType} returns UserType.CORPORATE
        every {createdBy.userId} returns authenticatedUser.userId
        every {createdBy.email} returns authenticatedUser.email
        every {createdBy.status} returns AccountStatus.ACTIVE
        every {caseRepository.getReferenceById(caseId)} returns caseEntity
        every {clientViewRepository.getReferenceById(updateCaseRequest.caseOwner!!)} returns createdBy
        every {expertCompanyProfileRepository.getReferenceById(updateCaseRequest.assignCompany!!)} returns mockk()
        every {caseStatusHistoryRepository.save(any())} returns mockk()
        every {countryRepository.findAllByCountryCode(caseEntity.country!!)} returns countryEntity
        every {corporateUserEntity.status} returns AccountStatus.ACTIVE
        every {corporateUserEntity.notificationPreferences} returns mutableListOf(
            NotificationPreferencesEntity(
                notificationKey=NotificationType.CASE_UPDATE_EMAIL,
                value=true,
                corporateUser=corporateUserEntity
            )
        )
        every {corporateUserEntity.managers} returns listOf()
        every {corporateUserService.getUserManagers(any())} returns listOf()
        every {
            milestonesRepository.findByCaseSubCategoryAndStatus(
                caseCategoryEntity.subCategoryId,
                caseEntity.status
            )
        } returns milestone
        every {caseMilestonesRepository.findFirstByCaseOrderByLastUpdatedDateDesc(any())} returns caseMilestoneEntity
        every {
            milestonesRepository.findFirstByMilestoneKeyAndCaseSubCategory(
                milestone.milestoneKey,
                caseCategoryEntity.subCategoryId
            )
        } returns milestone
        every {caseMilestonesRepository.saveAll(any<List<CaseMilestonesEntity>>())} returns mockk()
        every {accountEntityRepository.findById(updateCaseRequest.accountId)} returns Optional.of(mockk())
        every {caseRepository.save(any())} returns caseEntity
        every {groupChatService.updateChatParticipants(caseId,ChatType.CASE,any())} returns Unit
        every {
            caseStatusMasterRepository.findBySubCategoryAndStatus(
                caseCategoryEntity.subCategoryId,
                updateCaseRequest.status
            )
        } returns caseStatusMasterEntity

        every { caseRepository.findByIdForExpert(any(), any(), any()) } returns Optional.of(caseEntity)

        every { caseManagerRepository.deleteById(any()) } returns Unit

        every { accountEntityRepository.getReferenceById(any()) } returns AccountEntity(
            id = 3,
            name = "",
            status = AccountStatus.ACTIVE,
            description = "",
            companyName = "",
            corporate = null,
            corporateUsers = listOf(),
            cases = mutableListOf()
        )

        val caseAssigneeEny=CaseAssigneeEntity(
            id = 4,
            case = caseEntity,
            expert = expertUserEntity,
            userType = PartnerCaseType.CG
        )

        every { caseAssigneeRepository.findAllByCase(any()) } returns mutableListOf(caseAssigneeEny)
        every { caseAssigneeRepository.delete(any()) } returns Unit
        every { caseAssigneeRepository.saveAll(mutableListOf())} returns mockk()

        val caseManagerEntity = CaseManagerEntity(
            id = 1,
            case = caseEntity,
            user = loginAccountEntity,
            userType = PartnerCaseType.CG
        )

        every { caseManagerRepository.findAllByCase(any()) } returns mutableListOf(caseManagerEntity)
        every { caseManagerRepository.delete(any()) } returns Unit
        every { caseManagerRepository.saveAll(mutableListOf()) } returns mutableListOf()

        every { groupChatService.updateChatParticipants(any(), any(), any(), any() ) } returns Unit
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()

        val response=caseService.update(caseId,authenticatedUser,updateCaseRequest,authenticatedUser.role)

        assertNotNull(response)
        assertTrue(response)
    }

    /*@Test
    fun `create single visa case with dependents`() {

        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val caseId=15L
        val dependentCaseId=16L

        val caseEntityData=getSingleVisaPayload()
        val dependentCaseData=getDependentVisaPayloadForSingleVisa()

        caseEntityData.put("dependents",listOf(dependentCaseData))

        val additionalData=getAdditionalDataPayload()

        val caseRequest=GenericCaseRequest(
            caseEntityData=mapper.readTree(caseEntityData.toString()),
            additionalData=mapper.readTree(additionalData.toString()),
            userData=null
        )

        val caseStatusMaster=mockk<CaseStatusMasterEntity>()
        val caseEntity=mockk<DependentVisaEntity>()
        val dependentCaseEntity=mockk<DependentVisaEntity>()
        val createdBy=mockk<ClientView>()

        val caseCategoryEntity=CaseCategoryEntity(12L,caseCategory,caseCategory,parentCategoryId,parentCategoryId)
        val dependentCaseCategory=
            CaseCategoryEntity(13L,"DEPENDENT_VISA","DEPENDENT_VISA",parentCategoryId,parentCategoryId)

        every {caseStatusMaster.actionFor} returns "Centuro"
        every {caseEntity.id} returns caseId
        every {caseEntity.createdBy} returns createdBy
        every {caseEntity.category} returns caseCategoryEntity
        every { caseEntity.partnerId } returns 4
        every {createdBy.userId} returns authenticatedUser.userId
        every {createdBy.userType} returns UserType.CORPORATE
        every {createdBy.fullName} returns "full name"
        every {createdBy.email} returns "<EMAIL>"
        every { createdBy.company } returns "abc"
        every {caseEntity.linkedCases} returns mutableListOf()
        every { clientView.company } returns "abc"
        every {caseEntity.country} returns "IN"
        every {dependentCaseEntity.id} returns dependentCaseId
        every {dependentCaseEntity.createdBy} returns createdBy
        every {dependentCaseEntity.category} returns dependentCaseCategory
        every { dependentCaseEntity.partnerId } returns 4
        every { dependentCaseEntity.country } returns "IN"
        every {caseEntity.linkedCases} returns mutableListOf()

        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory,
                parentCategoryId
            )
        } returns mockk()
        every {clientViewRepository.getReferenceById(LOGGED_IN_USER_ID)} returns mockk()
        every {caseStatusMasterRepository.findBySubCategoryAndStatus(caseCategory,any())} returns caseStatusMaster
        every {accountEntityRepository.findById(any())} returns Optional.of(mockk())
        every {caseRepository.save(any())} returns caseEntity andThen dependentCaseEntity andThen caseEntity
        every {corporateEntity.users} returns listOf()
        every {caseRepository.findById(caseId)} returns Optional.of(caseEntity)
        every {caseDocumentsRepository.existsByCase(caseEntity)} returns true
        every {corporateUserService.getUserManagers(LOGGED_IN_USER_ID)} returns listOf()
        every {groupChatService.createGroupChat(
            ChatType.CASE, caseId, any<List<Long>>(),
            virtualParticipants = listOf()
        )} returns mockk()
        every {groupChatService.createGroupChat(
            ChatType.CASE, dependentCaseId, any<List<Long>>(),
            virtualParticipants = listOf()
        )} returns mockk()

        every { groupChatService.getVirtualParticipants(any()) } returns listOf()

        val groupChatEntity: GroupChatEntity = mockk()
        every{ groupChatService.createGroupChat(
            any(), any(), any(), any()
        ) } returns groupChatEntity

        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                dependentCaseCategory.subCategoryId,
                parentCategoryId
            )
        } returns dependentCaseCategory
        every {
            caseStatusMasterRepository.findBySubCategoryAndStatus(
                dependentCaseCategory.subCategoryId,
                any()
            )
        } returns caseStatusMaster

        every { caseDocumentsRepository.existsByCase(any()) } returns true

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        every { caseManagerRepository.findAllByCase(any()) } returns mutableListOf(caseManagerEntity)
        every { caseManagerRepository.delete(any()) } returns Unit
        every { caseManagerRepository.saveAll(mutableListOf()) } returns mutableListOf()

        every { groupChatService.updateChatParticipants(any(), any(), any(), any() ) } returns Unit
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()

        val corporateTeamEntity = CorporateTeamEntity(
            id = 4,
            designation = "abc",
            user = loginAccountEntity,
            corporate = corporateEntity
        )
        every { corporateEntity.team } returns mutableListOf(corporateTeamEntity)
        every { loginAccountEntity.email } returns "<EMAIL>"
        every { caseEntity.country } returns  "IN"

        every { caseEntity.caseForm } returns CaseFormEntity(
            id = 1,
            name = "name",
            description = "",
            countries = mutableListOf(),
            category = "category",
            fields = "",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = null,
            fieldCount = 12,
            defaultDocuments = ""
        )

        val response=caseService.createCase(caseCategory,caseRequest,authenticatedUser)

        assertNotNull(response)
        assertNotNull(response.first)
        assertEquals(2,response.first.size)
        assertEquals(caseId,response.first[0])
        assertEquals(dependentCaseId,response.first[1])
        assertTrue(response.second)
    }*/

    @Test
    fun `create dependent visa case`() {

        val caseCategory="DEPENDENT_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val caseId=15L
        val noOfDependents=2

        val caseEntityData=getDependentVisaPayload(noOfDependents)

        val dependentVisaEntity=mockk<DependentVisaEntity>()

        val additionalData=getAdditionalDataPayload()

        val caseRequest=GenericCaseRequest(
            caseEntityData=mapper.readTree(caseEntityData.toString()),
            additionalData=mapper.readTree(additionalData.toString()),
            userData=null
        )

        val caseStatusMaster=mockk<CaseStatusMasterEntity>()
        val caseEntity=mockk<DependentVisaEntity>()
        val createdBy=mockk<ClientView>()

        val caseCategoryEntity=
            CaseCategoryEntity(13L,"DEPENDENT_VISA","DEPENDENT_VISA",parentCategoryId,parentCategoryId)

        every {caseStatusMaster.actionFor} returns "Centuro"
        every {caseEntity.id} returns caseId
        every {caseEntity.createdBy} returns createdBy
        every {caseEntity.category} returns caseCategoryEntity
        every { caseEntity.partnerId } returns 4
        every {createdBy.userId} returns authenticatedUser.userId
        every {createdBy.userType} returns UserType.CORPORATE
        every {createdBy.fullName} returns "full name"
        every {createdBy.email} returns "<EMAIL>"
        every { createdBy.company } returns "abc"
        every {caseEntity.linkedCases} returns mutableListOf()
        every { clientView.company } returns "abc"
        every {caseEntity.country} returns "IN"

        every { dependentVisaEntity.partnerId } returns 4
        every { dependentVisaEntity.country } returns "IN"
        every {caseEntity.linkedCases} returns mutableListOf()

        every { caseEntity.caseForm } returns CaseFormEntity(
            id = 1,
            name = "name",
            description = "",
            countries = mutableListOf(),
            category = "category",
            fields = "",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = null,
            fieldCount = 12,
            defaultDocuments = ""
        )

        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory,
                parentCategoryId
            )
        } returns caseCategoryEntity
        every {clientViewRepository.getReferenceById(LOGGED_IN_USER_ID)} returns mockk()
        every {caseStatusMasterRepository.findBySubCategoryAndStatus(caseCategory,any())} returns caseStatusMaster
        every {accountEntityRepository.findById(any())} returns Optional.of(mockk())
        every {caseRepository.save(any())} returns caseEntity
        every {corporateEntity.users} returns listOf()
        every {caseRepository.findById(caseId)} returns Optional.of(caseEntity)
        every {caseDocumentsRepository.existsByCase(any())} returns true
        every {corporateUserService.getUserManagers(LOGGED_IN_USER_ID)} returns listOf()
        every {groupChatService.createGroupChat(
            ChatType.CASE, caseId, any<List<Long>>(),
            virtualParticipants = listOf()
        )} returns mockk()

        every { caseManagerRepository.findAllByCase(any()) } returns mutableListOf(caseManagerEntity)
        every { caseManagerRepository.delete(any()) } returns Unit
        every { caseManagerRepository.saveAll(mutableListOf()) } returns mutableListOf()

        every { groupChatService.getVirtualParticipants(any()) } returns listOf()

        val groupChatEntity: GroupChatEntity = mockk()
        every{ groupChatService.createGroupChat(
            any(), any(), any(), any()
        ) } returns groupChatEntity

        every { groupChatService.updateChatParticipants(any(), any(), any(), any() ) } returns Unit
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()
        every { corporateUserService.getCorporateSuperAdmins(any()) } returns listOf()
        every { corporateEntity.isTeamEmail } returns true

        val corporateTeamEntity = CorporateTeamEntity(
            id = 4,
            designation = "abc",
            user = loginAccountEntity,
            corporate = corporateEntity
        )
        every { corporateEntity.team } returns mutableListOf(corporateTeamEntity)
        every { loginAccountEntity.email } returns "<EMAIL>"
        every { caseEntity.country } returns  "IN"

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit



        val response=caseService.createCase(caseCategory,caseRequest,authenticatedUser)

        assertNotNull(response)
        assertNotNull(response.first)
        assertEquals(noOfDependents,response.first.size)
        assertEquals(caseId,response.first[0])
        assertEquals(caseId,response.first[1])
        assertTrue(response.second)
    }

    @Test
    fun `link case test`() {
        val linkFromId=10L
        val linkToId=11L

        val case1=mockk<CaseEntity>()
        val case2=mockk<CaseEntity>()

        every {case1.id} returns linkFromId
        every {case2.id} returns linkToId
        every {case1.linkedCases} returns mutableListOf()

        every {caseRepository.countLinks(linkFromId,linkToId)} returns 0L
        every {caseRepository.findById(linkFromId)} returns Optional.of(case1)
        every {caseRepository.findById(linkToId)} returns Optional.of(case2)
        every {caseRepository.save(any())} returns mockk()
        every { caseEntity.linkedCases } returns mutableListOf()

        val result=caseService.linkCase(
            linkFromId, listOf(linkToId),
            authenticatedUser = authenticatedUser
        )
        assertNotNull(result)
        assertTrue(result)
    }

    @Test
    fun `link already linked cases`() {
        val linkFromId=10L
        val linkToId=11L

        val case1=mockk<CaseEntity>()

        every {case1.id} returns linkFromId

        every {caseRepository.countLinks(linkFromId,linkToId)} returns 1L
        every {caseRepository.findById(linkToId)} returns Optional.of(case1)

        val result=caseService.linkCase(
            linkFromId, listOf(linkToId),
            authenticatedUser = authenticatedUser
        )
        assertNotNull(result)
        assertFalse(result)
    }

    @Test
    fun `unlink case test`() {
        val linkFromId=10L
        val linkToId=11L

        every {caseRepository.unlinkCases(linkFromId,linkToId)} returns 1

        val result=caseService.unlinkCase(linkFromId,listOf(linkToId))
        assertNotNull(result)
        assertTrue(result)
    }

    @Test
    fun `unlink already unlinked case`() {
        val linkFromId=10L
        val linkToId=11L

        every {caseRepository.unlinkCases(linkFromId,linkToId)} returns 0

        val result=caseService.unlinkCase(linkFromId,listOf(linkToId))
        assertNotNull(result)
        assertFalse(result)
    }

    @Test
    fun `linked cases listing`() {
        val caseId=12L
        val linkedCasesId=(caseId..16).toList()
        val caseViews=linkedCasesId.map {
            CaseView(
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }
        every {caseRepository.getLinkedCaseIds(caseId)} returns linkedCasesId
        every {caseViewRepository.findAllByIdIn(linkedCasesId,any<Pageable>())} returns PageImpl(caseViews)

        val response=caseService.getLinkedCases(caseId)

        assertNotNull(response)
        assertEquals(caseViews.size,response!!.totalElements.toInt())
        assertNotNull(response.rows)
        response.rows.forEach {
            assertNotNull(it)
        }
    }

    @Test
    fun retrieveCorporateUsersListTest() {

        val caseId=1L

        val clientView2=ClientView(            1,
            "<EMAIL>",
            "fName",
            AccountStatus.PENDING_VERIFICATION,
            "cName",
            UserType.CORPORATE,
            true,
            LocalDateTime.now(),
            null,
            null,
            1,
            1,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        )

        every {caseEntity.createdBy} returns clientView2

        every {caseRepository.findById(any())} returns Optional.of(caseEntity)
        every {corporateUserRepository.findById(1)} returns Optional.of(corporateUserEntity)
        every {corporateService.retrieveCorporateUsers(1)} returns listOf()
        every {corporateEntity.id} returns 1L

        val result=caseService.retrieveCorporateUsersList(
            caseId,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(result)
    }

    @Test
    fun `retrieve expert Users`() {

        val caseId=1L
        //simulating case created by expert
        every {clientView.userType} returns UserType.EXPERT
        every {caseRepository.findById(1)} returns Optional.of(caseEntity)
        every {expertCompanyProfileRepository.findByUsers_Id(1)} returns mockk<ExpertCompanyProfileEntity>()
        every { expertCompanyProfileRepository.getReferenceById(any())} returns mockk()
        every { expertCompanyProfileRepository.getReferenceByUsersId(any())} returns mockk()
        every {expertUserService.retrieveExpertUsers(any())} returns listOf()
        every {caseEntity.createdBy} returns clientView
        every {clientView.userId} returns 1L
        every {corporateEntity.id} returns 1L

        val result=caseService.retrieveCorporateUsersList(
            caseId,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(result)
    }

    @Test
    fun `retrieve backoffice Users`() {

        val caseId=1L
        //simulating case created by backoffice
        every {clientView.userType} returns UserType.BACKOFFICE
        every {caseRepository.findById(1)} returns Optional.of(caseEntity)
        every {caseEntity.createdBy} returns clientView
        assertThrows<ApplicationException> {
            caseService.retrieveCorporateUsersList(
                caseId,
                authenticatedUser = authenticatedUser
            )
        }
    }

    @Test
    fun getCaseListTest() {

        val caseCategory1="SINGLE_VISA"
        val caseCategory2="DEPENDENT_VISA"
        val parentCategoryId="IMMIGRATION_GM"

        val caseEntity=mockk<CaseEntity>()
        val caseReferenceData=mockk<CaseReferenceData>()
        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory1,
                parentCategoryId
            )
        } returns caseCategoryEntity1
        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory2,
                parentCategoryId
            )
        } returns caseCategoryEntity2
        every {clientViewRepository.getReferenceByUserId(1)} returns clientView
        every {
            caseRepository.findAllByCreatedByAndParentCategoryIdIn(
                clientView,
                listOf(parentCategoryId)
            )
        } returns listOf(caseReferenceData)
        every {caseEntity.id} returns 1
        every {caseEntity.initiatedFor} returns ""
        val result=caseService.getCaseList(1)
        assertNotNull(result)
    }

    @Test
    fun `list cases for admin`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,null,
                null,false,null,null,null,
                null,null,null, null, null, null, null)

        val pageRequest=PageRequest.of(0,20)
        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseView(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        every {authenticatedUser.role} returns Role.ROLE_ADMIN.name
        every {caseViewRepository.searchByCriteria(filter,pageRequest,any())} returns PageImpl(caseViews)


        val response=caseService.listCases(filter,pageRequest,authenticatedUser,"CASES_INITIATED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.totalElements.toInt())
        assertNotNull(response.rows)
        response.rows.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `list cases for corporate`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,null,null,false,null,
                null,null,null,null,null,
                null, null, null, null)

        val pageRequest=PageRequest.of(0,20)
        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseView(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        val clientView=mockk<ClientView>()
        every {clientView.userId} returns authenticatedUser.userId
        every { clientView.id } returns 3

        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name
//        every {caseDashboardService.getEligibleClientList(authenticatedUser,null,null)} returns Pair(
//            true,
//            listOf(clientView)
//        )

        val pair: Pair<Boolean, List<ClientView>> = Pair(false, listOf(clientView))

        every { corporateService.getEligibleClientList(any(), any(), any()) } returns pair
        every {caseViewRepository.searchByCriteriaAndCreatedUser(filter,pageRequest,listOf(10))} returns PageImpl(
            caseViews
        )


        val response=caseService.listCases(filter,pageRequest,authenticatedUser,"CASES_INITIATED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.totalElements.toInt())
        assertNotNull(response.rows)
        response.rows.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `list cases for assignee`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,null,
                null,false,null, null,null,null,
                null,null, null, null, null, null)

        val pageRequest=PageRequest.of(0,20)
        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseView(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        val clientView=mockk<ClientView>()
        every {clientView.userId} returns authenticatedUser.userId

        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name
        every {
            caseViewRepository.searchByCriteriaAndAssigneeUser(
                filter,
                pageRequest,
                authenticatedUser
            )
        } returns PageImpl(caseViews)


        val response=caseService.listCases(filter,pageRequest,authenticatedUser,"CASES_RECEIVED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.totalElements.toInt())
        assertNotNull(response.rows)
        response.rows.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `download cases for admin`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,
                null,null,false,null,null,null,
                null,null,null, null, null, null, null)

        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseViewForResidenceCountry(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        every {authenticatedUser.role} returns Role.ROLE_ADMIN.name
        every {caseViewRepository.searchByCriteriaForResidenceCountry(filter,any())} returns caseViews


        val response=caseService.downloadCases(filter,authenticatedUser,"CASES_INITIATED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.size)
        assertNotNull(response)
        response.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `download cases for corporate`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,
                null,null,false,null,null,null,
                null,null,null,null,null,null,null)

        val pair: Pair<Boolean, List<ClientView>> = Pair(false, listOf(clientView))

        every { corporateService.getEligibleClientList(any(), any(), any()) } returns pair

        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseViewForResidenceCountry(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        every {clientView.userId} returns authenticatedUser.userId
        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name
        every {caseViewRepository.searchByCriteriaForDownloadAndCreatedUser(filter,any())} returns caseViews
//        every {caseDashboardService.getEligibleClientList(authenticatedUser,null,null)} returns Pair(
//            true,
//            listOf(clientView)
//        )


        val response=caseService.downloadCases(filter,authenticatedUser,"CASES_INITIATED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.size)
        assertNotNull(response)
        response.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `download cases for expert`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,
                null,null,false,null,null,null,
                null,null,null, null,null,null,null)

        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseViewForResidenceCountry(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        every {clientView.userId} returns authenticatedUser.userId
        every {authenticatedUser.role} returns Role.ROLE_EXPERT.name
        every {caseViewRepository.searchByCriteriaForDownloadAndCreatedUser(filter,any())} returns caseViews
//        every {caseDashboardService.getEligibleClientList(authenticatedUser,null,null)} returns Pair(
//            true,
//            listOf(clientView)
//        )


        val response=caseService.downloadCases(filter,authenticatedUser,"CASES_INITIATED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.size)
        assertNotNull(response)
        response.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `download cases for assignee`() {
        val caseId=12L
        val filter=
            CaseSearchFilter.Builder.build(null,null,null,null,null,null,
                null,false,null,null,null,
                null,null,null,null,null,null,null)

        val casesIds=(caseId..16).toList()
        val caseViews=casesIds.map {
            CaseViewForResidenceCountry(
                id=it,
                archive=false,
                initiatedDate=LocalDateTime.now(),
                notifyCaseOwner=true,
                parentCategoryId="IMMIGRATION_GM"
            )
        }

        val clientView=mockk<ClientView>()
        every {clientView.userId} returns authenticatedUser.userId

        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name
        every {
            caseViewRepository.searchByCriteriaForDownloadAndAssigneeUser(
                filter,
                authenticatedUser
            )
        } returns caseViews


        val response=caseService.downloadCases(filter,authenticatedUser,"CASES_RECEIVED",false)

        assertNotNull(response)
        assertEquals(caseViews.size,response.size)
        response.forEach {
            assertNotNull(it)
            assertTrue(casesIds.contains(it.caseId))
        }
    }

    @Test
    fun `list Document status Logs`() {
        val caseId=12L
        val logType="STATUS"
        val docType="VISA"

        val statusLogs=(1..10).map {
            CaseDoucmentsStatusLogsEntity(
                it.toLong(),
                caseId,
                it.toLong(),
                "REQUESTED",
                "127.0.0.1",
                LocalDateTime.now(),
                authenticatedUser.userId,
                docType,
                null
            )
        }

        every {caseRepository.findById(caseId)} returns Optional.of(mockk())
        every {
            caseDocumentsStatusLogsRepository.findAllByCaseIdAndDocType(
                caseId,
                docType,
                any<PageRequest>()
            )
        } returns statusLogs
        every {caseDocumentsStatusLogsRepository.findAllByCaseId(caseId,any<PageRequest>())} returns statusLogs

        every { userProfileUtil.retrieveProfile(any()) } returns userProfile

        val response=caseService.listDocumentActivityLogs(
            caseId, logType, docType,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(response)
        assertTrue(response!!.isNotEmpty())
        response.forEach {
            assertNotNull(it)
            assertEquals(docType,it.docType)
            assertNotNull(it.status)
            assertNotNull(it.createdBy)
            assertNotNull(it.ipAddress)
        }

        // with docType null
        val responseWithoutDocType=caseService.listDocumentActivityLogs(
            caseId, logType, null,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(responseWithoutDocType)
        assertTrue(responseWithoutDocType!!.isNotEmpty())
        responseWithoutDocType.forEach {
            assertNotNull(it)
            assertEquals(docType,it.docType)
            assertNotNull(it.status)
            assertNotNull(it.createdBy)
            assertNotNull(it.ipAddress)
        }
    }

    @Test
    fun `list Document activity Logs`() {
        val caseId=12L
        val logType="ACTIVITY"
        val docType="VISA"

        val statusLogs=(1..10).map {
            CaseDoucmentsAuditEntity(
                it.toLong(),
                caseId,
                it.toLong(),
                LocalDateTime.now(),
                authenticatedUser.userId,
                CaseDocumentAction.UPLOAD,
                "127.0.0.1",
                it.toLong(),
                docType,
                "file-$it.pdf",
                null
            )
        }

        every {caseRepository.findById(caseId)} returns Optional.of(mockk())
        every {
            caseDocumentsAuditRepository.findAllByCaseIdAndDocType(
                caseId,
                docType,
                any<PageRequest>()
            )
        } returns statusLogs
        every {caseDocumentsAuditRepository.findAllByCaseId(caseId,any<PageRequest>())} returns statusLogs

        every { userProfileUtil.retrieveProfile(any()) } returns userProfile

        val response=caseService.listDocumentActivityLogs(
            caseId, logType, docType,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(response)
        assertTrue(response!!.isNotEmpty())
        response.forEach {
            assertNotNull(it)
            assertNotNull(it.status)
            assertEquals(CaseDocumentAction.UPLOAD.name,it.status)
            assertNotNull(it.createdBy)
            assertNotNull(it.ipAddress)
        }

        // with docType null
        val responseWithoutDocType=caseService.listDocumentActivityLogs(
            caseId, logType, null,
            authenticatedUser = authenticatedUser
        )
        assertNotNull(responseWithoutDocType)
        assertTrue(responseWithoutDocType!!.isNotEmpty())
        responseWithoutDocType.forEach {
            assertNotNull(it)
            assertNotNull(it.status)
            assertEquals(CaseDocumentAction.UPLOAD.name,it.status)
            assertNotNull(it.createdBy)
            assertNotNull(it.ipAddress)
        }
    }

    @Test
    fun `list Document Logs with invalid log type`() {
        val caseId=12L
        val logType="DEBUG"
        val docType="VISA"

        every {caseRepository.findById(caseId)} returns Optional.of(mockk())

        assertThrows<ApplicationException> {
            caseService.listDocumentActivityLogs(
                caseId, logType, docType,
                authenticatedUser = authenticatedUser
            )
        }
    }

    /*@Test
    fun `list Document Logs with invalid case id`() {
        val caseId=12L
        val logType="STATUS"
        val docType="VISA"

        every {caseRepository.findById(caseId)} returns Optional.empty()

        assertThrows<ApplicationException> {
            caseService.listDocumentActivityLogs(
                caseId, logType, docType,
                authenticatedUser = authenticatedUser
            )
        }
    }*/

    @Test
    fun `get applicant case details`() {
        val caseEntity=SingleVisaEntity(
            applicantStayType="",
            caseCountry="US",
            currencySymbol="$",
            foreName="forName",
            surname="surname",
            nationality="US"
        )

        val caseViewDetails=CaseViewDetails(archive=false,notifyCaseOwner=true)

        val actionFor=listOf("CLIENT","APPLICANT","CENTURO")

        val statusHistory=(1..5).map {
            CaseStatusHistory(
                id=it.toLong(),
                case=caseEntity,
                lastUpdatedBy=authenticatedUser.userId,
                actionFor=actionFor.random()
            )
        }

        caseEntity.id=13L
        caseEntity.category=caseCategoryEntity1
        caseEntity.statusHistory=statusHistory
        caseEntity.country="US"

        caseViewDetails.caseDetails=caseEntity

        every { caseDashboardService.getApplicantCaseDetails(any(), any()) } returns caseViewDetails

        every { caseStatusMasterRepository.findBySubCategoryAndStatus(any(), any()) } returns CaseStatusMasterEntity(
            id = 3,
            subCategory = "",
            status = "",
            statusDisplayText = "",
            percentage = 50,
            actionFor = "abc",
            dealStatusId = 18,
            showStatus = false
        )

        val response=caseDashboardService.getApplicantCaseDetails(authenticatedUser, caseViewDetails)
        assertNotNull(response)
    }

    @Test
    fun createPublicCaseTest() {
        val userId=1L
        val accounId=1L
        val caseCategory="SINGLE_VISA"
        val parentCategoryId="IMMIGRATION_GM"
        val status="NOT_STARTED"
        val caseEntityData=JSONObject()
            .put("visaType","string")
            .put("nationality","string")
            .put("caseCountry","string")
            .put("fromCountry","string")
            .put("tripPurpose","string")
            .put("foreName","string")
            .put("middleName","string")
            .put("surname","string")
            .put("covidVaccinated","string")
            .put("dependantTravelling","string")
            .put("applicantStay",50)
            .put("estimatedSalary",500000)
            .put("paidCountry","string")
            .put("entityInHostCountry",false)
            .put("duties","string")
            .put("qualification","string")
            .put("applicantStayType","string")
            .put("currencySymbol","string")
            .put("accountName","string")
            .put("shareApplicantInfo",false)
            .put("contactNo","string")
            .put("emailAddress","string")
            .put("visaIssueDate",*************)
            .put("visaExpiryDate",*************)
            .put("accountName","Test")
            .put("caseCountry","IN")
            .put("contactNo","445454")
            .put("emailAddress","<EMAIL>")
        val additionalData=JSONObject()
            .put("accountId",3)
            .put("caseOwner",authenticatedUser.userId)
            .put("parentCategoryId","IMMIGRATION_GM")
            .put("notifyCaseOwner",false)
            .put(
                "personalDetails",JSONObject()
                    .put("firstName","String")
                    .put("lastName","String")
                    .put("email","<EMAIL>")
                    .put("country","IN")
            )
        val userData=JSONObject()
            .put("firstName","String")
            .put("lastName","String")
            .put("email","<EMAIL>")
            .put("country","IN")
            .put("corporateName","Corporate_1")
            .put("recaptchaResponse","recaptchaResponse")
        val caseRequest=GenericCaseRequest(
            caseEntityData=mapper.readTree(caseEntityData.toString()),
            additionalData=mapper.readTree(additionalData.toString()),
            userData=mapper.readTree(userData.toString())
        )
        val caseStatusMasterEntity=CaseStatusMasterEntity(
            1,
            caseCategory,
            CaseStatus.NOT_STARTED.name,
            CaseStatus.NOT_STARTED.name,
            50L,
            "CENTURO",
            ********,
            showStatus = true
        )

        val clientView=ClientView(
            1,
            "<EMAIL>",
            "fName",
            AccountStatus.ACTIVE,
            "cName",
            UserType.EXPERT,
            true,
            LocalDateTime.now(),
            null,
            null,
            1,
            1,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        )
        val groupChatEntity=GroupChatEntity(
            1,
            ChatType.CASE,
            1
        )
        every {loginAccountRepository.findByEmail("<EMAIL>")} returns null andThen loginAccountEntity
        every {corporateService.createCorporateAndReturnData(signUpRequest,true)} returns Pair(userId,accounId)
        every {
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                caseCategory,
                parentCategoryId
            )
        } returns caseCategoryEntity1
        every {clientViewRepository.getReferenceById(1)} returns clientView
        every { loginAccountEntity.email } returns "<EMAIL>"
        every {corporateUserRepository.findById(any())} returns Optional.of(corporateUserEntity)

        every { caseEntity.caseForm } returns CaseFormEntity(
            id = 1,
            name = "name",
            description = "",
            countries = mutableListOf(),
            category = "category",
            fields = "",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = null,
            fieldCount = 12,
            defaultDocuments = ""
        )

        val corporateTeamEntity = CorporateTeamEntity(
            id = 4,
            designation = "abc",
            user = loginAccountEntity,
            corporate = corporateEntity
        )
        every { corporateEntity.team } returns mutableListOf(corporateTeamEntity)
        every {
            caseStatusMasterRepository.findBySubCategoryAndStatus(
                any(),
                any()
            )
        } returns caseStatusMasterEntity
        every {loginAccountRepository.findById(any())} returns Optional.of(corporateUserEntity)
        every {accountEntityRepository.findById(1)} returns Optional.of(accountEntity)
        every {caseRepository.save(any())} returns caseEntity
        every {caseEntity.createdBy} returns clientView
        every {caseEntity.category} returns caseCategoryEntity1
        every {caseEntity.id} returns 0L
        every { caseEntity.partnerId } returns 4
        every { caseEntity.country } returns  "IN"
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit
        every {loginAccountEntity.id} returns 1
        every {validationTokenRepository.findByUserId(1)} returns validationTokenEntity
        every {corporateUserService.getUserManagers(1)} returns listOf(1)
        every {groupChatService.createGroupChatWithOwner(ChatType.CASE,0,listOf(1,1), listOf(),1)} returns groupChatEntity
        val Response=caseService.createPublicCase(caseCategory,caseRequest)
        assertNotNull(Response)
    }

    @Test
    fun deletePublicCaseTest() {
        val caseEntity=mockk<CaseEntity>()
        val clientView=ClientView(
            1,
            "<EMAIL>",
            "fName",
            AccountStatus.PENDING_VERIFICATION,
            "cName",
            UserType.EXPERT,
            true,
            LocalDateTime.now(),
            null,
            null,
            1,
            1,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        )
        every {validationTokenRepository.findByCode("TOKEN")} returns validationTokenEntity
        every {caseRepository.findById(1)} returns Optional.of(caseEntity)
        every {caseEntity.createdBy} returns clientView
        every {caseRepository.delete(caseEntity)} returns Unit
        every {adminUserService.deleteCorporateByRootUserId(1)} returns Unit
        val Response=caseService.deletePublicCase(1,"TOKEN")
        assertNotNull(Response)
    }

    @Test
    fun getDetailsTest() {
        val approverDetails=ApproverDetails(
            "fName",
            "lName",
            "jobTitle",
            "<EMAIL>",
            LocalDateTime.now()
        )
        val caseContactInformation=CaseContactInformation(
            "fName",
            "lName",
            "title",
            "<EMAIL>",
            null,
            "US",
            null,
            null
        )
        val caseEntity=mockk<CaseEntity>()
        val feeDetails=FeeDetails(
            475.59,
            4673.67,
            8765.10,
            230.20,
            480.79,
            500.20,
            700.50,
            false,
            false,
            400.00
        )
        every {caseRepository.findByUuid("ID")} returns caseEntity
        every {caseEntity.feeToken} returns "feeToken"
        every {caseEntity.caseFees} returns mockk<CaseFees>()
        every {caseEntity.caseFees!!.isApproved} returns true
        every {caseEntity.caseFees!!.approverDetails} returns approverDetails
        every {caseEntity.id} returns 1L
        every {caseEntity.initiatedDate} returns LocalDateTime.now()
        every {caseEntity.country} returns "US"
        every {caseEntity.personalDetails} returns caseContactInformation
        every {caseEntity.initiatedFor} returns "String"
        every {caseEntity.category} returns caseCategoryEntity1
        every {caseEntity.caseFees!!.comments} returns "String"
        every {caseEntity.caseFees!!.approverEmails} returns "String"
        every {caseEntity.caseFees!!.needApproval} returns true
        every {caseEntity.caseFees!!.isApproved} returns true
        every {caseEntity.caseFees!!.feesCreationDate} returns LocalDateTime.now()
        every {caseEntity.caseFees!!.feesDetails} returns feeDetails
        val Response=caseService.getDetails("ID","feeToken")
        assertNotNull(Response)
        assertEquals(Response.caseId,caseEntity.id)
    }
    @Test
    fun caseFeesApprovalForExternalTest()
    {
        val caseEntity = mockk<CaseEntity>()
        val approverDetailsRequest = ApproverDetailsRequest(
            "fName",
            "lName",
            "jobTitle",
            "<EMAIL>",
            1L,
            "feeToken")
        val caseFees = CaseFees(
            "Comment",
            "<EMAIL>",
            true,
            true,
            LocalDateTime.now())
        val clientView = ClientView(
            1,
            "<EMAIL>",
            "fName",
            AccountStatus.PENDING_VERIFICATION,
            "cName",
            UserType.EXPERT,
            true,
            LocalDateTime.now(),
            null,
            null,
            1,
            1,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        )
        val loginAccountEntity: LoginAccountEntity = mockk()
        val caseFeesApprovalHistory = CaseFeesApprovalHistory(
            0,
            caseFees,
            caseEntity)
        every { caseRepository.findByUuid("ID")} returns caseEntity
        every { caseEntity.feeToken } returns "feeToken"
        every { caseEntity.caseFees } returns caseFees
        every { caseEntity.createdBy } returns clientView
        every { caseEntity.accountManager } returns  loginAccountEntity
        every { caseEntity.id } returns 1L
        every { caseEntity.category} returns caseCategoryEntity1
        every { loginAccountEntity.status } returns AccountStatus.ACTIVE
        every { loginAccountEntity.email } returns "<EMAIL>"
        every { caseRepository.save(any()) } returns caseEntity
        every { caseFeesApprovalHistoryRepository.save(any()) } returns caseFeesApprovalHistory
        val response = caseService.caseFeesApprovalForExternal("ID",approverDetailsRequest)
        assertNotNull(response)
    }
}
