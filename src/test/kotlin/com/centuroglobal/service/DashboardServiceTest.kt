package com.centuroglobal.service

import com.centuroglobal.costofliving.client.NumbeoClient
import com.centuroglobal.shared.data.entity.CountryGdpEntity
import com.centuroglobal.shared.data.entity.CountryIndicesEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.entity.view.CountryView
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.repository.view.CountryViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.*

class DashboardServiceTest{

    private val blueprintRepository:BlueprintRepository = mockk()
    private val expertUserRepository:ExpertUserRepository = mockk()
    private val leadRepository:LeadRepository = mockk()
    private val eventRepository:EventRepository = mockk()
    private val loginAccountRepository:LoginAccountRepository = mockk()
    private val countryGdpRepository:CountryGdpRepository = mockk()
    private val countryIndicesRepository:CountryIndicesRepository = mockk()
    private val countryViewRepository:CountryViewRepository = mockk()
    private val countryIndicesCategoryRepository:CountryIndicesCategoryRepository = mockk()
    private val countryHighlightsRepository:CountryHighlightsRepository = mockk()
    private val caseRepository:CaseRepository = mockk()
    private val clientViewRepository:ClientViewRepository = mockk()
    private val authenticatedUser:AuthenticatedUser=mockk()
    private val clientView:ClientView=mockk()
    private val  numbeoClient:NumbeoClient= mockk()
    private val userProfileUtil:UserProfileUtil = mockk()
    private val awsS3Service:AwsS3Service = mockk()
    private val clientDocRepository:ClientDocRepository = mockk()




    private val expiryDay: Long = 1L
    private val indicesExpiryDay: Long = 100L
    private val apiKey: String = "api-key"

    private val dashboardService = DashboardService(
        blueprintRepository,
        expertUserRepository,
        leadRepository,
        eventRepository,
        loginAccountRepository,
        countryGdpRepository,
        countryIndicesRepository,
        countryViewRepository,
        countryIndicesCategoryRepository,
        countryHighlightsRepository,
        caseRepository,
        clientViewRepository,
        expiryDay,
        indicesExpiryDay,
        apiKey,
        userProfileUtil = userProfileUtil,
        awsS3Service = awsS3Service,
        clientDocRepository = clientDocRepository
    )

//    val dashboardService.worldBankClient = mockk()

    private var mapper=ObjectMapper()


    private val countryIndicesEntity = CountryIndicesEntity(
        1L,
        "US",
        5.5,
        5.5,
        5.5,
        5.5,
        5.5,
        5.5,
        5.5,
        5.5,
        null)

    private  val countryGdpEntity = CountryGdpEntity(
        1L,
        "US",
        "5.5",
        "123456",
        "5.5",
        null)

    private val countryView = CountryView(
        1,
        "US",
        "US",
        "001",
        10L,
        10L,
        10L,
        10L,
        10L,
        10L,
        10L,
        10L,
        10L,
        10L,
        "001")

    @BeforeEach
    fun setup() {
        every {authenticatedUser.userId} returns LOGGED_IN_USER_ID
        every {authenticatedUser.companyId} returns LOGGED_IN_USER_CORPORATE_ID
        every {authenticatedUser.email} returns LOGGED_IN_USER_EMAIL
        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun generateAggregateDataTest(){

        val userId = 10L
            every { blueprintRepository.countByStatus(BlueprintStatus.ACTIVE) } returns 1L
            every { expertUserRepository.count() } returns 1L
            every { leadRepository.countByCreatedBy(userId) } returns 1L
            every { eventRepository.countByEndDateGreaterThan(any()) } returns 1L
            every { loginAccountRepository.countByReferredBy(userId) } returns 1L
            every { clientViewRepository.findById(userId) } returns Optional.of(clientView)
            every { caseRepository.countByCreatedBy(clientView) } returns 1L

        val result = dashboardService.generateAggregateData(authenticatedUser)
        assertNotNull(result)
        assertNotNull(result[0].key)
        assertNotNull(result[0].value)
    }

    @Test
    fun getTopCountryDataByFilterTest() {

        val filters=listOf<String>(
            "costOfLivingIndex","qualityOfLifeIndex","crimeIndex","healthCareIndex",
            "purchasingPowerInclRentIndex","pollutionIndex","safetyIndex","propertyPriceToIncomeRatio"
        )

        every {countryViewRepository.findAll()} returns  listOf(countryView!!)
        every {countryGdpRepository.findAllByCreatedDateGreaterThan(any())} returns  mutableListOf(countryGdpEntity)
        every {countryIndicesRepository.findAllByCreatedDateGreaterThan(any())} returns mutableListOf(countryIndicesEntity)

        filters.forEach {
            val result=dashboardService.getTopCountryDataByFilter(it)
            assertNotNull(result)
            assertEquals(result[0].countryCode,countryGdpEntity.countryCode)
        }
    }
}