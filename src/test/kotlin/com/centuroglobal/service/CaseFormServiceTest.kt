package com.centuroglobal.service

import com.centuroglobal.data.payload.case.CaseFormContent
import com.centuroglobal.data.payload.case.CaseFormRequest
import com.centuroglobal.shared.data.entity.CaseFormCountryEntity
import com.centuroglobal.shared.data.entity.CaseFormEntity
import com.centuroglobal.shared.data.entity.DocumentMasterEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.case.CaseFormMapping
import com.centuroglobal.shared.data.pojo.case.CaseFormSearchFilter
import com.centuroglobal.shared.repository.PartnerRepository
import com.centuroglobal.shared.repository.case.CaseFormRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.util.*
import kotlin.test.assertTrue

class CaseFormServiceTest {

    private val caseFormRepository: CaseFormRepository = mockk()
    private val partnerRepository: PartnerRepository = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val mapper: ObjectMapper = ObjectMapper()

    val caseFormService = CaseFormService(
        caseFormRepository,
        partnerRepository,
        userProfileUtil,
        mapper
    )


    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { authenticatedUser.userType } returns UserType.BACKOFFICE.name

        every { partnerRepository.getReferenceById(any()) } returns mockk()

        every { userProfileUtil.retrieveProfile(any()) } returns UserProfile(
            1,
            "<EMAIL>",
            "String",
            "String",
            AccountStatus.ACTIVE,
            Role.ROLE_ADMIN,
            "String",
            "String",
            "String"
        )


    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun createCaseForm() {
        val caseFormEntityId = 1L
        val caseFormEntity = mockk<CaseFormEntity>()
        every { caseFormRepository.save(any()) } returns caseFormEntity
        every { caseFormEntity.id } returns caseFormEntityId
        every { authenticatedUser.partnerId } returns null

        val request = CaseFormRequest(
            name = "Test case form 1",
            description = "Test Description",
            countries = listOf("IN", "US"),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 5,
            defaultDocumentList = listOf("VISA", "PASSPORT")
        )

        val response = caseFormService.create(request, authenticatedUser)

        assertNotNull(response)
        assertEquals(caseFormEntityId, response)
    }

    @Test
    fun createCaseFormForPartner() {
        val caseFormEntityId = 1L
        val caseFormEntity = mockk<CaseFormEntity>()
        every { caseFormRepository.save(any()) } returns caseFormEntity
        every { caseFormEntity.id } returns caseFormEntityId

        val request = CaseFormRequest(
            name = "Test partner case form 1",
            description = "Test Description",
            countries = listOf("IN", "US"),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 5,
            defaultDocumentList = listOf("VISA", "PASSPORT")
        )

        val response = caseFormService.create(request, authenticatedUser)

        assertNotNull(response)
        assertEquals(caseFormEntityId, response)
    }

    @Test
    fun getCaseForm() {
        val caseFormEntityId = 1L
        val caseFormName = "Test Form 1"

        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )
        caseFormEntity.updatedBy = 1

        every { authenticatedUser.partnerId } returns null
        every { caseFormRepository.getFormForView(caseFormEntityId, any()) } returns caseFormEntity

        val response = caseFormService.get(caseFormEntityId, authenticatedUser)

        assertNotNull(response)
        assertEquals(caseFormEntityId, response.id)
        assertEquals(caseFormName, response.name)
        assertNotNull(response.updatedOn)
        assertNotNull(response.updatedBy)
        assertEquals(caseFormEntity.countries.map { it.code }, response.countries)
        assertEquals(caseFormEntity.description, response.description)
        assertEquals(caseFormEntity.visibility, response.visibility)
        assertEquals(caseFormEntity.status, response.status)
        assertNotNull(response.mapping)
        assertEquals(caseFormEntity.mappingFields!!.email, response.mapping!!.email)
        assertEquals(caseFormEntity.mappingFields!!.country, response.mapping!!.country)
        assertEquals(caseFormEntity.mappingFields!!.company, response.mapping!!.company)
        assertEquals(caseFormEntity.mappingFields!!.firstName, response.mapping!!.firstName)
        assertEquals(caseFormEntity.mappingFields!!.lastName, response.mapping!!.lastName)
    }

    @Test
    fun listCaseForms() {
        val caseFormEntityId = 1L
        val caseFormName = "Test Form 1"

        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )
        caseFormEntity.updatedBy = 1

        val filter = CaseFormSearchFilter.Builder.build(null, null, null, null,
            null, null, null, false)

        val pageRequest = PageRequest.of(0,20)

        every { authenticatedUser.partnerId } returns null
        every { caseFormRepository.searchByCriteriaForAdmin(filter,pageRequest) } returns PageImpl(listOf(caseFormEntity))

        val pagedResponse = caseFormService.list(filter, pageRequest, authenticatedUser)

        assertNotNull(pagedResponse)
        assertNotNull(pagedResponse.rows)
        val response = pagedResponse.rows[0]
        assertEquals(caseFormEntityId, response.id)
        assertEquals(caseFormName, response.name)
        assertNotNull(response.updatedOn)
        assertNotNull(response.updatedBy)
        assertEquals(caseFormEntity.countries.map { it.code }, response.countries)
        assertEquals(caseFormEntity.description, response.description)
        assertEquals(caseFormEntity.visibility, response.visibility)
        assertEquals(caseFormEntity.status, response.status)
    }

    @Test
    fun updateCaseForm() {
        val caseFormEntityId = 1L
        val caseFormName = "Test Form 1"
        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )

        every { caseFormRepository.findById(caseFormEntityId) } returns Optional.of(caseFormEntity)
        every { caseFormRepository.save(any()) } returns caseFormEntity
        every { authenticatedUser.partnerId } returns null

        val request = CaseFormRequest(
            name = "Test case form 1",
            description = "Test Description",
            countries = listOf("IN", "US"),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 5,
            defaultDocumentList = listOf("VISA", "PASSPORT")
        )

        val response = caseFormService.update(caseFormEntityId, request, authenticatedUser)
        assertNotNull(response)
        assertTrue(response)
    }

    @Test
    fun copyCaseForm() {
        val caseFormEntityId = 1L
        val copiedFormEntityId = 2L
        val caseFormName = "Test Form 1"
        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )
        val copiedCaseFormEntity = mockk<CaseFormEntity>()

        every { caseFormRepository.getFormForView(caseFormEntityId, null) } returns caseFormEntity
        every { caseFormRepository.save(any()) } returns copiedCaseFormEntity
        every { copiedCaseFormEntity.id } returns copiedFormEntityId
        every { authenticatedUser.partnerId } returns null

        val response = caseFormService.copy(caseFormEntityId, authenticatedUser)

        assertNotNull(response)
        assertEquals(copiedFormEntityId, response)
    }

    @Test
    fun deleteCaseForm() {
        val caseFormEntityId = 1L
        val caseFormEntity = mockk<CaseFormEntity>()

        every { caseFormRepository.findById(caseFormEntityId) } returns Optional.of(caseFormEntity)
        every { caseFormRepository.delete(any()) } returns Unit
        every { authenticatedUser.partnerId } returns null

        val response = caseFormService.delete(caseFormEntityId, authenticatedUser)

        assertNotNull(response)
        assertTrue(response)
    }

    @Test
    fun updateStatusOfCaseForm() {
        val caseFormEntityId = 1L
        val caseFormName = "Test Form 1"
        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )

        every { caseFormRepository.findById(caseFormEntityId) } returns Optional.of(caseFormEntity)
        every { caseFormRepository.save(any()) } returns caseFormEntity
        every { authenticatedUser.partnerId } returns null

        val response = caseFormService.updateStatus(caseFormEntityId, CaseFormStatus.ACTIVE, authenticatedUser)

        assertNotNull(response)
        assertTrue(response == true)
    }

    @Test
    fun updateContentOfCaseForm() {
        val caseFormEntityId = 1L
        val caseFormName = "Test Form 1"
        val caseFormEntity = CaseFormEntity(
            id = caseFormEntityId,
            name = caseFormName,
            description = "test desc",
            countries = mutableListOf(CaseFormCountryEntity(code = "IN")),
            category = "LEGAL",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            partner = null,
            mappingFields = CaseFormMapping(
                "firstName", "lastName", "country", "email", "company"
            )
        )

        val request = CaseFormContent(
            content = mapper.readTree(""" {"field1": "value1", "field2": "value2" } """),
            mapping = CaseFormMapping(
                firstName = "firstName",
                lastName = "lastName",
                country = "country",
                email = "emailAddress",
                company = "companyName"
            ),
            fieldCount = 2,
            defaultDocumentList = listOf(DocumentMasterEntity(documentCode = "VISA", documentName = "Visa"))
        )

        every { caseFormRepository.findById(caseFormEntityId) } returns Optional.of(caseFormEntity)
        every { caseFormRepository.save(any()) } returns caseFormEntity
        every { authenticatedUser.partnerId } returns null

        val response = caseFormService.updateContent(caseFormEntityId, request, authenticatedUser)

        assertNotNull(response)
        assertTrue(response == true)
    }



}