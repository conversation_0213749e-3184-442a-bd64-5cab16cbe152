package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.TaskEntity
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Value

class TaskEmailServiceTest {

    private lateinit var taskEmailService: TaskEmailService
    private val mailSendingService = mockk<MailSendingService>(relaxed = true)
    private val awsS3Service = mockk<AwsS3Service>()

    @Value("\${app.web-url}")
    private val webUrl: String = "http://example.com"

    @BeforeEach
    fun setUp() {
        taskEmailService = TaskEmailService(
            webUrl,
            mailSendingService,
            awsS3Service
        )
    }

    @Test
    fun `taskEmail should send new task email`() {
        val task = mockk<TaskEntity>(relaxed = true)
        val mailToUserName = "John Doe"
        val performerFullName = "Jane Smith"
        val recipient = "<EMAIL>"
        val taskEvent = AppConstant.CONST_NEW_TASK

        every { task.name } returns "Sample Task"
        every { awsS3Service.getS3PublicUrl(any()) } returns "http://s3-url.com"

        taskEmailService.taskEmail(task, mailToUserName, performerFullName, recipient, taskEvent)

        verify {
            mailSendingService.sendEmail(
                withArg<MailTemplate> {
                    assertEquals("email/task/new_task", it.templateName)
                    assertEquals("New Task - Sample Task", it.subject)
                    assertEquals(recipient, it.recipient)
                }
            )
        }
    }

    @Test
    fun `taskEmail should send update task email`() {
        val task = mockk<TaskEntity>(relaxed = true)
        val mailToUserName = "John Doe"
        val performerFullName = "Jane Smith"
        val recipient = "<EMAIL>"
        val taskEvent = AppConstant.CONST_UPDATE_TASK

        every { task.name } returns "Sample Task"
        every { awsS3Service.getS3PublicUrl(any()) } returns "http://s3-url.com"

        taskEmailService.taskEmail(task, mailToUserName, performerFullName, recipient, taskEvent)

        verify {
            mailSendingService.sendEmail(
                withArg<MailTemplate> {
                    assertEquals("email/task/update_task", it.templateName)
                    assertEquals("Task status updated - Sample Task", it.subject)
                    assertEquals(recipient, it.recipient)
                }
            )
        }
    }

    @Test
    fun `taskEmail should send task reminder email`() {
        val task = mockk<TaskEntity>(relaxed = true)
        val mailToUserName = "John Doe"
        val performerFullName = "Jane Smith"
        val recipient = "<EMAIL>"
        val taskEvent = AppConstant.CONST_TASK_REMINDER

        every { task.name } returns "Sample Task"
        every { awsS3Service.getS3PublicUrl(any()) } returns "http://s3-url.com"

        taskEmailService.taskEmail(task, mailToUserName, performerFullName, recipient, taskEvent)

        verify {
            mailSendingService.sendEmail(
                withArg<MailTemplate> {
                    assertEquals("email/task/task_reminder", it.templateName)
                    assertEquals("Task Reminder - Sample Task", it.subject)
                    assertEquals(recipient, it.recipient)
                }
            )
        }
    }
}
