package com.centuroglobal.service

import com.centuroglobal.shared.costofliving.data.entity.CountryEntity
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.subscription.SubscriptionPlanRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.DownloadWrapper
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.*
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.LocalDateTime

class AdminMasterDataServiceTest {


    private val masterDateS3Bucket = "test-master-data-bucket"
    private val s3Service: AwsS3Service = mockk()
    private val countryTaxRepository: CountryTaxRepository = mockk()
    private val countryHighlightsRepository: CountryHighlightsRepository = mockk()
    private val countryRepository: CountryRepository = mockk()
    private val masterDataRepository: MasterDataRepository = mockk()
    private val masterContentRepository: MasterContentRepository = mockk()
    private val immigrationRequirementRepository: ImmigrationRequirementRepository = mockk()
    private val entityTypeRepository: EntityTypeRepository = mockk()
    private val authoritiesRepository: AuthoritiesRepository = mockk()
    private val milestonesRepository: MilestonesRepository = mockk()
    private val caseStatusMasterRepository: CaseStatusMasterRepository = mockk()
    private val businessIndustryRepository: BusinessIndustryRepository = mockk()
    private val subscriptionPlanRepository: SubscriptionPlanRepository = mockk()
    private val excelService: ExcelService = mockk()

    private val adminMasterDataService = AdminMasterDataService(
        masterDateS3Bucket,
        s3Service,
        countryTaxRepository,
        countryHighlightsRepository,
        countryRepository,
        masterDataRepository,
        masterContentRepository,
        immigrationRequirementRepository,
        entityTypeRepository,
        authoritiesRepository,
        milestonesRepository,
        caseStatusMasterRepository,
        businessIndustryRepository = businessIndustryRepository,
        excelService = excelService,
        subscriptionPlanRepository = subscriptionPlanRepository
    )


    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `test downloadTemplate should return ResponseEntity with correct content`() {
        // Mock the dependencies
        val templateData = "Template content"
        val template = "test-template"
        every { s3Service.downLoadFile(masterDateS3Bucket, template) } returns templateData.byteInputStream()

        val responseEntity = adminMasterDataService.downloadTemplate(template)
        assertNotNull(responseEntity)
        assertEquals(HttpStatus.OK, responseEntity.statusCode)
        assertNotNull(responseEntity.body)
        val baos = ByteArrayOutputStream()
        responseEntity.body?.writeTo(baos)
        baos.close()
        assertEquals(templateData, String(baos.toByteArray()))
        assertTrue(responseEntity.headers.contentType?.isCompatibleWith(MediaType.APPLICATION_OCTET_STREAM) ?: false)
    }

    @Test
    fun `test downloadTemplate should not return template response when template file not found in s3`() {
        val template = "test-template"
        every { s3Service.downLoadFile(masterDateS3Bucket, template) } throws Exception()

        val responseEntity = adminMasterDataService.downloadTemplate(template)
        assertNotNull(responseEntity)
        assertEquals(HttpStatus.OK, responseEntity.statusCode)
        assertNotNull(responseEntity.body)
        val baos = ByteArrayOutputStream()

        var hasError = false
        try {
            responseEntity.body?.writeTo(baos)
        }
        catch (ex: ApplicationException){
            hasError = true
        }finally {
            baos.close()
        }
        assertTrue(hasError)
        assertTrue(String(baos.toByteArray()).isEmpty())
        assertTrue(responseEntity.headers.contentType?.isCompatibleWith(MediaType.APPLICATION_OCTET_STREAM) ?: false)
    }


    @Test
    fun `test downloadTemplate should return error`() {
        // Mock the dependencies
        val templateData = "Template content"
        val fileName = "template.xlsx"
        every { s3Service.downLoadFile(masterDateS3Bucket, fileName) } returns templateData.byteInputStream()

        mockkObject(DownloadWrapper)
        every { DownloadWrapper.downloadFile(fileName, MediaType.APPLICATION_OCTET_STREAM, any()) } throws(ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR))

        val responseEntity = adminMasterDataService.downloadTemplate("template")

        assertNotNull(responseEntity)
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.statusCode)
        assertNotNull(responseEntity.body)
        val baos = ByteArrayOutputStream()
        responseEntity.body?.writeTo(baos)
        baos.close()
        assertEquals("Internal Server Error", String(baos.toByteArray()))
        assertFalse(responseEntity.headers.contentType?.isCompatibleWith(MediaType.APPLICATION_OCTET_STREAM) ?: false)
    }

    @Test
    fun `test setTaxData for Corporate tax`(){

        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3

        every { excelService.setValue(sheet, 1, 1) } returns "US"
        every { excelService.setValue(sheet, 1, 2) } returns "10"

        every { excelService.setValue(sheet, 2, 1) } returns "UK"
        every { excelService.setValue(sheet, 2, 2) } returns "15"

        every { excelService.setValue(sheet, 3, 1) } returns "CA"
        every { excelService.setValue(sheet, 3, 2) } returns "20"

        every { countryTaxRepository.findAllByCountryCode("US") } returns CountryTaxEntity(1, "United States", "US", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("UK") } returns CountryTaxEntity(1, "United Kingdom", "UK", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("CA") } returns CountryTaxEntity(1, "Canada", "CA", null, null, null, null)

        every { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) } returns listOf()

        val templateType = "CORPORATE_TAX"
        val userId = 1L
        val displayName = "John Doe"
        val template = MockMultipartFile("template.xlsx", ByteArray(0))

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns masterDataEntity

        // Call the method under test
        adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, template)

        // Verify the interactions and assertions
        verify(exactly = 1) { excelService.setValue(sheet, 1, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 1, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("US") }

        verify(exactly = 1) { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) }

        verify(exactly = 1) { excelService.setValue(sheet, 2, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 2, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("UK") }

        verify(exactly = 1) { excelService.setValue(sheet, 3, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 3, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("CA") }

        verify(exactly = 1) { masterDataRepository.save(any()) }
        verify(exactly = 1) { s3Service.uploadFile(template, templateType, masterDateS3Bucket) }
    }

    @Test
    fun `test setTaxData for Employee tax`(){

        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3

        every { excelService.setValue(sheet, 1, 1) } returns "US"
        every { excelService.setValue(sheet, 1, 2) } returns "10"

        every { excelService.setValue(sheet, 2, 1) } returns "UK"
        every { excelService.setValue(sheet, 2, 2) } returns "15"

        every { excelService.setValue(sheet, 3, 1) } returns "CA"
        every { excelService.setValue(sheet, 3, 2) } returns "20"

        every { countryTaxRepository.findAllByCountryCode("US") } returns CountryTaxEntity(1, "United States", "US", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("UK") } returns CountryTaxEntity(1, "United Kingdom", "UK", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("CA") } returns CountryTaxEntity(1, "Canada", "CA", null, null, null, null)

        every { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) } returns listOf()

        val templateType = "EMPLOYEE_TAX"
        val userId = 1L
        val displayName = "John Doe"
        val template = MockMultipartFile("template.xlsx", ByteArray(0))

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns masterDataEntity

        // Call the method under test
        adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, template)

        // Verify the interactions and assertions
        verify(exactly = 1) { excelService.setValue(sheet, 1, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 1, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("US") }

        verify(exactly = 1) { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) }

        verify(exactly = 1) { excelService.setValue(sheet, 2, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 2, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("UK") }

        verify(exactly = 1) { excelService.setValue(sheet, 3, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 3, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("CA") }

        verify(exactly = 1) { masterDataRepository.save(any()) }
        verify(exactly = 1) { s3Service.uploadFile(template, templateType, masterDateS3Bucket) }
    }
    @Test
    fun `test setTaxData for Employer tax`(){

        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3

        every { excelService.setValue(sheet, 1, 1) } returns "US"
        every { excelService.setValue(sheet, 1, 2) } returns "10"

        every { excelService.setValue(sheet, 2, 1) } returns "UK"
        every { excelService.setValue(sheet, 2, 2) } returns "15"

        every { excelService.setValue(sheet, 3, 1) } returns "CA"
        every { excelService.setValue(sheet, 3, 2) } returns "20"

        every { countryTaxRepository.findAllByCountryCode("US") } returns CountryTaxEntity(1, "United States", "US", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("UK") } returns CountryTaxEntity(1, "United Kingdom", "UK", null, null, null, null)
        every { countryTaxRepository.findAllByCountryCode("CA") } returns CountryTaxEntity(1, "Canada", "CA", null, null, null, null)

        every { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) } returns listOf()

        val templateType = "EMPLOYER_TAX"
        val userId = 1L
        val displayName = "John Doe"
        val template = MockMultipartFile("template.xlsx", ByteArray(0))

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns masterDataEntity

        // Call the method under test
        adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, template)

        // Verify the interactions and assertions
        verify(exactly = 1) { excelService.setValue(sheet, 1, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 1, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("US") }

        verify(exactly = 1) { countryTaxRepository.saveAll(any<List<CountryTaxEntity>>()) }

        verify(exactly = 1) { excelService.setValue(sheet, 2, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 2, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("UK") }

        verify(exactly = 1) { excelService.setValue(sheet, 3, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 3, 2) }
        verify(exactly = 1) { countryTaxRepository.findAllByCountryCode("CA") }

        verify(exactly = 1) { masterDataRepository.save(any()) }
        verify(exactly = 1) { s3Service.uploadFile(template, templateType, masterDateS3Bucket) }
    }

    @Test
    fun `setCountryHighlightsData should save country highlights data and update master data`() {
        // Arrange
        val sheet = mockk<XSSFSheet>()
        val templateType = "country_highlights"
        val userId = 123L
        val displayName = "John Doe"
        val template = MockMultipartFile("template.xlsx", byteArrayOf())

        val highlights1 = "Highlight 1"
        val highlights2 = "Highlight 2"

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)


        every { sheet.lastRowNum } returns 2
        every { excelService.setValueWithoutTruncate(sheet, 1, 0) } returns highlights1
        every { excelService.setValueWithoutTruncate(sheet, 2, 0) } returns highlights2
        every { countryHighlightsRepository.deleteAll() } just runs
        every { countryHighlightsRepository.saveAll(any<List<CountryHighlightsEntity>>()) } returns listOf()
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns masterDataEntity
        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity


        // Act
        adminMasterDataService.setCountryHighlightsData(sheet, templateType, userId, displayName, template)

        // Assert
        verify(exactly = 1) { countryHighlightsRepository.deleteAll() }
        verify(exactly = 1) { countryHighlightsRepository.saveAll(any<List<CountryHighlightsEntity>>()) }
        verify(exactly = 1) { masterDataRepository.save(any()) }
        verify(exactly = 1) { s3Service.uploadFile(template, templateType, masterDateS3Bucket) }
    }
    @Test
    fun `test setGlobalRankingData`(){

        val c1 = CountryEntity(1L,"US", "United States", "USA", 5, 4, 5, 4, 3, 2, 5, 4, 3, 2, "+1")
        val c2 = CountryEntity(2L,"UK", "United Kingdom", "UK", 5, 4, 5, 4, 3, 2, 5, 4, 3, 2, "+1")
        val c3 = CountryEntity(3L,"CA", "Canada", "CA", 5, 4, 5, 4, 3, 2, 5, 4, 3, 2, "+1")

        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3

        every { excelService.setValue(sheet, 1, 1) } returns "US"
        every { excelService.setValue(sheet, 1, 2) } returns "10"
        every { excelService.setValue(sheet, 1, 3) } returns c1.overallRating.toString()
        every { excelService.setValue(sheet, 1, 4) } returns c1.entrepreneurshipRating.toString()
        every { excelService.setValue(sheet, 1, 5) } returns c1.openForBusinessRating.toString()
        every { excelService.setValue(sheet, 1, 6) } returns c1.qualityOfLifeRating.toString()
        every { excelService.setValue(sheet, 1, 7) } returns c1.socialPurposeRating.toString()
        every { excelService.setValue(sheet, 1, 8) } returns c1.globalTravelRiskRating.toString()
        every { excelService.setValue(sheet, 1, 9) } returns c1.gdpRating.toString()
        every { excelService.setValue(sheet, 1, 10) } returns c1.economicFreedomRating.toString()
        every { excelService.setValue(sheet, 1, 11) } returns c1.highestInflationRateRating.toString()
        every { excelService.setValue(sheet, 1, 12) } returns c1.lowestInflationRateRating.toString()
        every { excelService.setValue(sheet, 1, 13) } returns c1.dialCode.toString()

        every { excelService.setValue(sheet, 2, 1) } returns "UK"
        every { excelService.setValue(sheet, 2, 2) } returns "15"
        every { excelService.setValue(sheet, 2, 3) } returns  c2.overallRating.toString()
        every { excelService.setValue(sheet, 2, 4) } returns  c2.entrepreneurshipRating.toString()
        every { excelService.setValue(sheet, 2, 5) } returns  c2.openForBusinessRating.toString()
        every { excelService.setValue(sheet, 2, 6) } returns  c2.qualityOfLifeRating.toString()
        every { excelService.setValue(sheet, 2, 7) } returns  c2.socialPurposeRating.toString()
        every { excelService.setValue(sheet, 2, 8) } returns  c2.globalTravelRiskRating.toString()
        every { excelService.setValue(sheet, 2, 9) } returns  c2.gdpRating.toString()
        every { excelService.setValue(sheet, 2, 10) } returns c2.economicFreedomRating.toString()
        every { excelService.setValue(sheet, 2, 11) } returns c2.highestInflationRateRating.toString()
        every { excelService.setValue(sheet, 2, 12) } returns c2.lowestInflationRateRating.toString()
        every { excelService.setValue(sheet, 2, 13) } returns c2.dialCode.toString()


        every { excelService.setValue(sheet, 3, 1) } returns "CA"
        every { excelService.setValue(sheet, 3, 2) } returns "20"
        every { excelService.setValue(sheet, 3, 3) } returns  c3.overallRating.toString()
        every { excelService.setValue(sheet, 3, 4) } returns  c3.entrepreneurshipRating.toString()
        every { excelService.setValue(sheet, 3, 5) } returns  c3.openForBusinessRating.toString()
        every { excelService.setValue(sheet, 3, 6) } returns  c3.qualityOfLifeRating.toString()
        every { excelService.setValue(sheet, 3, 7) } returns  c3.socialPurposeRating.toString()
        every { excelService.setValue(sheet, 3, 8) } returns  c3.globalTravelRiskRating.toString()
        every { excelService.setValue(sheet, 3, 9) } returns  c3.gdpRating.toString()
        every { excelService.setValue(sheet, 3, 10) } returns c3.economicFreedomRating.toString()
        every { excelService.setValue(sheet, 3, 11) } returns c3.highestInflationRateRating.toString()
        every { excelService.setValue(sheet, 3, 12) } returns c3.lowestInflationRateRating.toString()
        every { excelService.setValue(sheet, 3, 13) } returns c3.dialCode.toString()


        every { countryRepository.findAllByCountryCode("US") } returns c1
        every { countryRepository.findAllByCountryCode("UK") } returns c2
        every { countryRepository.findAllByCountryCode("CA") } returns c3

        every { countryRepository.saveAll(any<List<CountryEntity>>()) } returns listOf()

        val templateType = "GLOBAL_RANKING"
        val userId = 1L
        val displayName = "John Doe"
        val template = MockMultipartFile("template.xlsx", ByteArray(0))

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns masterDataEntity

        // Call the method under test
        adminMasterDataService.setGlobalRankingData(sheet, templateType, userId, displayName, template)

        // Verify the interactions and assertions
        verify(exactly = 1) { excelService.setValue(sheet, 1, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 1, 3) }
        verify(exactly = 1) { countryRepository.findAllByCountryCode("US") }

        verify(exactly = 1) { countryRepository.saveAll(any<List<CountryEntity>>()) }

        verify(exactly = 1) { excelService.setValue(sheet, 2, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 2, 3) }
        verify(exactly = 1) { countryRepository.findAllByCountryCode("UK") }

        verify(exactly = 1) { excelService.setValue(sheet, 3, 1) }
        verify(exactly = 1) { excelService.setValue(sheet, 3, 3) }
        verify(exactly = 1) { countryRepository.findAllByCountryCode("CA") }

        verify(exactly = 1) { masterDataRepository.save(any()) }
        verify(exactly = 1) { s3Service.uploadFile(template, templateType, masterDateS3Bucket) }
    }

    @Test
    fun `getMasterData test`() {

        val list = (1..10).map { MasterDataEntity("templateType-$it","Template-$1",null,null,"","", it.toLong()) }

        every { masterDataRepository.findAll() } returns list

        val masterData = adminMasterDataService.getMasterData()
        assertNotNull(masterData)
        assertTrue(masterData.isNotEmpty())

        for (i in masterData.indices){
            assertEquals(list[i].docName, masterData[i].docName)
            assertEquals(list[i].docType, masterData[i].docType)
        }
    }
    @Test
    fun `getMasterContent test`() {
        val templateType = "TEMPLATE"
        val masterContentEntity = MasterContentEntity("VISA", "This is sample content", LocalDateTime.now(), TimeUtil.toEpochMillis(
            LocalDate.now()))

        every { masterContentRepository.findByDocType(templateType) } returns masterContentEntity
        val masterContent = adminMasterDataService.getMasterContent(templateType)
        assertNotNull(masterContent)
        assertEquals(masterContentEntity.docType, masterContent.docType)
        assertEquals(masterContentEntity.content, masterContent.content)
    }
    @Test
    fun `getAuthorities test`() {

        val list = (1..10).map { AuthoritiesEntity("key-$it", "display Name") }

        every { authoritiesRepository.findAll() } returns list

        val authoritiesEntities = adminMasterDataService.getAuthorities()
        assertNotNull(authoritiesEntities)
        assertTrue(authoritiesEntities.isNotEmpty())

        for (i in authoritiesEntities.indices){
            assertEquals(list[i].key, authoritiesEntities[i].key)
            assertEquals(list[i].displayName, authoritiesEntities[i].displayName)
        }
    }
    @Test
    fun `getEntityType test`() {

        val countryCode = "US"
        val entityType = EntityTypeEntity(1, countryCode, "Type")
        every { entityTypeRepository.findAllByCountryCode(countryCode) } returns listOf(entityType)

        val entityTypeResponse = adminMasterDataService.getEntityType(countryCode)
        assertNotNull(entityTypeResponse)
        assertTrue(entityTypeResponse.isNotEmpty())

        assertEquals(entityType.entityType, entityTypeResponse[0].entityType)
        assertEquals(countryCode, entityTypeResponse[0].countryCode)
    }
    @Test
    fun `uploadMasterContent test`() {
        val templateType = "TEMPLATE"
        val userId = 12L
        val content = "This is sample content"
        val displayName = "John Doe"

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns mockk<MasterDataEntity>()
        every { masterContentRepository.save(any<MasterContentEntity>()) } returns mockk<MasterContentEntity>()
        val response = adminMasterDataService.uploadMasterContent(templateType, userId, content, displayName)
        assertNotNull(response)
        assertEquals(templateType, response.docType)
        assertEquals(content, response.content)
        assertEquals(userId, response.lastUpdateBy)
        assertNotNull(response.lastUploadDate)
    }
    @Test
    fun `setImmigrationRequirement should save immigration requirements to the database`() {
        // Arrange
        val templateType = "IMMIGRATION_REQUIREMENTS"
        val userId = 1L
        val displayName = "John Doe"

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        val i1 = ImmigrationRequirementEntity(1L,"USA", "CA", "REQUIRED", "NOTREQUIRED", "", 5)
        val i2 = ImmigrationRequirementEntity(2L,"CA", "USA", "REQUIRED", "REQUIRED", "", 7)
        val i3 = ImmigrationRequirementEntity(3L,"USA", "IN", "NOTREQUIRED", "REQUIRED", "", 50)

        val template = MockMultipartFile("template.xlsx", ByteArray(0))
        val workbook = mockk<XSSFWorkbook>()
        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3
        every { sheet.sheetName } returns "sheet-1"
        every { workbook.numberOfSheets } returns 4
        every { workbook.getSheetAt(3) } returns sheet

        every { s3Service.uploadFile(any(), any(), any(), any(), any(), any()) } returns Unit

        listOf(i1, i2, i3).forEachIndexed {index, it->
            every { excelService.setValueWithoutTruncate(sheet, index+1, 0) } returns it.sourceCountry
            every { excelService.setValueWithoutTruncate(sheet, index+1, 2) } returns it.destCountry
            every { excelService.setValue(sheet, index+1, 4) } returns it.businessVisa
            every { excelService.setValue(sheet, index+1, 5) } returns it.workVisa
        }

        every { immigrationRequirementRepository.deleteAll() } just runs
        every { immigrationRequirementRepository.saveAll(any<List<ImmigrationRequirementEntity>>()) } returns listOf()

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns mockk<MasterDataEntity>()
        every { masterContentRepository.save(any<MasterContentEntity>()) } returns mockk<MasterContentEntity>()

        // Act
        adminMasterDataService.setImmigrationRequirement(workbook, templateType, userId, displayName, template)

        // Assert
        verify { immigrationRequirementRepository.saveAll(any<List<ImmigrationRequirementEntity>>()) }
        verify { immigrationRequirementRepository.deleteAll() }
        verify { masterDataRepository.save(any()) }
        //verify { s3Service.uploadFile(eq(template), eq(templateType), masterDateS3Bucket) }
    }
    @Test
    fun `setEntityType should save entity types to the database`() {
        // Arrange
        val templateType = "ENTITY_TYPE"
        val userId = 1L
        val displayName = "John Doe"

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        val e1 = EntityTypeEntity(1L,"US", "TYPE-1")
        val e2 = EntityTypeEntity(2L,"UK", "TYPE-2")
        val e3 = EntityTypeEntity(3L,"CA", "TYPE-1")

        val template = MockMultipartFile("template.xlsx", ByteArray(0))
        val workbook = mockk<XSSFWorkbook>()
        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3
        every { sheet.sheetName } returns "sheet-1"
        every { workbook.numberOfSheets } returns 3
        every { workbook.getSheetAt(2) } returns sheet


        listOf(e1, e2, e3).forEachIndexed {index, it->
            every { excelService.setValueWithoutTruncate(sheet, index+1, 0) } returns it.countryCode
            every { excelService.setValueWithoutTruncate(sheet, index+1, 2) } returns it.entityType
        }

        every { entityTypeRepository.deleteAll() } just runs
        every { entityTypeRepository.saveAll(any<List<EntityTypeEntity>>()) } returns listOf()

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns mockk<MasterDataEntity>()
        every { masterContentRepository.save(any<MasterContentEntity>()) } returns mockk<MasterContentEntity>()

        // Act
        adminMasterDataService.setEntityType(workbook, templateType, userId, displayName, template)

        // Assert
        verify { entityTypeRepository.saveAll(any<List<EntityTypeEntity>>()) }
        verify { entityTypeRepository.deleteAll() }
        verify { masterDataRepository.save(any()) }
        verify { s3Service.uploadFile(eq(template), eq(templateType), masterDateS3Bucket) }
    }

    @Test
    fun `setMilestoneStatusData should save milestones data to the database`() {

        val templateType = "CASE_STATUS_MILESTONE"
        val userId = 1L
        val displayName = "John Doe"

        val masterDataEntity = MasterDataEntity(templateType,"Template",null,null,"","",1)

        val template = MockMultipartFile("template.xlsx", ByteArray(0))
        val workbook = mockk<XSSFWorkbook>()
        val sheet = mockk<XSSFSheet>()
        every { sheet.lastRowNum } returns 3
        every { sheet.sheetName } returns "sheet-1"
        every { workbook.numberOfSheets } returns 1
        every { workbook.getSheetAt(0) } returns sheet

        every { excelService.setValue(any(), any(), any()) } returns  ""

        (1..3).forEach {
            every { excelService.setValue(sheet, it, 1) } returns "milestone-$it"
            every { excelService.setValue(sheet, it, 2) } returns "milestone-key-$it"
            every { excelService.setValue(sheet, it, 3) } returns "status-text-$it"
            every { excelService.setValue(sheet, it, 4) } returns "status-$it"
            every { excelService.setValue(sheet, it, 5) } returns (it*10).toString()
            every { excelService.setValue(sheet, it, 0) } returns it.toString()
            every { excelService.setValue(sheet, it, 6) } returns "action-for"
            every { excelService.setValue(sheet, it, 7) } returns (it*10).toString()
        }


        every { milestonesRepository.deleteAll() } just runs
        every { caseStatusMasterRepository.deleteAll() } just runs
        every { milestonesRepository.saveAll(any<List<MilestonesEntity>>()) } returns listOf()
        every { caseStatusMasterRepository.saveAll(any<List<CaseStatusMasterEntity>>()) } returns listOf()

        every { masterDataRepository.findByDocType(templateType) } returns masterDataEntity
        every { masterDataRepository.save(any<MasterDataEntity>()) } returns mockk<MasterDataEntity>()
        every { masterContentRepository.save(any<MasterContentEntity>()) } returns mockk<MasterContentEntity>()

        // Act
        adminMasterDataService.setMilestoneStatusData(workbook, templateType, userId, displayName, template)

        // Assert
        verify { milestonesRepository.saveAll(any<List<MilestonesEntity>>()) }
        verify { caseStatusMasterRepository.saveAll(any<List<CaseStatusMasterEntity>>()) }
        verify { milestonesRepository.deleteAll() }
        verify { caseStatusMasterRepository.deleteAll() }
        verify { masterDataRepository.save(any()) }
        verify { s3Service.uploadFile(eq(template), eq(templateType), masterDateS3Bucket) }
    }

}
