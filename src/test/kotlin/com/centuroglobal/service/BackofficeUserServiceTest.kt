package com.centuroglobal.service
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.ValidationType
import com.centuroglobal.shared.data.payload.account.CreateAdminUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateAdminUserRequest
import com.centuroglobal.shared.data.pojo.AdminAuthorities
import com.centuroglobal.shared.data.pojo.AdminUserSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AdminAuthoritiesRepository
import com.centuroglobal.shared.repository.BackofficeUserRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.repository.findByIdOrNull
import java.util.*
class BackofficeUserServiceTest {
    private val backofficeUserRepository: BackofficeUserRepository = mockk()
    private val corporateUserRepository: CorporateUserRepository = mockk()
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository = mockk()
    private val tokenVerificationService: TokenVerificationService = mockk()
    private val s3Service: AwsS3Service = mockk()
    private val corporateName = "Test Company"
    private val backofficeUserService = BackofficeUserService(
        backofficeUserRepository,
        adminAuthoritiesRepository,
        tokenVerificationService,
        s3Service
    )
    private val corporateUserEntity: CorporateUserEntity = mockk()
    private val corporateEntity: CorporateEntity = mockk()
    private val authenticatedUser: AuthenticatedUser = mockk()
    @BeforeEach
    fun setup(){
        every { authenticatedUser.userId } returns LOGGED_IN_USER_ID
        every { authenticatedUser.companyId } returns LOGGED_IN_USER_CORPORATE_ID
        every { authenticatedUser.email } returns LOGGED_IN_USER_EMAIL
        every { corporateUserEntity.id } returns LOGGED_IN_USER_ID
        every { corporateUserEntity.corporate } returns corporateEntity
        every { corporateEntity.name } returns corporateName
        every { corporateEntity.id } returns LOGGED_IN_USER_CORPORATE_ID
        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.of(corporateUserEntity)
    }
    @Test
    fun `retrieve Backoffice Users` () {
        val filter = AdminUserSearchFilter.Builder.build("admin", Role.ROLE_ADMIN.name, AccountStatus.ACTIVE.name, null)
        val users = (1..5).map { BackofficeUserEntity(true) }
        users.forEach {
            every { adminAuthoritiesRepository.findAllByUserIdAndHasAccess(it.id!!, true) } returns mutableListOf(AdminAuthoritiesEntity(it.id!!, it.id!!, "READ_WRITE", true))
        }
        every { backofficeUserRepository.searchByCriteria(filter) } returns users
        val result = backofficeUserService.retrieveBackofficeUsers(filter)
        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        result.forEach {
            assertTrue(users.map { it1->it1.id }.contains(it.id))
        }
    }
    @Test
    fun `retrieve Backoffice User by user Id` () {
        val userId = 15L
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        every { backofficeUserRepository.findByIdOrNull(userId) } returns userEntity
        every { adminAuthoritiesRepository.findAllByUserIdAndHasAccess(userId, true) }returns mutableListOf(AdminAuthoritiesEntity(1, userId, "READ_WRITE", true))
        every { adminAuthoritiesRepository.findAllByUserId(userId) } returns mutableListOf(AdminAuthoritiesEntity(1, userId, "READ_WRITE", true))
        val result = backofficeUserService.retrieveBackofficeUser(userId)
        assertNotNull(result)
        assertEquals(userId, result.id)
    }
    @Test
    fun `retrieve Backoffice User with invalid user Id` () {
        val userId = 15L
        every { backofficeUserRepository.findByIdOrNull(userId) } returns null
        assertThrows<ApplicationException> {
            backofficeUserService.retrieveBackofficeUser(userId)
        }
    }
    @Test
    fun `update Backoffice User` () {
        val userId = 15L
        val loggedInUserId = authenticatedUser.userId
        val request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(
                AdminAuthorities("CASE", true, "READ"),
                AdminAuthorities("CASE", true, "WRITE")
            ),
            aiMessageCount = 1234
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        userEntity.status = AccountStatus.ACTIVE
        userEntity.userRoles = mutableListOf(UserRoleEntity(
            id = 3,
            role = Role.ROLE_ADMIN,
            user = mockk()
        ))
        every { backofficeUserRepository.findByIdOrNull(userId) } returns userEntity
        every { adminAuthoritiesRepository.findAllByUserId(userId) } returns mutableListOf(AdminAuthoritiesEntity(1, userId, "READ_WRITE", true))
        every { adminAuthoritiesRepository.saveAll(any<List<AdminAuthoritiesEntity>>()) } returns listOf()
        every { backofficeUserRepository.save(any()) } returns userEntity
        val result = backofficeUserService.updateBackofficeUser(userId, loggedInUserId, request)
        assertNotNull(result)
        assertEquals(userEntity.role, result.role)
        assertEquals(userEntity.firstName, result.firstName)
        assertEquals(userEntity.lastName, result.lastName)
    }
    @Test
    fun `update Backoffice User with invalid user id` () {
        val userId = 99999L
        val loggedInUserId = authenticatedUser.userId
        val request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE")),
            aiMessageCount = 1234
        )
        every { backofficeUserRepository.findByIdOrNull(userId) } returns null
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, loggedInUserId, request)
        }
    }
    @Test
    fun `update Backoffice User with invalid status` () {
        val userId = 15L
        val loggedInUserId = authenticatedUser.userId
        var request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE")),
            aiMessageCount = 1234
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        userEntity.status = AccountStatus.DELETED
        every { backofficeUserRepository.findByIdOrNull(userId) } returns userEntity
        // status is deleted
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, loggedInUserId, request)
        }
        // status is pending verification
        userEntity.status = AccountStatus.PENDING_VERIFICATION
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, loggedInUserId, request)
        }
        // status is suspended
        userEntity.status = AccountStatus.ACTIVE
        request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.SUSPENDED.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE")),
            aiMessageCount = 1234
        )
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, userId, request)
        }
    }
    @Test
    fun `update Backoffice User with corporate role` () {
        val userId = 15L
        val request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_CORPORATE.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE")),
            aiMessageCount = 1234
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        userEntity.status = AccountStatus.ACTIVE
        every { backofficeUserRepository.findByIdOrNull(userId) } returns userEntity
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, userId, request)
        }
    }
    @Test
    fun `update Backoffice User throws Exception on unexpected error` () {
        val userId = 15L
        val loggedInUserId = authenticatedUser.userId
        val request = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE")),
            aiMessageCount = 1234
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        userEntity.status = AccountStatus.ACTIVE
        every { backofficeUserRepository.findByIdOrNull(userId) } returns userEntity
        every { adminAuthoritiesRepository.findAllByUserId(userId) } returns mutableListOf(AdminAuthoritiesEntity(1, userId, "READ_WRITE", true))
        every { adminAuthoritiesRepository.saveAll(any<List<AdminAuthoritiesEntity>>()) } returns listOf()
        every { backofficeUserRepository.save(any()) } throws Exception()
        assertThrows<ApplicationException> {
            backofficeUserService.updateBackofficeUser(userId, loggedInUserId, request)
        }
    }
    @Test
    fun `create Backoffice User` () {
        val userId = 15L
        val request = CreateAdminUserRequest(
            "<EMAIL>",
            "John",
            "Doe",
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE"))
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = request.email
        userEntity.role = Role.valueOf(request.role)
        userEntity.status = AccountStatus.ACTIVE
        every { backofficeUserRepository.save(any()) } returns userEntity
        every { adminAuthoritiesRepository.saveAll(any<List<AdminAuthoritiesEntity>>()) } returns listOf()
        every { tokenVerificationService.createToken(any(), ValidationType.INVITE_BACKOFFICE) } returns Unit
        val result = backofficeUserService.createBackofficeUser(authenticatedUser.userId, request)
        assertNotNull(result)
        assertEquals(userEntity.role, result.role)
        assertEquals(userEntity.firstName, result.firstName)
        assertEquals(userEntity.lastName, result.lastName)
        assertEquals(userEntity.email, result.email)
    }
    @Test
    fun `create Backoffice User with duplicate email` () {
        val userId = 15L
        val request = CreateAdminUserRequest(
            "<EMAIL>",
            "John",
            "Doe",
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE"))
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = request.email
        userEntity.role = Role.valueOf(request.role)
        userEntity.status = AccountStatus.ACTIVE
        every { backofficeUserRepository.save(any()) } throws DataIntegrityViolationException("Email already exists")
        assertThrows<ApplicationException> {
            backofficeUserService.createBackofficeUser(authenticatedUser.userId, request)
        }
    }
    @Test
    fun `create Backoffice User throws exception when unexpected error occurs` () {
        val userId = 15L
        val request = CreateAdminUserRequest(
            "<EMAIL>",
            "John",
            "Doe",
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE"))
        )
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = request.email
        userEntity.role = Role.valueOf(request.role)
        userEntity.status = AccountStatus.ACTIVE
        every { backofficeUserRepository.save(any()) } throws Exception()
        assertThrows<ApplicationException> {
            backofficeUserService.createBackofficeUser(authenticatedUser.userId, request)
        }
    }
    @Test
    fun `resend verification email` () {
        val userId = 15L
        val userEntity = BackofficeUserEntity()
        userEntity.id = userId
        userEntity.email = "<EMAIL>"
        userEntity.role = Role.ROLE_ADMIN
        userEntity.status = AccountStatus.PENDING_VERIFICATION
        every { backofficeUserRepository.findByIdAndStatus(userId, AccountStatus.PENDING_VERIFICATION) } returns userEntity
        every { tokenVerificationService.resend(userId, ValidationType.INVITE_BACKOFFICE) } returns AppConstant.SUCCESS_RESPONSE_STRING
        val result = backofficeUserService.resend(userId)
        assertNotNull(result)
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
    }
    @Test
    fun `resend verification email for already active user` () {
        val userId = 15L
        every { backofficeUserRepository.findByIdAndStatus(userId, AccountStatus.PENDING_VERIFICATION) } returns null
        assertThrows<ApplicationException> {
            backofficeUserService.resend(userId)
        }
    }
}