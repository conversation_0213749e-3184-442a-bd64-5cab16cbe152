package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.subscription.WorkLogEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.subscription.WorkLogRequest
import com.centuroglobal.shared.data.pojo.subscription.WorkLogSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.RfpRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.repository.subscription.WorkLogRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.UserProfileUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import junit.framework.TestCase.assertNotNull
import junit.framework.TestCase.assertTrue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class WorkLogServiceTest {

    private lateinit var workLogService: WorkLogService
    private lateinit var corporateRepository: CorporateRepository
    private lateinit var workLogRepository: WorkLogRepository
    private lateinit var caseRepository: CaseRepository
    private lateinit var queryRepository: QueryRepository
    private lateinit var rfpRepository: RfpRepository
    private lateinit var userProfileUtil: UserProfileUtil
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        corporateRepository = mockk()
        workLogRepository = mockk()
        caseRepository = mockk()
        queryRepository = mockk()
        rfpRepository = mockk()
        userProfileUtil = mockk()
        authenticatedUser = mockk()

        workLogService = WorkLogService(
            corporateRepository,
            workLogRepository,
            caseRepository,
            queryRepository,
            rfpRepository,
            userProfileUtil
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `create should save work log and return its ID`() {
        val request = WorkLogRequest(
            corporateId = 1L,
            description = "Test Description",
            eventDate = 123456789L,
            referenceId = 1L,
            referenceType = ReferenceType.CASE,
            timeSpent = 120
        )
        val corporate = mockk<CorporateEntity>()
        val workLogEntity = mockk<WorkLogEntity>(relaxed = true) {
            every { id } returns 1L
        }

        every { corporateRepository.getReferenceById(request.corporateId) } returns corporate
        every { workLogRepository.save(any()) } returns workLogEntity

        val result = workLogService.create(request, 1L)

        assertEquals(1L, result);
        verify { workLogRepository.save(any()) }
    }

    @Test
    fun `create should throw ApplicationException when referenceId is null for non-OTHER referenceType`() {
        val request = WorkLogRequest(
            corporateId = 1L,
            description = "Test Description",
            eventDate = 123456789L,
            referenceId = null,
            referenceType = ReferenceType.CASE,
            timeSpent = 120
        )

        every { corporateRepository.getReferenceById(any()) } returns mockk<CorporateEntity>()

        val exception = assertThrows<ApplicationException> {
            workLogService.create(request, 1L)
        }
        assertEquals(ErrorCode.BAD_REQUEST, exception.error)
    }

    @Test
    fun `list should return paged result of work logs`() {
        val filter = WorkLogSearchFilter(
            corporateId = 3,
            referenceId = 3,
            referenceType = ReferenceType.CASE,
            from = LocalDate.now().minusDays(5),
            to = LocalDate.now(),
            loggedBy = 8
        )
        val pageRequest = PageRequest.of(0, 10)
        val workLogEntity = mockk<WorkLogEntity>(relaxed = true)
        val page = PageImpl(listOf(workLogEntity))

        every { workLogRepository.searchByCriteria(filter, pageRequest) } returns page
        every { userProfileUtil.retrieveProfile(any()) } returns mockk()

        every { authenticatedUser.role } returns Role.ROLE_ADMIN.toString()
        every { authenticatedUser.userId } returns 5

        val result = workLogService.list(filter, pageRequest, authenticatedUser)

        assertNotNull(result)
        verify { workLogRepository.searchByCriteria(filter, pageRequest) }
    }

    @Test
    fun `delete should mark work log as deleted when allowed`() {
        val workLogEntity = mockk<WorkLogEntity>(relaxed = true) {
            every { isDeleted } returns false
            every { createdBy } returns 1L
            every { createdDate } returns LocalDateTime.now()
        }

        every { workLogRepository.findById(any()) } returns Optional.of(workLogEntity)
        every { workLogRepository.save(any()) } returns workLogEntity
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_USER.name

        val result = workLogService.delete(1L, authenticatedUser)

        assertTrue(result)
        verify { workLogRepository.save(workLogEntity) }
    }

    @Test
    fun `delete should throw ApplicationException when not allowed`() {
        val workLogEntity = mockk<WorkLogEntity>(relaxed = true) {
            every { isDeleted } returns false
            every { createdBy } returns 2L
            every { createdDate } returns LocalDateTime.now()
        }

        every { workLogRepository.findById(any()) } returns Optional.of(workLogEntity)
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_USER.name

        val exception = assertThrows<ApplicationException> {
            workLogService.delete(1L, authenticatedUser)
        }
        assertEquals(ErrorCode.FORBIDDEN, exception.error)
    }

    @Test
    fun `usageStats should return work log corporate stats`() {
        val corporateId = 1L
        val timeSpent = 500L

        every { workLogRepository.totalTimeSpentByCorporate(corporateId) } returns timeSpent

        val result = workLogService.usageStats(corporateId, authenticatedUser)

        assertEquals(1000L, result.timeAllocated)
        assertEquals(timeSpent, result.timeSpent)
        assertEquals(500L, result.timeRemaining)
    }

    @Test
    fun `referenceIdList should return list of reference IDs for CASE type`() {
        val corporateId = 1L
        val caseEntity = mockk<CaseEntity>(relaxed = true) {
            every { id } returns 1L
        }

        every { caseRepository.findByCreatedById(corporateId) } returns listOf(caseEntity)

        val result = workLogService.referenceIdList(ReferenceType.CASE, corporateId, authenticatedUser)

        assertEquals(listOf(1L), result)
    }

    @Test
    fun `extractOwnerUserIdAndDate should return owner user ID and date for CASE type`() {
        val payload = WorkLogRequest(
            corporateId = 1L,
            description = "Test Description",
            eventDate = 123456789L,
            referenceId = 1L,
            referenceType = ReferenceType.CASE,
            timeSpent = 120
        )
        val caseEntity = mockk<CaseEntity>(relaxed = true) {
            every { createdBy } returns mockk { every { id } returns 1L }
            every { createdDate } returns LocalDateTime.now()
        }

        every { caseRepository.findById(payload.referenceId!!) } returns Optional.of(caseEntity)

        val result = workLogService.extractOwnerUserIdAndDate(payload)

        assertEquals(1L, result.first)
        assertNotNull(result.second)
    }
}
