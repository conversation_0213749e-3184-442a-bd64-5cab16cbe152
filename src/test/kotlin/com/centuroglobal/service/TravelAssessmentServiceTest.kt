package com.centuroglobal.service

import com.centuroglobal.service.travel.TravelHistoryService
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.CorporateUserManagerEntity
import com.centuroglobal.shared.data.entity.FeatureSessionEntity
import com.centuroglobal.shared.data.entity.NotificationPreferencesEntity
import com.centuroglobal.shared.data.entity.PassportVisaEntity
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.entity.travel.TravelAssessmentEntity
import com.centuroglobal.shared.data.entity.travel.TravelAssessmentTrackingEntity
import com.centuroglobal.shared.data.entity.travel.TravelHistoryEntity
import com.centuroglobal.shared.data.entity.travel.view.TravelAssessmentLogView
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.BandStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.travel.TravelAssessmentStatus
import com.centuroglobal.shared.data.enums.travel.TravelStayUnit
import com.centuroglobal.shared.data.payload.travel.AssessmentDossierGenerateRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentLogsSearchFilter
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentSearchFilter
import com.centuroglobal.shared.data.pojo.travel.TravelHistoryResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.repository.FeatureSessionRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.PartnerRepository
import com.centuroglobal.shared.repository.PassportVisaRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentLogViewRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentTrackingRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.util.ReflectionTestUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class TravelAssessmentServiceTest {

    private val travelAssessmentTrackingRepository: TravelAssessmentTrackingRepository = mockk()
    private val travelAssessmentRepository: TravelAssessmentRepository = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val corporateRepo: CorporateRepository = mockk()
    private val corporateUserRepo: CorporateUserRepository = mockk()
    private val expertUserRepo: ExpertUserRepository = mockk()
    private val partnerRepo: PartnerRepository = mockk()
    private val partnerUserRepo: LoginAccountRepository = mockk()
    private val caseRepository: CaseRepository = mockk()
    private val featureSessionRepository: FeatureSessionRepository = mockk()
    private val travelHistoryService: TravelHistoryService = mockk()
    private val passportVisaRepository: PassportVisaRepository = mockk()
    private val pyApiClient: PythonApiClient = mockk()
    private val travelAssessmentLogViewRepository: TravelAssessmentLogViewRepository = mockk()

    private lateinit var travelAssessmentService: TravelAssessmentService

    private val corporateEntity = CorporateEntity(
        1,
        "String",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1L,
        listOf(),
        listOf(),
        1L)
    private val accountEntity = AccountEntity(
        corporateEntity.id,
        "accountEntity",
        AccountStatus.PENDING_VERIFICATION,
        null,
        null,
        corporateEntity
    )
    private val bandsEntity = BandsEntity(
        corporateEntity.id,
        "bands1",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null
    )
    private val corporateUserEntity = CorporateUserEntity(
        corporateEntity,
        setOf(accountEntity),
        "jobTitle",
        false,
        listOf(),
        bandsEntity,
        mutableListOf(
            NotificationPreferencesEntity(
            1L, NotificationType.CASE_GCHAT_EMAIL, true,
            corporateUser = CorporateUserEntity()
        )
        ),
        null)

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {

        travelAssessmentService = TravelAssessmentService(
            travelAssessmentRepository = travelAssessmentRepository,
            travelAssessmentTrackingRepository = travelAssessmentTrackingRepository,
            userProfileUtil = userProfileUtil,
            objectMapper = ObjectMapper(),
            pyApiClient = pyApiClient,
            travelHistoryService = travelHistoryService,
            passportVisaRepository = passportVisaRepository,
            caseRepository = caseRepository,
            travelAssessmentLogViewRepository = travelAssessmentLogViewRepository,
            featureSessionRepository = featureSessionRepository
        )

        // Initialize BaseService dependencies using ReflectionTestUtils
        ReflectionTestUtils.setField(travelAssessmentService, "corporateRepo", corporateRepo)
        ReflectionTestUtils.setField(travelAssessmentService, "corporateUserRepo", corporateUserRepo)
        ReflectionTestUtils.setField(travelAssessmentService, "expertUserRepo", expertUserRepo)
        ReflectionTestUtils.setField(travelAssessmentService, "partnerRepo", partnerRepo)
        ReflectionTestUtils.setField(travelAssessmentService, "partnerUserRepo", partnerUserRepo)
        val sessionEntity = mockk<FeatureSessionEntity>()
        every { sessionEntity.sessionId } returns UUID.randomUUID().toString()
        every { featureSessionRepository.save(any()) } returns sessionEntity

        corporateUserEntity.id = 1L

        // Setup common authenticatedUser mocks
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.companyId } returns 1L
        every { authenticatedUser.email } returns "<EMAIL>"
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
    }

    @Test
    fun `listTravelAssessmentTracking should return empty list when no tracking exists`() {
        // Arrange
        val assessmentId = 1L
        every { travelAssessmentTrackingRepository.findByAssessmentId(assessmentId) } returns emptyList()

        // Act
        val result = travelAssessmentService.listTravelAssessmentTracking(assessmentId)

        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `listTravelAssessmentTracking should return tracking details when tracking exists`() {
        // Arrange
        val assessmentId = 1L
        val createdDate = LocalDateTime.now()
        val updatedDate = LocalDateTime.now()
        val createdById = 1L
        val updatedById = 2L

      val assessmentEntity = TravelAssessmentEntity(
          id = assessmentId,
          originCountry = "US",
          destinationCountry = "UK",
          arrival = LocalDateTime.now(),
          departure = LocalDateTime.now(),
          periodOfStay = 10,
          stayUnit = TravelStayUnit.DAYS,
          purpose = "test purpose",
          data = "test data",
          status = TravelAssessmentStatus.IN_PROGRESS,
          user = corporateUserEntity,
          partner = mockk(),
          responses = mutableListOf(),
          sessionId = UUID.randomUUID().toString(),
          visaType = "test visa type",
          visaName = "test visa name",
          creatorRole = "test creator role",
      )

        val trackingEntity = TravelAssessmentTrackingEntity(
            id = assessmentId,
            step = "STEP_1",
            request = "test request",
            response = "test response",
            assessment = assessmentEntity
        )

            trackingEntity.createdDate = createdDate
            trackingEntity.lastUpdatedDate = updatedDate
            trackingEntity.createdBy = createdById
            trackingEntity.updatedBy = updatedById

        val userProfile = UserProfile(1, "<EMAIL>", "test", "user", AccountStatus.ACTIVE, Role.ROLE_CORPORATE, null, null, null)

        every { travelAssessmentTrackingRepository.findByAssessmentId(assessmentId) } returns listOf(trackingEntity)
        every { userProfileUtil.retrieveProfile(createdById) } returns userProfile
        every { userProfileUtil.retrieveProfile(updatedById) } returns userProfile

        // Act
        val result = travelAssessmentService.listTravelAssessmentTracking(assessmentId)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.size)
        
        val trackingDetails = result[0]
        assertEquals(trackingEntity.id, trackingDetails.id)
        assertEquals(trackingEntity.step, trackingDetails.step)
        assertEquals(trackingEntity.request, trackingDetails.request)
        assertEquals(trackingEntity.response, trackingDetails.response)
        assertEquals(TimeUtil.toEpochMillis(createdDate), trackingDetails.createdDate)
        assertEquals(TimeUtil.toEpochMillis(updatedDate), trackingDetails.updatedDate)
        assertEquals(userProfile, trackingDetails.createdBy)
        assertEquals(userProfile, trackingDetails.updatedBy)
    }

    @Test
    fun `listTravelAssessmentTracking should handle null created and updated by users`() {
        // Arrange
        val assessmentId = 1L
        val trackingEntity = TravelAssessmentTrackingEntity(
            id = 1L,
            step = "STEP_1",
            request = "test request",
            response = "test response",
            assessment = mockk()
        )

        every { travelAssessmentTrackingRepository.findByAssessmentId(assessmentId) } returns listOf(trackingEntity)

        // Act
        val result = travelAssessmentService.listTravelAssessmentTracking(assessmentId)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.size)
        
        val trackingDetails = result[0]
        assertNull(trackingDetails.createdBy)
        assertNull(trackingDetails.updatedBy)
    }

    @Test
    fun `get should return assessment data when assessment is completed`() {
        // Arrange
        val assessmentData = "{\"key\":\"ENTRY_VISA\",\"message\":{\"status\":\"PROCESSING_END\",\"step\":\"VISA_OPTION_VALIDATE\",\"result\":\"SUCCESS\",\"data\":{\"metadata\":{\"assessment_for\":\"MYSELF\",\"applicant\":\"14\",\"origin_country\":\"IN\",\"destination_country\":\"BD\",\"arrival_date\":\"2024-12-26\",\"departure_date\":\"2025-09-12\",\"period_of_stay\":3,\"stay_unit\":\"DAYS\",\"purpose_of_travel\":[\"FAMILIARISATION_ACTIVITIES\",\"DESIGN_AND_DEVELOPMEN\"],\"name\":\"Corporate 5\",\"job_title\":\"CEO\"},\"summary\":\"For Corporate's trip to Bangladesh, a short-term entry visa is the most suitable option. This visa aligns with the brief duration of stay (3 days) and the purpose of travel, which includes familiarization activities and design and development. Given Corporate's German nationality and the fact that he holds a valid Indian visa, he is eligible to apply for a short-term entry visa to Bangladesh.\",\"process\":[{\"title\":\"Step 1: Gather Required Documents\",\"description\":\"Collect all necessary documents including a valid passport, recent passport-sized photographs, proof of travel itinerary, and any supporting documents related to the purpose of travel.\"},{\"title\":\"Step 2: Complete the Visa Application Form\",\"description\":\"Fill out the Bangladesh visa application form accurately. Ensure all details match the information on your passport and other documents.\"},{\"title\":\"Step 3: Submit Application\",\"description\":\"Submit the completed application form along with the required documents to the nearest Bangladesh Embassy or Consulate. This can often be done in person or via a visa application center.\"},{\"title\":\"Step 4: Pay Visa Fee\",\"description\":\"Pay the applicable visa fee. The fee amount and payment method will be specified by the Bangladesh Embassy or Consulate.\"},{\"title\":\"Step 5: Attend Interview (if required)\",\"description\":\"Some applicants may be required to attend an interview. If so, attend the interview at the scheduled time and provide any additional information requested.\"},{\"title\":\"Step 6: Wait for Visa Processing\",\"description\":\"After submission, wait for the visa to be processed. Processing times can vary, so it's important to apply well in advance of your travel date.\"},{\"title\":\"Step 7: Collect Visa\",\"description\":\"Once the visa is approved, collect your passport with the visa stamp from the embassy, consulate, or visa application center.\"}],\"timeline\":[{\"title\":\"Document Preparation\",\"description\":\"1-2 days to gather and prepare all necessary documents.\"},{\"title\":\"Application Submission\",\"description\":\"1 day to submit the application and documents.\"},{\"title\":\"Visa Processing\",\"description\":\"7-10 days for the visa to be processed by the Bangladesh authorities.\"},{\"title\":\"Visa Collection\",\"description\":\"1 day to collect the visa once approved.\"}],\"eta_in_days\":14,\"recommended_visa\":\"Short-term Entry Visa\",\"travel_possible\":false,\"potential_start_date\":\"2025-01-03\",\"session_id\":\"fcf524de-fec6-41c9-9db8-55a13b4a8326\",\"assessment_id\":1}}}"
        val assessmentId = 1L
        val authenticatedUser: AuthenticatedUser = mockk()
        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            status = TravelAssessmentStatus.COMPLETE,
            data = assessmentData,
            user = corporateUserEntity,
            createdByUser = corporateUserEntity,
            originCountry = "AU",
            destinationCountry = "US",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(10),
            periodOfStay = 10,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "test purpose",
            sessionId = UUID.randomUUID().toString(),
            visaType = "test visa type",
            visaName = "test visa name",
            creatorRole = "test creator role"
        )

        val caseEntity = mockk<CaseEntity>()

        every { travelAssessmentRepository.findById(assessmentId) } returns Optional.of(assessment)
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { authenticatedUser.companyId } returns 1L
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { authenticatedUser.userId } returns 1L
        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
        every { caseRepository.findByAssessmentId(assessmentId) } returns caseEntity
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(
            assessment
        )
        every { caseEntity.id } returns 1L

        // Act
        val result = travelAssessmentService.get(assessmentId, authenticatedUser, UUID.randomUUID().toString())

        // Assert
        assertNotNull(result)
        assertEquals(assessmentId, ((result["message"] as Map<*, *>)["data"] as Map<*, *>)["assessment_id"].toString().toLong())
    }

    @Test
    fun `get should return empty map when assessment status is IN_PROGRESS`() {
        // Arrange
        val assessmentId = 1L
        val authenticatedUser: AuthenticatedUser = mockk()
        val corporateUserEntity = mockk<CorporateUserEntity>()
        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            status = TravelAssessmentStatus.IN_PROGRESS,
            data = "test data",
            user = corporateUserEntity,
            createdByUser = corporateUserEntity,
            originCountry = "AU",
            destinationCountry = "US",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(10),
            periodOfStay = 10,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "test purpose",
            sessionId = UUID.randomUUID().toString(),
            visaType = "test visa type",
            visaName = "test visa name",
            creatorRole = "test creator role"
        )

        every { travelAssessmentRepository.findById(assessmentId) } returns Optional.of(assessment)
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { authenticatedUser.companyId } returns 1L
        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)

        // Act
        assertThrows<ApplicationException> {
            travelAssessmentService.get(assessmentId, authenticatedUser, null)
        }
    }

    @Test
    fun `listTravelAssessments should return paged result when search filter is valid`() {
        // Arrange
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 1L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        val searchFilter = TravelAssessmentSearchFilter(
            originCountry = "US",
            destinationCountry = "UK",
            fromDate = LocalDateTime.now().minusDays(30),
            toDate = LocalDateTime.now()
        )

        val pageRequest = PageRequest.of(0, 10)

        val corporateUserEntity = mockk<CorporateUserEntity> {
            every { id } returns 1L
            every { firstName } returns "John"
            every { lastName } returns "Doe"
            every { corporate } returns mockk {
                every { name } returns "Test Corp"
            }
        }

        val assessment = TravelAssessmentEntity(
            id = 1L,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(10),
            periodOfStay = 10,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            user = corporateUserEntity,
            createdByUser = corporateUserEntity,
            status = TravelAssessmentStatus.COMPLETE,
            data = "{}",
            sessionId = UUID.randomUUID().toString(),
            visaType = "visa type",
            visaName = "visa name",
            creatorRole = "creator role"
        )

        val pagedAssessments = PageImpl(listOf(assessment), pageRequest, 1)

        every { corporateRepo.findIdById(1L) } returns mockk()
        every { travelAssessmentRepository.searchByCriteriaForFullAccess(any(), pageRequest) } returns pagedAssessments
        every { userProfileUtil.retrieveProfile(1L, "Test Corp") } returns mockk()

        // Act
        val result = travelAssessmentService.listTravelAssessments(searchFilter, pageRequest, authenticatedUser)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.totalElements)
        assertEquals(1, result.rows.size)

        val firstResult = result.rows[0]
        assertEquals(1L, firstResult.id)
        assertEquals("US", firstResult.originCountry)
        assertEquals("UK", firstResult.destinationCountry)
        assertEquals(10, firstResult.periodOfStay)
        assertEquals("DAYS", firstResult.stayUnit)
        assertEquals(listOf("Business"), firstResult.purpose)
        assertEquals("John Doe", firstResult.createdBy)
    }

    @Test
    fun `listTravelAssessments should return empty paged result when no assessments found`() {
        // Arrange
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 1L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        val searchFilter = TravelAssessmentSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        val emptyPage = PageImpl<TravelAssessmentEntity>(
            emptyList(),
            pageRequest,
            0
        )

        every { corporateRepo.findIdById(1L) } returns mockk()
        every { travelAssessmentRepository.searchByCriteriaForFullAccess(any(),  pageRequest) } returns emptyPage

        // Act
        val result = travelAssessmentService.listTravelAssessments(searchFilter, pageRequest, authenticatedUser)

        // Assert
        assertNotNull(result)
        assertEquals(0, result.totalElements)
        assertTrue(result.rows.isEmpty())
    }

    @Test
    fun `listTravelAssessments should set loggedInUserCorporateId in search filter`() {
        // Arrange
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 123L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        val searchFilter = TravelAssessmentSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        val emptyPage = PageImpl<TravelAssessmentEntity>(
            emptyList(),
            pageRequest,
            0
        )

        every { corporateRepo.findIdById(123L) } returns mockk()
        every { travelAssessmentRepository.searchByCriteriaForFullAccess(any(), pageRequest) } returns emptyPage

        // Act
        travelAssessmentService.listTravelAssessments(searchFilter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(123L, searchFilter.loggedInUserCorporateId)
    }

    @Test
    fun `delete should successfully delete assessment when it exists and user has access`() {
        // Arrange
        val assessmentId = 1L
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 1L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        val assessment = mockk<TravelAssessmentEntity>()

        every { assessment.status } returns TravelAssessmentStatus.COMPLETE
        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)

        every { travelAssessmentRepository.delete(assessment) } just runs

        // Act
        val result = travelAssessmentService.delete(assessmentId, authenticatedUser)

        // Assert
        assertTrue(result)
        verify(exactly = 1) {
            travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L)
            travelAssessmentRepository.delete(assessment)
        }
    }

    @Test
    fun `delete should throw ApplicationException when assessment is in progress`() {
        // Arrange
        val assessmentId = 1L
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 1L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        val assessment = mockk<TravelAssessmentEntity> {
            every { status } returns TravelAssessmentStatus.IN_PROGRESS
        }

        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)


        // Act & Assert
        assertThrows<ApplicationException> {
            travelAssessmentService.delete(assessmentId, authenticatedUser)
        }.also { exception ->
            assertEquals(ErrorCode.NOT_FOUND, exception.error)
        }

        verify(exactly = 0) {
            travelAssessmentRepository.delete(any())
        }
    }

    @Test
    fun `delete should throw ApplicationException when assessment does not exist`() {
        // Arrange
        val assessmentId = 1L
        val authenticatedUser: AuthenticatedUser = mockk {
            every { userId } returns 1L
            every { userType } returns UserType.CORPORATE.name
            every { companyId } returns 1L
            every { role } returns "ROLE_USER"
            every { visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        }

        every { corporateRepo.findIdById(1L) } returns EntityIdDto(1L)
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.empty()

        // Act & Assert
        assertThrows<ApplicationException> {
            travelAssessmentService.delete(assessmentId, authenticatedUser)
        }

        verify(exactly = 0) {
            travelAssessmentRepository.delete(any())
        }
    }

    @Test
    fun `generateCase should successfully generate case with default case type`() {
        // Arrange
        val assessmentId = 1L

        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(30),
            periodOfStay = 30,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            data = """
                {
                    "key": "BUSINESS_VISA",
                    "message": {
                        "data": {
                            "metadata": {}
                        }
                    }
                }
            """.trimIndent(),
            status = TravelAssessmentStatus.COMPLETE,
            user = corporateUserEntity,
            partner = null,
            responses = mutableListOf(),
            sessionId = UUID.randomUUID().toString(),
            visaType = "Business",
            visaName = "UK Business Visa",
            creatorRole = "CORPORATE"
        )

        val travelHistory = PagedResult(
            listOf(
                TravelHistoryResponse(
                    id = 1L,
                    destinationCountry = "FR",
                    originCountry = "US",
                    periodOfStay = 5,
                    stayUnit = TravelStayUnit.DAYS,
                    arrival = TimeUtil.toEpochMillis(LocalDateTime.now().minusMonths(1)),
                    departure = TimeUtil.toEpochMillis(LocalDateTime.now().minusMonths(1).plusDays(5)),
                    purpose = "Business",
                    originCity = "New York",
                    destinationCity = "Paris"
                )
            ),
            1L,
            0,
            1
        )

        val passportDetails = listOf(
            PassportVisaEntity(
                id = 1L,
                docType = "passport",
                nationality = "US",
                issueDate = LocalDate.now().minusYears(1).toString(),
                expiryDate = LocalDate.now().plusYears(9).toString(),
                user = corporateUserEntity
            )
        )

        val expectedResponse = mapOf(
            "status" to "success",
            "caseId" to "12345"
        )

        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)
        every { travelHistoryService.list(any(), any(), any(), any()) } returns travelHistory
        every { passportVisaRepository.getDocByUserAndDocType(any(), "passport") } returns passportDetails
        every { pyApiClient.generateCase(any()) } returns expectedResponse

        // Act
        val result = travelAssessmentService.generateCase(assessmentId, authenticatedUser)

        // Assert
        assertEquals(expectedResponse, result)
        verify {
            pyApiClient.generateCase(match { request ->
                request.caseType == "BUSINESS_VISA" &&
                request.contactPerson == 1L &&
                (request.assessmentData["metadata"] as Map<*, *>).containsKey("travel_history") &&
                (request.assessmentData["metadata"] as Map<*, *>).containsKey("passport_details")
            })
        }
    }

    @Test
    fun `generateCase should use manager ID as contact person when available`() {
        // Arrange
        val assessmentId = 1L
        val managerId = 999L

        val assessmentUser = mockk<CorporateUserEntity> {
            every { id } returns 1L
            every { salary } returns "100K"
            every { managers } returns listOf(
                CorporateUserManagerEntity(
                    managerId = managerId,
                    corporateUser = mockk()
                )
            )
        }

        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(30),
            periodOfStay = 30,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            data = """
                {
                    "key": "BUSINESS_VISA",
                    "message": {
                        "data": {
                            "metadata": {}
                        }
                    }
                }
            """.trimIndent(),
            status = TravelAssessmentStatus.COMPLETE,
            user = assessmentUser,
            partner = null,
            responses = mutableListOf(),
            sessionId = UUID.randomUUID().toString(),
            visaType = "Business",
            visaName = "UK Business Visa",
            creatorRole = "CORPORATE"
        )

        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)
        every { travelHistoryService.list(any(), any(), any(), any()) } returns PagedResult.ModelMapper.from(PageImpl<TravelHistoryEntity>(emptyList()), emptyList())
        every { passportVisaRepository.getDocByUserAndDocType(any(), "passport") } returns emptyList()
        every { pyApiClient.generateCase(any()) } returns mapOf("status" to "success")

        // Act
        travelAssessmentService.generateCase(assessmentId, authenticatedUser)

        // Assert
        verify {
            pyApiClient.generateCase(match { request ->
                request.contactPerson == managerId
            })
        }
    }

    @Test
    fun `generateCase should throw ApplicationException when assessment is not found`() {
        // Arrange
        val assessmentId = 1L
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.empty()

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            travelAssessmentService.generateCase(assessmentId, authenticatedUser)
        }

        assertEquals(ErrorCode.NOT_FOUND, exception.error)
        verify(exactly = 0) { pyApiClient.generateCase(any()) }
    }

    @Test
    fun `logs should return paged result of travel assessment logs`() {
        // Arrange
        val pageRequest = PageRequest.of(0, 10)
        val searchFilter = TravelAssessmentLogsSearchFilter(null, null, null, null, false, null, null)

        val logDto = TravelAssessmentLogView(
            id = 1L,
            assessmentId = 100L,
            corporateId = 1L,
            corporateName = "Test Corp",
            destination = "UK",
            purpose = "Business",
            createdAt = LocalDateTime.now(),
            sessionId = "test-session-id",
            type = "VIEW",
            visaType = "Business",
            createdBy = "John Doe",
            timeSpent = 20,
            aiQuestions = 10,
            caseId = null,
            partnerId = null,
            endTimestamp = LocalDateTime.now().plusMinutes(20),
            partnerName = null,
            assessmentTime = null,
            visaName = "UK Business Visa",
            dossierCount = 0
        )

        val page = PageImpl(
            listOf(logDto),
            pageRequest,
            1
        )

        every { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) } returns page

        // Act
        val result = travelAssessmentService.logs(searchFilter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(1, result?.totalElements)
        assertEquals(1, result?.rows?.size)

        val firstLog = result?.rows?.first()
        assertNotNull(firstLog)
        with(firstLog!!) {
            assertEquals(logDto.id, 1L)
            assertEquals(logDto.assessmentId, 100L)
            assertEquals(logDto.corporateId, 1L)
            assertEquals(logDto.corporateName, corporateName)
            assertEquals(logDto.destination, "UK")
            assertEquals(logDto.purpose, "Business")
            assertEquals(logDto.sessionId, sessionId)
            assertEquals(logDto.type, "VIEW")
            assertEquals(logDto.visaType, visaType)
            assertEquals(logDto.timeSpent, 20)
            assertEquals(logDto.aiQuestions, 10)
            assertEquals(logDto.caseId, caseId)
            assertEquals(logDto.partnerName, partnerName)
        }

        verify(exactly = 1) { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) }
    }

    @Test
    fun `logs should return empty paged result when no logs found`() {
        // Arrange
        val pageRequest = PageRequest.of(0, 10)
        val searchFilter = TravelAssessmentLogsSearchFilter(null, null, null, null, false, null, null)

        val emptyPage = PageImpl<TravelAssessmentLogView>(
            emptyList(),
            pageRequest,
            0
        )

        every { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) } returns emptyPage

        // Act
        val result = travelAssessmentService.logs(searchFilter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(0, result?.totalElements)
        assertTrue(result?.rows?.isEmpty() == true)

        verify(exactly = 1) { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) }
    }

    @Test
    fun `logs should handle search filter with specific criteria`() {
        // Arrange
        val pageRequest = PageRequest.of(0, 10)
        val searchFilter = TravelAssessmentLogsSearchFilter(
            corporateId = 2L,
            fromDate = LocalDateTime.now().minusDays(7),
            toDate = LocalDateTime.now(),
            destination = "UK",
            isPartner = true,
            visaType = "Business",
            partner = 1L
        )

        val logDto = TravelAssessmentLogView(
            id = 1L,
            assessmentId = 100L,
            corporateId = 2L,
            corporateName = "Test Corp",
            destination = "UK",
            purpose = "Business",
            createdAt = LocalDateTime.now(),
            sessionId = "test-session-id",
            type = "VIEW",
            visaType = "Business",
            createdBy = "John Doe",
            timeSpent = 20,
            aiQuestions = 10,
            caseId = null,
            partnerId = 1L,
            endTimestamp = LocalDateTime.now().plusMinutes(20),
            partnerName = "Partner Name",
            assessmentTime = null,
            visaName = "UK Business Visa",
            dossierCount = 0
        )

        val page = PageImpl(
            listOf(logDto),
            pageRequest,
            1
        )

        every { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) } returns page

        // Act
        val result = travelAssessmentService.logs(searchFilter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(1, result?.totalElements)

        verify(exactly = 1) { travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest) }
    }

    @Test
    fun `generateDossier should return existing dossier data when available`() {
        // Arrange
        val assessmentId = 1L
        val existingDossierData = "existing dossier content"

        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(30),
            periodOfStay = 30,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            data = """
                {
                    "key": "BUSINESS_VISA",
                    "message": {
                        "data": {
                            "metadata": {}
                        }
                    }
                }
            """.trimIndent(),
            status = TravelAssessmentStatus.COMPLETE,
            user = corporateUserEntity,
            partner = null,
            responses = mutableListOf(),
            sessionId = UUID.randomUUID().toString(),
            visaType = "Business",
            visaName = "UK Business Visa",
            creatorRole = "CORPORATE",
            dossierData = existingDossierData
        )

        val passportDetails = listOf(
            PassportVisaEntity(
                nationality = "US",
                expiryDate = LocalDate.now().plusYears(5).toString(),
                user = corporateUserEntity
            )
        )

        every { passportVisaRepository.getDocByUserAndDocType(any(), "passport") } returns passportDetails
        every { travelHistoryService.list(any(), any(), 1L, authenticatedUser) } returns PagedResult.ModelMapper.from(PageImpl<TravelHistoryEntity>(emptyList()), emptyList())
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, corporateUserEntity.corporate.id!!) } returns Optional.of(assessment)
        every { travelAssessmentRepository.save(any()) } returns assessment

        // Act
        val result = travelAssessmentService.generateDossier(assessmentId, authenticatedUser)

        // Assert
        assertEquals(existingDossierData, result)
        verify(exactly = 0) { pyApiClient.generateDossier(any()) }
    }

    @Test
    fun `generateDossier should generate new dossier for PWN_CHECK type`() {
        // Arrange
        val assessmentId = 1L
        val generatedDossier = "new dossier content"

        val corporateUser :CorporateUserEntity = mockk {
            every { id } returns 1L
            every { jobTitle } returns "Software Engineer"
        }

        val assessment = TravelAssessmentEntity (
            id = assessmentId,
            dossierData = null,
            status = TravelAssessmentStatus.COMPLETE,
            data = """
                {
                    "key": "PWN_CHECK",
                    "message": {
                        "data": {
                            "metadata": {}
                        }
                    }
                }
            """.trimIndent(),
            user = corporateUser,
            createdByUser = corporateUser,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(30),
            periodOfStay = 30,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            sessionId = UUID.randomUUID().toString(),
            visaType = "Business",
            visaName = "UK Business Visa",
            creatorRole = "CORPORATE"
        )

        val travelHistory = PagedResult(
            listOf(
                TravelHistoryResponse(
                    id = 1L,
                    destinationCountry = "FR",
                    originCountry = "US",
                    periodOfStay = 5,
                    stayUnit = TravelStayUnit.DAYS,
                    originCity = "New York",
                    destinationCity = "Paris",
                    arrival = TimeUtil.toEpochMillis(LocalDateTime.now().minusMonths(1)),
                    departure = TimeUtil.toEpochMillis(LocalDateTime.now().minusMonths(1).plusDays(5)),
                    purpose = "Business"
                )
            ),
            1L,
            0,
            1
        )

        val passportDetails = listOf(
            PassportVisaEntity(
                nationality = "US",
                expiryDate = LocalDate.now().plusYears(5).toString(),
                user = corporateUser
            )
        )

        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)
        every { travelHistoryService.list(any(), any(), 1L, authenticatedUser) } returns travelHistory
        every { passportVisaRepository.getDocByUserAndDocType(any(), "passport") } returns passportDetails
        every { pyApiClient.generateDossier(any()) } returns generatedDossier
        every { travelAssessmentRepository.save(any()) } returns assessment

        // Act
        val result = travelAssessmentService.generateDossier(assessmentId, authenticatedUser)

        // Assert
        assertEquals(generatedDossier, result)
        val expectedRequest = AssessmentDossierGenerateRequest(
            assessmentData = mapOf(
                "metadata" to mapOf(
                    "travel_history" to travelHistory.rows.map {
                        mapOf(
                            "destination_country" to it.destinationCountry,
                            "origin_country" to it.originCountry,
                            "period_of_stay" to it.periodOfStay,
                            "stay_unit" to it.stayUnit.name,
                        )
                    },
                    "passport_details" to passportDetails.map {
                        mapOf(
                            "nationality" to it.nationality,
                            "expiry_date" to it.expiryDate
                        )
                    },
                    "job_title" to "Software Engineer",
                    "recommended_visa" to "Posted worker notification"
                )
            )
        )

        verify(exactly = 1) {
            pyApiClient.generateDossier(expectedRequest)
        }
    }

    @Test
    fun `generateDossier should generate new dossier for BUSINESS_VISA type`() {
        // Arrange
        val assessmentId = 1L
        val generatedDossier = "new dossier content"

        val corporateUser: CorporateUserEntity = mockk{
            every { id } returns 1L
            every { jobTitle } returns "Manager"
        }

        val assessment = TravelAssessmentEntity(
            id = assessmentId,
            dossierData = null,
            status = TravelAssessmentStatus.COMPLETE,
            data = """
                {
                    "key": "BUSINESS_VISA",
                    "message": {
                        "data": {
                            "metadata": {}
                        }
                    }
                }
            """.trimIndent(),
            user = corporateUser,
            createdByUser = corporateUser,
            originCountry = "US",
            destinationCountry = "UK",
            arrival = LocalDateTime.now(),
            departure = LocalDateTime.now().plusDays(30),
            periodOfStay = 30,
            stayUnit = TravelStayUnit.DAYS,
            purpose = "Business",
            sessionId = UUID.randomUUID().toString(),
            visaType = "Business",
            visaName = "UK Business Visa",
            creatorRole = "CORPORATE"
        )

        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)
        every { travelHistoryService.list(any(), any(), 1L, authenticatedUser) } returns PagedResult.ModelMapper.from(PageImpl<TravelHistoryEntity>(emptyList()), emptyList())
        every { passportVisaRepository.getDocByUserAndDocType(any(), "passport") } returns emptyList()
        every { pyApiClient.generateDossier(any()) } returns generatedDossier
        every { travelAssessmentRepository.save(any()) } returns assessment

        // Act
        val result = travelAssessmentService.generateDossier(assessmentId, authenticatedUser)

        // Assert
        assertEquals(generatedDossier, result)
        val expectedRequest = AssessmentDossierGenerateRequest(
            assessmentData = mapOf(
                "metadata" to mapOf(
                    "travel_history" to emptyList<Any>(),
                    "passport_details" to emptyList<Any>(),
                    "job_title" to "Manager",
                    "recommended_visa" to "Business Visa"
                )
            )
        )

        verify(exactly = 1) {
            pyApiClient.generateDossier(expectedRequest)
        }
    }

    @Test
    fun `generateDossier should throw ApplicationException when assessment is not found`() {
        // Arrange
        val assessmentId = 1L
        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.empty()

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            travelAssessmentService.generateDossier(assessmentId, authenticatedUser)
        }

        assertEquals(ErrorCode.NOT_FOUND, exception.error)
        verify(exactly = 0) { pyApiClient.generateDossier(any()) }
    }

    @Test
    fun `generateDossier should throw ApplicationException when assessment is in progress`() {
        // Arrange
        val assessmentId = 1L
        val assessment: TravelAssessmentEntity = mockk{
            every { status } returns TravelAssessmentStatus.IN_PROGRESS
            every { id } returns assessmentId
        }

        every { travelAssessmentRepository.findByIdAndCreatedByCorporateId(assessmentId, 1L) } returns Optional.of(assessment)

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            travelAssessmentService.generateDossier(assessmentId, authenticatedUser)
        }

        assertEquals(ErrorCode.NOT_FOUND, exception.error)
        verify(exactly = 0) { pyApiClient.generateDossier(any()) }
    }
}
