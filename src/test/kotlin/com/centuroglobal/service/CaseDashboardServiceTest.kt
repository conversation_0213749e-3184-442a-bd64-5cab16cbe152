package com.centuroglobal.service
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.CaseStatusHistory
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.case.DashboardCaseSearchFilter
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.CaseTrackingViewRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.test.util.ReflectionTestUtils
import java.time.LocalDateTime
import java.util.*


class CaseDashboardServiceTest{
    private val caseRepository:CaseRepository = mockk()
    private val clientViewRepository:ClientViewRepository= mockk()
    private val statusHistoryRepository:CaseStatusHistoryRepository= mockk()
    private val loginAccountRepository:LoginAccountRepository= mockk()
    private val corporateUserRepository:CorporateUserRepository= mockk()
    private val accountRepository:AccountEntityRepository= mockk()
    private val awsS3Service:AwsS3Service = mockk()
    private val reminderRepository: ReminderRepository = mockk()
    private val caseTrackingViewRepository:CaseTrackingViewRepository = mockk()
    private val taskService: TaskService= mockk()
    private val corporateService:CorporateService = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()

    private val caseDashboardService = CaseDashboardService(
        caseRepository,
        statusHistoryRepository,
        accountRepository,
        reminderRepository,
        caseTrackingViewRepository,
        taskService,
        userProfileUtil,
        corporateService)


    private val loginAccountEntity: LoginAccountEntity = mockk()
    private val authenticatedUser: AuthenticatedUser = mockk()
    private val clientView: ClientView = mockk()
    private val caseEntity: CaseEntity = mockk()
    private val caseStatusHistoryRepository: CaseStatusHistoryRepository = mockk()
    private val accountEntityRepository: AccountEntityRepository = mockk()
    private val corporateEntity = CorporateEntity(
        1,
        "String",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1L,
        listOf(),
        listOf(),
        1L)
    private val accountEntity = AccountEntity(
        corporateEntity.id,
        "accountEntity",
        AccountStatus.PENDING_VERIFICATION,
        null,
        null,
        corporateEntity
    )
    private val bandsEntity = BandsEntity(
        corporateEntity.id,
        "bands1",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null
    )
    private val corporateUserEntity = CorporateUserEntity(
            corporateEntity,
            setOf(accountEntity),
            "jobTitle",
            false,
            listOf(),
            bandsEntity,
            mutableListOf(NotificationPreferencesEntity(
                1L, NotificationType.CASE_GCHAT_EMAIL, true,
                corporateUser = CorporateUserEntity()
            )),
            null)
    private val  userAccess = UserAccess(
        "USER",
                            mutableListOf("OWN","FULL","REPORTEES"))
    private val caseStatusHistory = CaseStatusHistory(
        1,
        CaseStatus.CASE_COMPLETE.name,
        "String",
        caseEntity,
        1L,
        "CLIENT",
        false)
    private val dashboardCaseSearchFilter = DashboardCaseSearchFilter(
    "DEPENDENT_VISA",
        1,
        1,
        listOf(clientView),
        "CLIENT",
        "US",
        1)
        private val caseCategoryEntity = CaseCategoryEntity(
            1,
            "1",
            "DEPENDENT_VISA",
            "1",
            "IMMIGRATION_GM",)

    //commenting since functions don't exist
//    @Test
//    fun  retrieveProfileTest(){
//        every { loginAccountRepository.findByIdOrNull( 1) } returns loginAccountEntity
//        every { loginAccountEntity.profilePhotoUrl } returns "String"
//        every { awsS3Service.getProfilePicUrl("String")}  returns "String"
//        every { loginAccountEntity.id } returns 1
//        every { loginAccountEntity.email } returns "String"
//        every { loginAccountEntity.firstName } returns "String"
//        every { loginAccountEntity.lastName } returns "String"
//        every { loginAccountEntity.status } returns AccountStatus.ACTIVE
//        every { loginAccountEntity.role } returns Role.ROLE_EXPERT
//        every { loginAccountEntity.countryCode } returns "US"
//        val response =  caseDashboardService.retrieveProfile(1,"String")
//        assertNotNull(response)
//        assertEquals("US",response.countryCode)
//        assertEquals("String",response.companyName)
//        assertEquals(1,response.id)
//        assertEquals("String",response.firstName)
//        assertEquals("String",response.lastName)
//    }
    @Test
    fun generateAggregateCaseDataForUserTest(){
        every { authenticatedUser.userId } returns 1
        every { authenticatedUser.companyId } returns 1
        every { authenticatedUser.visibilities } returns listOf(userAccess)
        every { corporateUserRepository.findById(1) } returns Optional.of(corporateUserEntity)
        every { clientViewRepository.findAllByUserIdIn(listOf(1L)) } returns listOf(clientView)
        every { accountRepository.findById(1) } returns Optional.of(accountEntity)
        every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(accountEntity,false,listOf(clientView))} returns mutableListOf(caseEntity)
        every { caseEntity.archive } returns false
        every { caseEntity.percentCompletion } returns 1
        every { caseEntity.parentCategoryId } returns "1"
        every { corporateUserRepository.findByCorporateId(1) } returns listOf(corporateUserEntity)

        every { corporateService.getEligibleClientList(any(),any(),any()) } returns Pair(true, listOf())
        every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(any(), any(), any()) } returns mutableListOf()

        val response = caseDashboardService.generateAggregateCaseDataForUser(authenticatedUser,1,1)
        assertNotNull(response)
    }
//    @Test
//    fun getEligibleCaseListTest(){
//        every { authenticatedUser.visibilities } returns listOf(userAccess)
//        every { authenticatedUser.userId } returns 1
//        every { authenticatedUser.companyId } returns 1
//        every { corporateUserRepository.findById(1) } returns Optional.of(corporateUserEntity)
//        every { clientViewRepository.findAllByUserIdIn(listOf(1L))} returns listOf(clientView)
//        every { corporateUserRepository.findByCorporateId(1) } returns listOf(corporateUserEntity)
//        val response = caseDashboardService.getEligibleClientList(authenticatedUser,1,1)
//        assertNotNull(response)
//    }
//    @Test
//    fun getEligibleClientListTest(){
//        every { authenticatedUser.visibilities } returns listOf(userAccess)
//        every { authenticatedUser.userId } returns 1
//        every { authenticatedUser.companyId } returns 1
//        every { corporateUserRepository.findByCorporateId(1)} returns listOf(corporateUserEntity)
//        every { clientViewRepository.findAllByUserIdIn(listOf(1L))} returns  listOf(clientView)
//        val response = caseDashboardService.getEligibleClientList(authenticatedUser,1,1)
//        assertNotNull(response)
//    }
    @Test
    fun getPendingTasksTest(){
        every { authenticatedUser.visibilities } returns listOf(userAccess)
        every { authenticatedUser.companyId } returns 1
        every { corporateUserRepository.findByCorporateId(1) } returns listOf(corporateUserEntity)
        every { clientViewRepository.findAllByUserIdIn(listOf(1L))} returns  listOf(clientView)
        every { accountRepository.findById(1) } returns Optional.of(accountEntity)
        every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(accountEntity,false,listOf(clientView))} returns mutableListOf(caseEntity)
        every { caseEntity.actionFor } returns "CLIENT"
        every { statusHistoryRepository.findAllByCaseInAndIsDeleted(listOf(caseEntity),false) } returns mutableListOf(caseStatusHistory)
        every { clientView.fullName } returns "String"
        every { caseEntity.createdBy } returns clientView
        every { caseEntity.status } returns CaseStatus.NOT_STARTED.name
        every { caseEntity.lastUpdatedDate } returns LocalDateTime.now()
        every { caseEntity.id } returns 1
        every { clientView.userId } returns 1
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.profilePhotoUrl } returns "String"
        every { awsS3Service.getProfilePicUrl("String")}  returns "String"
        every { loginAccountEntity.id } returns 1
        every { loginAccountEntity.email } returns "String"
        every { loginAccountEntity.firstName } returns "String"
        every { loginAccountEntity.lastName } returns "String"
        every { loginAccountEntity.status } returns AccountStatus.ACTIVE
        every { loginAccountEntity.role }  returns Role.ROLE_EXPERT
        every { loginAccountEntity.countryCode } returns "US"

        every { corporateService.getEligibleClientList(any(), any(), any()) } returns Pair(true, listOf())
        every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(any(), any(), any() )} returns mutableListOf()
        every { taskService.listPendingTasks(any(), any(), any(), any()) } returns listOf()
        every { caseStatusHistoryRepository.findAllByCaseInAndIsDeleted(listOf(), false) } returns mutableListOf()

        ReflectionTestUtils.setField(caseDashboardService, "statusHistoryRepository", caseStatusHistoryRepository)
        val response = caseDashboardService.getPendingTasks(authenticatedUser,1,1)
        assertNotNull(response)
    }
  @Test
  fun casesByCountryTest(){
      every { authenticatedUser.visibilities } returns listOf(userAccess)
      every { authenticatedUser.companyId } returns 1
      every { corporateUserRepository.findByCorporateId(1) } returns listOf(corporateUserEntity)
      every { clientViewRepository.findAllByUserIdIn(listOf(1)) } returns listOf(clientView)
      every { caseRepository.searchByCriteria(dashboardCaseSearchFilter) } returns listOf(caseEntity)
      every { caseEntity.country } returns "US"
      every { caseEntity.account } returns accountEntity
      every { caseEntity.archive } returns false
      every { caseEntity.percentCompletion } returns 1

      every { corporateService.getEligibleClientList(any(), any(), any()) } returns Pair(true, listOf())
      every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(any(), any(), any() )} returns mutableListOf()
      every { taskService.listPendingTasks(any(), any(), any(), any()) } returns listOf()
      every { caseStatusHistoryRepository.findAllByCaseInAndIsDeleted(listOf(), false) } returns mutableListOf()

      ReflectionTestUtils.setField(caseDashboardService, "statusHistoryRepository", caseStatusHistoryRepository)

      val response = caseDashboardService.casesByCountry(authenticatedUser,dashboardCaseSearchFilter)
      assertNotNull(response)
  }
    @Test
    fun caseStatusHistoryTest(){
        val pageRequest = PageRequest.of(1,10,Sort.unsorted())
        every { authenticatedUser.visibilities } returns listOf(userAccess)
        every { authenticatedUser.companyId } returns 1
        every { corporateUserRepository.findByCorporateId(1) } returns listOf(corporateUserEntity)
        every { clientViewRepository.findAllByUserIdIn(listOf(1))} returns listOf(clientView)
        every { caseRepository.searchByCriteria(dashboardCaseSearchFilter) } returns listOf(caseEntity)
        every { statusHistoryRepository.searchByCriteria(listOf(caseEntity),"String",dashboardCaseSearchFilter.actionFor,pageRequest) } returns PageImpl(listOf(caseStatusHistory))
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.profilePhotoUrl } returns "String"
        every { awsS3Service.getProfilePicUrl("String")}  returns "String"
        every { loginAccountEntity.id } returns 1
        every { loginAccountEntity.email } returns "String"
        every { loginAccountEntity.firstName } returns "String"
        every { loginAccountEntity.lastName } returns "String"
        every { loginAccountEntity.status } returns AccountStatus.ACTIVE
        every { loginAccountEntity.role }  returns Role.ROLE_EXPERT
        every { loginAccountEntity.countryCode } returns "US"
        every { caseEntity.id } returns 1
        every { caseEntity.category } returns caseCategoryEntity
        every { caseEntity.initiatedFor } returns "String"
        every { caseEntity.country } returns "US"

        every { corporateService.getEligibleClientList(any(), any(), any()) } returns Pair(true, listOf())
        every { caseRepository.findAllByAccountAndArchiveAndCreatedByIn(any(), any(), any() )} returns mutableListOf()
        every { taskService.listPendingTasks(any(), any(), any(), any()) } returns listOf()

        every { userProfileUtil.retrieveProfile(1) } returns UserProfile(
            1,
            "<EMAIL>",
            "String",
            "String",
            AccountStatus.ACTIVE,
            Role.ROLE_EXPERT,
            "String",
            "String",
            "String")

        every { caseStatusHistoryRepository.findAllByCaseInAndIsDeleted(listOf(), false) } returns mutableListOf()

        every { caseStatusHistoryRepository.searchByCriteria(any(), any(), any(), any()) } returns PageImpl(listOf(caseStatusHistory))

        ReflectionTestUtils.setField(caseDashboardService, "statusHistoryRepository", caseStatusHistoryRepository)

        val response = caseDashboardService.caseStatusHistory(authenticatedUser,dashboardCaseSearchFilter,"String",pageRequest)
        assertNotNull(response)
    }
}