package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.enums.BandStatus
import com.centuroglobal.shared.repository.BandsRepository
import com.centuroglobal.shared.repository.FeatureMasterRepository
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class BandServiceTest {
    private val bandsRepository: BandsRepository = mockk()
    private val caseDashboardService: CaseDashboardService = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val featureMasterRepository: FeatureMasterRepository = mockk()

    private val bandService = BandService(
        bandsRepository,
        userProfileUtil,
        featureMasterRepository = featureMasterRepository
    )

    private val bandsEntity = BandsEntity(
        1,
        "bands1",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null
    )

    @Test
    fun listBandsTest() {
        every { bandsRepository.findByCorporateId(1) } returns listOf(bandsEntity)
        val result = bandService.listBands(1)
        assertNotNull(result)
    }

    @Test
    fun bandsListingTest() {
        every { bandsRepository.findByCorporateId(1) } returns listOf(bandsEntity)
        val result = bandService.bandsListing(1)
        assertNotNull(result)
    }
}