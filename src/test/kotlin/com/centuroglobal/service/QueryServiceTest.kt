package com.centuroglobal.service

import com.centuroglobal.data.payload.query.QueryRequest
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.data.entity.query.QueryAssigneeEntity
import com.centuroglobal.shared.data.entity.query.QueryCategoryEntity
import com.centuroglobal.shared.data.entity.query.QueryEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.ProposalRepository
import com.centuroglobal.shared.repository.query.QueryAssigneeRepository
import com.centuroglobal.shared.repository.query.QueryCategoryRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.PartnerEmailUtil
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.util.*

const val LOGGED_IN_USER_ID = 10L
const val LOGGED_IN_USER_CORPORATE_ID = 5L
const val LOGGED_IN_USER_EMAIL = "<EMAIL>"

class QueryServiceTest {

    private val queryRepository: QueryRepository = mockk()
    private val baseRepository = queryRepository as BaseRepository<QueryEntity, Long>
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val queryCategoryRepository: QueryCategoryRepository = mockk()
    private val queryProposalRepository: ProposalRepository = mockk()
    private val corporateRepository: CorporateRepository = mockk()
    private val corporateUserRepository: CorporateUserRepository = mockk()

    private val caseDashboardService: CaseDashboardService = mockk()
    private val groupChatService: GroupChatService = mockk()
    private val s3Service: AwsS3Service = mockk()
    private val mailSendingService: MailSendingService = mockk()
    private val corporateUserService: CorporateUserService = mockk()
    private val partnerService: PartnerService= mockk()
    private val partnerEmailUtil: PartnerEmailUtil = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()

    private val proposalService = ProposalService("test-bucket", listOf(),
        queryProposalRepository, s3Service)

    private val corporateName = "Test Company"
    val partnerRepository: PartnerRepository = mockk()
    val queryAssigneeRepository: QueryAssigneeRepository = mockk()

    private val queryService = QueryService(
        "test-url",
        listOf("<EMAIL>"),
        listOf("<EMAIL>"),
        loginAccountRepository,
        queryRepository,
        queryCategoryRepository,
        userProfileUtil,
        groupChatService,
        s3Service,
        corporateUserRepository,
        corporateRepository,
        mailSendingService,
        corporateUserService,
        proposalService,
        partnerRepository,
        queryAssigneeRepository,
        partnerEmailUtil
    )


    private val corporateUserEntity: CorporateUserEntity = mockk()
    private val corporateEntity = CorporateEntity(
        id = 5,
        name = "",
        countryCode = "IN",
        status = CorporateStatus.ACTIVE,
        subscriptionActive = true,
        subscriptionExpiryDate = LocalDateTime.now(),
        rootUserId = 2,
        accountList = listOf(),
        users = listOf(),
        lastUpdatedBy = 2,
        partner = null,
        primaryColor = "",
        secondaryColor = "",
        companyLogoId = "",
        team = mutableListOf(),
        questionsQuota = 4,
        subscriptionStartDate = LocalDateTime.now(),
        subscriptionEndDate = LocalDateTime.now(),
        isTeamEmail = false
    )

    private val authenticatedUser: AuthenticatedUser = mockk()

    private val queryCategoryEntity: QueryCategoryEntity = mockk()
    private val groupChatEntity: GroupChatEntity = mockk()

    private val queryEntity = QueryEntity(
        1,
        "heading",
        description = "description",
        status = QueryStatus.OPEN,
        country = "US",
        createdBy = corporateUserEntity,
        assignedTo = mutableListOf(ExpertUserEntity(
            id= 134,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"))
    )

    private val partner = PartnerEntity(
        id = 1L,
        name = "partner",
        country = "IN",
        createdFrom = PartnerType.NEW,
        contractFromDate = LocalDateTime.now(),
        contractToDate = LocalDateTime.now(),
        casesManaged = PartnerCaseType.PARTNER,
        queriesManaged = PartnerCaseType.PARTNER,
        companyLogo = "",
        themePrimaryColor = "",
        themeSecondaryColor = "",
        rootUserId = 2,
        status = CorporateStatus.ACTIVE,
        corporates = listOf(),
        partnerUsers = mutableListOf(),
        band = BandsEntity(
            id = 7,
            name = "",
            description = "",
            status = BandStatus.ACTIVE,
            color = "",
            corporate = null,
            corporateUsers = listOf(),
            bandAccesses = mutableListOf()
        ),
        referenceId = 5,
        associatedCompanies = mutableListOf(),
        corporateAccess = ""
    )


    private fun createQueryRequest(heading: String, desc: String, country: String, categories: List<String>, assignee: List<Long>?): QueryRequest{
        return QueryRequest(
            heading,
            description = desc,
            country = country,
            queryCategory = categories,
            assignedExperts = assignee
        )
    }
    private val queryRequest = createQueryRequest("Test heading", "Test description", "IN", listOf("CAT1"), null)


    @BeforeEach
    fun setup(){
        every { authenticatedUser.userId } returns LOGGED_IN_USER_ID
        every { authenticatedUser.companyId } returns LOGGED_IN_USER_CORPORATE_ID
        every { authenticatedUser.email } returns LOGGED_IN_USER_EMAIL

        every { authenticatedUser.partnerId } returns 4L


        every { corporateUserEntity.id } returns LOGGED_IN_USER_ID
        every { corporateUserEntity.corporate } returns corporateEntity

        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.of(corporateUserEntity)

        every { (queryEntity.createdBy as LoginAccountEntity).firstName } returns "test"
        every { (queryEntity.createdBy as LoginAccountEntity).lastName } returns "user"
        every { (queryEntity.createdBy as LoginAccountEntity).email } returns LOGGED_IN_USER_EMAIL


        every { corporateUserService.getUserManagers(any()) } returns listOf()

        every { corporateUserRepository.findAllByCorporateIdAndBandNameIn(any(), listOf("Super Admin (free)", "Super Admin")) } returns listOf()

        every { groupChatService.createGroupChat(
            ChatType.QUERY, any(), any(),
            virtualParticipants = listOf()
        ) } returns groupChatEntity

        every { s3Service.getS3PublicUrl(any()) } returns "this-is-s3-public-url"
        every { mailSendingService.sendEmail(any()) } returns true

        //every { caseDashboardService.retrieveProfile(any(), corporateName ) } returns UserProfile(LOGGED_IN_USER_ID, LOGGED_IN_USER_EMAIL, "test", "user", AccountStatus.ACTIVE, Role.ROLE_ADMIN, null, null)
        //every { caseDashboardService.retrieveProfile(any() ) } returns UserProfile(LOGGED_IN_USER_ID, LOGGED_IN_USER_EMAIL, "test", "user", AccountStatus.ACTIVE, Role.ROLE_ADMIN, null, null)

        every { groupChatService.getMessagesCount(ChatType.QUERY, any()) } returns 13
        every { groupChatService.getUnreadMessagesCount(ChatType.QUERY, any(), any()) } returns 4

        every { corporateRepository.findById(any()) } returns Optional.of(corporateEntity)

        every { queryProposalRepository.findAllByTypeAndReferenceId(ChatType.QUERY, queryEntity.id!!) } returns listOf()

        ReflectionTestUtils.setField(queryService, "corporateRepo", corporateRepository)
        ReflectionTestUtils.setField(queryService, "corporateUserRepo", corporateUserRepository)

        every { partnerService.getPartnerId(any()) } returns null

        val allowedFileTypes = listOf("pdf")
        ReflectionTestUtils.setField(proposalService, "allowedFileTypes", allowedFileTypes)

    }

    @Test
    fun createQuery() {
        every { queryRepository.save(any()) } returns queryEntity
        every { queryCategoryRepository.save(any()) } returns queryCategoryEntity

        every { corporateUserRepository.findAllByCorporateIdAndBandNameIn(any(), any())} returns listOf()

        every { partnerRepository.findById(any()) } returns Optional.of(partner)

        every { groupChatService.getVirtualParticipants(any()) } returns listOf()

        every { queryCategoryEntity.name } returns "abc"

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        val createQuery = queryService.createQuery(queryRequest, authenticatedUser)
        assertEquals(queryEntity.id, createQuery)
    }

    @Test
    fun `test CreateQuery Throws Exception When User Not Found`() {
        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.empty()
        assertThrows<ApplicationException> {
            queryService.createQuery(queryRequest, authenticatedUser)
        }
    }

    @Test
    fun testCreateQuery_AssignedExpertsNull() {
        // Mock input data
        val queryRequest = QueryRequest(
            heading = "Test Heading",
            country = "Test Country",
            queryCategory = listOf("Category 1", "Category 2"),
            description = "Test Description",
            assignedExperts = null

        )

        every { queryRepository.save(any()) } answers {
            val queryEntity = arg<QueryEntity>(0)
            queryEntity.id = 1
            queryEntity
        }

        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.of(corporateUserEntity)

        every { queryCategoryRepository.save(any()) } answers {
            val queryCategoryEntity = arg<QueryCategoryEntity>(0)
            queryCategoryEntity.id = 1
            queryCategoryEntity
        }

        every { partnerRepository.findById(any()) } returns Optional.of(partner)
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()

        every { queryCategoryEntity.name } returns "abc"

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        val result = queryService.createQuery(queryRequest, authenticatedUser)

        // Verify the result
        assertEquals(1L, result)

        // Verify the interactions
        verify {
            queryRepository.save(match { it.heading == "Test Heading" && it.country == "Test Country" })
            queryCategoryRepository.save(match { it.query.id == 1L && it.name == "Category 1" })
            queryCategoryRepository.save(match { it.query.id == 1L && it.name == "Category 2" })
            groupChatService.createGroupChat(
                ChatType.QUERY, 1L, any(),
                virtualParticipants = listOf()
            )
        }

    }

    @Test
    fun testListQueriesForAdmin_AdminRole() {
        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)

        // Mock repository methods
        every { queryRepository.searchByCriteriaForAdmin(searchFilter, pageRequest) } returns PageImpl(listOf(queryEntity))
        every { queryRepository.findStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { queryRepository.findQueryProposalStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name

        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns
                CorporateUserProfile(
                    id = 3,
                    email = "<EMAIL>",
                    firstName = "a",
                    lastName = "b",
                    status = AccountStatus.ACTIVE,
                    role = Role.ROLE_ADMIN,
                    countryCode = "IN",
                    profilePictureFullUrl = "",
                    companyName = "",
                    userType = PartnerCaseType.CG,
                    bandName = "",
                    partnerName = ""
                )

        every { queryAssigneeRepository.findAllByQuery(any())} returns mutableListOf()

        // Call the method
        val result = queryService.listQueriesForAdmin(searchFilter, pageRequest, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertNotNull(result!!.queries)
        assertNotNull(result.stats)
        assertNotNull(result.stats.queries)
        assertNotNull(result.stats.closed)
        assertNotNull(result.stats.open)
        assertNotNull(result.stats.proposalsApproved)
        assertNotNull(result.stats.proposalsUploaded)
        assertFalse(result.queries.rows.isEmpty())
        assertEquals(result.queries.rows[0].heading, queryEntity.heading)


        // Verify the interactions
        verify { queryRepository.searchByCriteriaForAdmin(searchFilter, pageRequest) }
    }

    @Test
    fun testListQueriesForAdmin_NonAdminRole_ThrowsException() {

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name

        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("")))

        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )

        val page = PageImpl(listOf(queryEntity))

        every { queryRepository.searchByCriteriaForFullAccess(any(), any()) } returns page

        every { queryRepository.findStatsByCriteria(any()) } returns listOf()

        every { queryRepository.findQueryProposalStatsByCriteria(any()) } returns listOf()

        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns
                CorporateUserProfile(
                    id = 3,
                    email = "<EMAIL>",
                    firstName = "a",
                    lastName = "b",
                    status = AccountStatus.ACTIVE,
                    role = Role.ROLE_ADMIN,
                    countryCode = "IN",
                    profilePictureFullUrl = "",
                    companyName = "",
                    userType = PartnerCaseType.CG,
                    bandName = "",
                    partnerName = ""
                )

        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()

        val pageRequest = PageRequest.of(1, 10)
        assertThrows<ApplicationException> {
            queryService.listQueriesForAdmin(searchFilter, pageRequest, authenticatedUser)
        }
    }

    @Test
    fun testListQueriesWithFullAccess_CorporateRole() {
        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)

        // Mock repository methods
        every { queryRepository.searchByCriteriaForFullAccess(searchFilter, pageRequest) } returns PageImpl(listOf(queryEntity))
        every { queryRepository.findStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { queryRepository.findQueryProposalStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name

        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))


        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns
                CorporateUserProfile(
                    id = 3,
                    email = "<EMAIL>",
                    firstName = "a",
                    lastName = "b",
                    status = AccountStatus.ACTIVE,
                    role = Role.ROLE_ADMIN,
                    countryCode = "IN",
                    profilePictureFullUrl = "",
                    companyName = "",
                    userType = PartnerCaseType.CG,
                    bandName = "",
                    partnerName = "")

        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()


                    // Call the method
        val result = queryService.listQueries(searchFilter, pageRequest, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, queryEntity.heading)


        // Verify the interactions
        verify { queryRepository.searchByCriteriaForFullAccess(searchFilter,  pageRequest) }
    }

    @Test
    fun testListQueriesWithReporteesAccess_CorporateRole() {
        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)

        // Mock repository methods
        every { queryRepository.searchByCriteria(searchFilter,  listOf(LOGGED_IN_USER_ID, LOGGED_IN_USER_ID), pageRequest) } returns PageImpl(listOf(queryEntity))
        every { queryRepository.findStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { queryRepository.findQueryProposalStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)

        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("REPORTEES")))

        every { corporateUserRepository.findIdByManagersManagerId(any()) } returns mutableListOf()
        every { queryRepository.searchByCriteria(any(),any(),any()) } returns PageImpl(listOf(queryEntity))
        every { userProfileUtil.retrieveCorporateProfile(any(),any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = "")
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()

        // Call the method
        val result = queryService.listQueries(searchFilter, pageRequest, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, queryEntity.heading)


        // Verify the interactions
        //verify { queryRepository.searchByCriteria(searchFilter,  listOf(LOGGED_IN_USER_ID, LOGGED_IN_USER_ID), pageRequest) }
    }

    @Test
    fun testListQueriesWithOwnAccess_CorporateRole() {
        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)

        // Mock repository methods
        every { queryRepository.searchByCriteria(searchFilter, listOf(LOGGED_IN_USER_ID), pageRequest) } returns PageImpl(listOf(queryEntity))
        every { queryRepository.findStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { queryRepository.findQueryProposalStatsByCriteria(searchFilter) } returns listOf(listOf(mapOf("status" to "resolved"), mapOf("count" to "1")))
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)

        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("OWN")))

        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()


            // Call the method
        val result = queryService.listQueries(searchFilter, pageRequest, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, queryEntity.heading)


        // Verify the interactions
        verify { queryRepository.searchByCriteria(searchFilter, listOf(LOGGED_IN_USER_ID), pageRequest) }
    }

    /*@Test
    fun testListQueries_AdminRole_ThrowsException() {

        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { authenticatedUser.visibilities } returns listOf()

        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)

        every { queryRepository.searchByCriteriaForAdmin() }

        assertThrows<ApplicationException> {
            queryService.listQueries(searchFilter, pageRequest, authenticatedUser)
        }
    }*/
    /*@Test
    fun testListQueries_Invalid_Query_Access_ThrowsException() {

        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("INVALID_ACCESS")))

        // Mock input data
        val searchFilter = QuerySearchFilter.Builder.build(
            search = "query",
            accountId = null,
            user = null,
            responses = null,
            categories = null,
            countryCode = null,
            status = null,
            corporateId = 1L,
            from = null,
            to = null,
            null,null,null
        )
        val pageRequest = PageRequest.of(1, 10)
        assertThrows<ApplicationException> {
            queryService.listQueries(searchFilter, pageRequest, authenticatedUser)
        }
    }*/

    @Test
    fun getQueryById_With_FullAccess() {

        val queryId = 1L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { groupChatService.updateLastSeen(ChatType.QUERY, queryEntity.id!!, LOGGED_IN_USER_ID) } returns Unit


        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()

        val result = queryService.getQueryById(queryId, authenticatedUser)

        assertNotNull(result)
        assertEquals(queryEntity.id, result!!.id)
        assertEquals(queryEntity.heading, result.heading)
    }

    @Test
    fun getQueryById_With_Reportees_access() {

        val queryId = 1L
        val userId = 2L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        //Changing user id to acts as reportees
        every { authenticatedUser.userId } returns userId
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("REPORTEES")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { groupChatService.updateLastSeen(ChatType.QUERY, queryId, userId) } returns Unit
        every { corporateUserRepository.findAllByManagersManagerId(userId) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(queryId, mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(queryEntity)
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUserEntity)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateUserRepository.findIdByManagersManagerId(any()) } returns mutableListOf()

        every { queryRepository.findByIdAndCreatedByIdIn(any(), any()) } returns Optional.of(queryEntity)
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()


        val result = queryService.getQueryById(queryId, authenticatedUser)

        assertNotNull(result)
        assertEquals(queryEntity.id, result!!.id)
        assertEquals(queryEntity.heading, result.heading)
    }

    @Test
    fun getQueryById_With_Reportees_throws_Exception() {

        val queryId = 1L
        val userId = 2L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        //Changing user id to acts as reportees
        every { authenticatedUser.userId } returns userId
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("REPORTEES")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { corporateUserRepository.findAllByManagersManagerId(userId) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(queryId, mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.empty()
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUserEntity)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()

        every { corporateUserRepository.findIdByManagersManagerId(any()) } returns mutableListOf()
        every { queryRepository.findByIdAndCreatedByIdIn(any(), any()) } returns Optional.empty()
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()
        every { groupChatService.updateLastSeen(any(), any(), any()) } returns Unit


        assertThrows<ApplicationException> {
            queryService.getQueryById(queryId, authenticatedUser)
        }

    }

    @Test
    fun getQueryById_With_Own_Access() {

        val queryId = 1L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("OWN")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { baseRepository.findByIdAndCreatedByIn(queryId, mutableListOf(corporateUserEntity)) } returns Optional.of(queryEntity)

        every { groupChatService.updateLastSeen(ChatType.QUERY, queryEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateUserRepository.findIdById(any()) } returns EntityIdDto(5)
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()
        every { queryRepository.findByIdAndCreatedByIdIn(any(), any()) } returns Optional.of(queryEntity)

        val result = queryService.getQueryById(queryId, authenticatedUser)

        assertNotNull(result)
        assertEquals(queryEntity.id, result!!.id)
        assertEquals(queryEntity.heading, result.heading)
    }

    @Test
    fun getQueryById_With_Admin_Access() {

        val queryId = 1L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name

        every { groupChatService.updateLastSeen(ChatType.QUERY, queryEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryRepository.findByIdAndCreatedByCorporateId(any(), any()) } returns Optional.of(queryEntity)
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()
        every { queryRepository.findByIdAndCreatedByIdIn(any(), any()) } returns Optional.of(queryEntity)

        val result = queryService.getQueryById(queryId, authenticatedUser)

        assertNotNull(result)
        assertEquals(queryEntity.id, result!!.id)
        assertEquals(queryEntity.heading, result.heading)
    }

    @Test
    fun getQueryById_With_No_Corporate() {

        val queryId = 1L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns null

        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()

        assertThrows<ApplicationException> {
            queryService.getQueryById(queryId, authenticatedUser)
        }
    }

    @Test
    fun getQueryById_privilegeEscalation() {

        val queryId = 1L

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.empty()

        every { authenticatedUser.visibilities } returns   listOf(UserAccess("QUERY", mutableListOf("FULL")))

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryRepository.findByIdAndCreatedByCorporateId(any(), any()) } returns Optional.empty()
        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns CorporateUserProfile(
            id = 3,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_ADMIN,
            countryCode = "IN",
            profilePictureFullUrl = "",
            companyName = "",
            userType = PartnerCaseType.CG,
            bandName = "",
            partnerName = ""
        )
        every { groupChatService.updateLastSeen(any(), any(), any()) } returns Unit
        every { queryAssigneeRepository.findAllByQuery(any()) } returns mutableListOf()

        assertThrows<ApplicationException> {
            queryService.getQueryById(queryId, authenticatedUser)
        }
    }
    @Test
    fun getQueryById_invalid_Access() {

        val queryId = 1L

        every { authenticatedUser.userId } returns 2L
        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("INVALID")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryId, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()

        assertThrows<ApplicationException> {
            queryService.getQueryById(queryId, authenticatedUser)
        }
    }

    @Test
    fun updateStatus() {

        every { queryRepository.findById(queryEntity.id!!) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { groupChatService.updateLastSeen(ChatType.QUERY, queryEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        val newQueryEntity = queryEntity.copy()
        newQueryEntity.status = QueryStatus.RESOLVED

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { queryAssigneeRepository.saveAll(any<List<QueryAssigneeEntity>>()) } returns listOf()
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { groupChatService.updateChatParticipants(any(),any(),any(),any()) } returns Unit
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit


        every { queryRepository.save(any()) } returns newQueryEntity

        val result = queryService.updateStatus(newQueryEntity.id!!, QueryStatus.RESOLVED, authenticatedUser)
        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun updateStatusToOpen() {

        every { queryRepository.findById(queryEntity.id!!) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { groupChatService.updateLastSeen(ChatType.QUERY, queryEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        val newQueryEntity = queryEntity.copy()
        newQueryEntity.status = QueryStatus.OPEN

        every { queryRepository.save(any()) } returns newQueryEntity

        every { authenticatedUser.userType} returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { queryAssigneeRepository.saveAll(any<List<QueryAssigneeEntity>>()) } returns listOf()
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { groupChatService.updateChatParticipants(any(),any(),any(),any()) } returns Unit
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        val result = queryService.updateStatus(newQueryEntity.id!!, QueryStatus.OPEN, authenticatedUser)
        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun testEditQuery() {
        val queryId = 1L
        val updatedQuery = queryEntity.copy()
        updatedQuery.heading = "Updated Heading"

        val updateQueryRequest = createQueryRequest(
            updatedQuery.heading,
            updatedQuery.description,
            updatedQuery.country!!,
            listOf("CAT1"),
            null
        )

        // Mock the repository method
        every { queryRepository.findById(queryId) } returns Optional.of(updatedQuery)
        every { queryRepository.save(updatedQuery) } returns updatedQuery
        every { queryRepository.findById(queryEntity.id!!) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { groupChatService.updateChatParticipants(queryEntity.id!!, ChatType.QUERY, setOf(LOGGED_IN_USER_ID)) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { queryAssigneeRepository.saveAll(any<List<QueryAssigneeEntity>>()) } returns listOf()
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { groupChatService.updateChatParticipants(any(),any(),any(),any()) } returns Unit
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        // Call the function to edit the query
        val result = queryService.editQuery(queryId, updateQueryRequest, authenticatedUser)

        // Verify the repository method calls
        verify { queryRepository.save(updatedQuery) }

        // Verify the result
        assertEquals(true, result)
    }

    @Test
    fun testEditQueryWithAssignee() {
        val queryId = 1L
        val updatedQuery = queryEntity.copy()
        updatedQuery.heading = "Updated Heading"
        updatedQuery.assignedTo = mutableListOf(ExpertUserEntity(
            id= 134,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"))

        val updateQueryRequest = createQueryRequest(
            updatedQuery.heading,
            updatedQuery.description,
            updatedQuery.country!!,
            listOf("CAT1"),
            listOf(134,133)
        )

        // Mock the repository method
        every { queryRepository.findById(queryId) } returns Optional.of(updatedQuery)
        every { queryRepository.save(updatedQuery) } returns updatedQuery
        every { queryRepository.findById(queryEntity.id!!) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { groupChatService.updateChatParticipants(queryEntity.id!!, ChatType.QUERY, setOf(LOGGED_IN_USER_ID)) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { queryAssigneeRepository.saveAll(any<List<QueryAssigneeEntity>>()) } returns listOf()
        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { groupChatService.updateChatParticipants(any(),any(),any(),any()) } returns Unit
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        every { loginAccountRepository.findById(133) } returns Optional.of(ExpertUserEntity(
            id= 133,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"))

        every { loginAccountRepository.findById(134) } returns Optional.of(ExpertUserEntity(
            id= 134,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"))

        // Call the function to edit the query
        val result = queryService.editQuery(queryId, updateQueryRequest, authenticatedUser)

        // Verify the repository method calls
        verify { queryRepository.save(updatedQuery) }

        // Verify the result
        assertEquals(true, result)
    }

    @Test
    fun testEditQuery_with_Assignees() {
        val queryId = 1L
        val updatedQuery = queryEntity.copy()
        updatedQuery.heading = "Updated Heading"

        val updateQueryRequest = createQueryRequest(
            updatedQuery.heading,
            updatedQuery.description,
            updatedQuery.country!!,
            listOf("CAT1"),
            listOf(10, 11, 12)
        )

        // Mock the repository method
        every { queryRepository.findById(queryId) } returns Optional.of(updatedQuery)
        every { queryRepository.save(updatedQuery) } returns updatedQuery
        every { queryRepository.findById(queryEntity.id!!) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { groupChatService.updateChatParticipants(queryEntity.id!!, ChatType.QUERY, setOf(LOGGED_IN_USER_ID, 11, 12)) } returns Unit
        every { loginAccountRepository.findById(any()) } returns Optional.of(corporateUserEntity)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        every { queryAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { queryAssigneeRepository.saveAll(any<List<QueryAssigneeEntity>>()) } returns listOf()

        every { groupChatService.getVirtualParticipants(any()) } returns listOf()
        every { groupChatService.updateChatParticipants(any(),any(),any(),any()) } returns Unit
        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit

        // Call the function to edit the query
        val result = queryService.editQuery(queryId, updateQueryRequest, authenticatedUser)

        // Verify the repository method calls
        verify { queryRepository.save(updatedQuery) }

        // Verify the result
        assertEquals(true, result)
    }

    @Test
    fun testCreateProposal() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )

        val file = MockMultipartFile(proposal.fileName, "abc.pdf", "text/plain", "Sample content".toByteArray(
            StandardCharsets.UTF_8))

        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { queryProposalRepository.save(any()) } returns proposal
        every { s3Service.uploadFile(file, any(), "test-bucket") } returns Unit
        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        every { queryRepository.touch(any(), any()) } returns Unit

        // Call the function to create the proposal
        val result = queryService.createProposal(queryId, proposal.fileName, proposal.fileType!!, proposal.fileSize, file, authenticatedUser)

        // Verify the result
        assertEquals(proposal.id, result)
    }

    @Test
    fun testCreateProposal_s3_upload_throws_Exception() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )

        val file = MockMultipartFile(
            proposal.fileName, null, "text/plain", "Sample content".toByteArray(
                StandardCharsets.UTF_8
            )
        )


        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(
            queryEntity
        )

        every { queryProposalRepository.save(any()) } returns proposal
        every { s3Service.uploadFile(file, any(), "test-bucket") } throws (Exception())

        // Call the function to create the proposal
        assertThrows<Exception> {
            queryService.createProposal(
                queryId,
                proposal.fileName,
                proposal.fileType!!,
                proposal.fileSize,
                file,
                authenticatedUser
            )
        }
    }

    @Test
    fun testDeleteProposal() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )

        every { queryRepository.save(queryEntity) } returns queryEntity
        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { queryProposalRepository.save(any()) } returns proposal
        every { s3Service.deleteFileByS3Key(any(), any()) } returns Unit
        every { queryProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, queryId, ChatType.QUERY) } returns Optional.of(proposal)
        every { queryProposalRepository.deleteById(proposal.id!!) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        val result = queryService.deleteProposal(queryId, proposal.id!!, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertEquals(true, result)
    }

    @Test
    fun testDeleteProposal_s3_delete_throws_Exception() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )

        every { queryRepository.save(queryEntity) } returns queryEntity
        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)

        every { queryProposalRepository.save(any()) } returns proposal
        every { s3Service.deleteFileByS3Key(any(), any()) } throws(Exception())
        every { queryProposalRepository.findAllByTypeAndReferenceId(ChatType.QUERY, queryId) } returns listOf(proposal)

        assertThrows<Exception> {
            queryService.deleteProposal(queryId, proposal.id!!, authenticatedUser)
        }
    }

    @Test
    fun testViewProposal() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )
        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!)} returns Optional.of(queryEntity)
        every { queryProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, queryId, ChatType.QUERY) } returns Optional.of(proposal)
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        val result = queryService.viewProposalUrl(queryId, proposal.id!!, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!.isEmpty())
    }
    @Test
    fun testViewProposal_throws_Exception() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.QUERY,
            referenceId = queryId
        )
        every { queryRepository.findById(queryId) } returns Optional.of(queryEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { queryProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, queryId, ChatType.QUERY) } returns Optional.empty()
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        assertThrows<ApplicationException>{
            queryService.viewProposalUrl(queryId, proposal.id!!, authenticatedUser)
        }
    }
    @Test
    fun testApproveProposals() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.QUERY,
            referenceId = queryId
        )
        val approvedQuery = queryEntity.copy()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(approvedQuery)
        every { queryProposalRepository.findAllByTypeAndReferenceId(ChatType.QUERY, queryId) } returns listOf(proposal)

        every { queryRepository.save(approvedQuery) } returns approvedQuery
        every { queryProposalRepository.saveAll(listOf(proposal)) } returns listOf(proposal)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        val result = queryService.approveQueryProposals(queryId, authenticatedUser)

        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun testApproveProposals_when_no_proposals_present() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.QUERY,
            referenceId = queryId
        )
        val approvedQuery = queryEntity.copy()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(approvedQuery)
        every { queryProposalRepository.findAllByTypeAndReferenceId(ChatType.QUERY, queryId) } returns listOf()

        every { queryRepository.save(approvedQuery) } returns approvedQuery
        every { queryProposalRepository.saveAll(listOf(proposal)) } returns listOf(proposal)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        val result = queryService.approveQueryProposals(queryId, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!)
    }

    @Test
    fun testDownloadProposal() {

        val queryProposalId = 1L
        val queryProposalFileName = "proposal.docx"
        val queryProposalContent = "Proposal content".toByteArray()
        val queryProposal = ProposalEntity(id = queryProposalId, fileName = queryProposalFileName, fileSize = queryProposalContent.size.toLong(), fileUploadDate = LocalDateTime.now(), fileType = "docx", type = ChatType.QUERY, referenceId = queryEntity.id!!)

        every { queryProposalRepository.findByIdAndReferenceIdAndType(queryProposalId, queryEntity.id!!, ChatType.QUERY) } returns Optional.of(queryProposal)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { s3Service.downLoadFile(any(), "queries/${queryEntity.id}/${queryProposalId}/${queryProposalFileName}") } returns ByteArrayInputStream(queryProposalContent)

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)
        // Call the downloadProposal function
        val result: ResponseEntity<StreamingResponseBody> = queryService.downloadProposal(queryEntity.id!!, queryProposalId, authenticatedUser)

        // Verify the assertions
        assertEquals(HttpStatus.OK, result.statusCode)
        assertEquals(MediaType.APPLICATION_OCTET_STREAM, result.headers.contentType)
        assertTrue(result.headers[HttpHeaders.CONTENT_DISPOSITION]?.get(0)!!.contains(queryProposalFileName))
        assertNotNull(result.body)
    }

    @Test
    fun testDownloadProposal_proposal_not_found() {

        val queryProposalId = 1L
        val queryProposalFileName = "proposal.docx"
        val queryProposalContent = "Proposal content".toByteArray()

        every { queryProposalRepository.findByIdAndReferenceIdAndType(queryProposalId, queryEntity.id!!, ChatType.QUERY) } returns Optional.empty()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(queryEntity)
        every { s3Service.downLoadFile(any(), "queries/${queryEntity.id}/${queryProposalId}/${queryProposalFileName}") } returns ByteArrayInputStream(queryProposalContent)
        every { queryProposalRepository.deleteById(queryProposalId) } returns Unit

        every { authenticatedUser.userType } returns UserType.CORPORATE.toString()
        every { corporateRepository.findIdById(any()) } returns EntityIdDto(5)

        assertThrows<ApplicationException>{
            queryService.downloadProposal(queryEntity.id!!, queryProposalId, authenticatedUser)
        }
    }

    @Test
    fun testGetProposalsById() {
        // Mock the input parameters
        val queryId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.QUERY,
            referenceId = queryId
        )
        proposal.createdBy = 10

        val newQueryEntity = queryEntity.copy()

        // Mock authenticated user
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { authenticatedUser.userId } returns LOGGED_IN_USER_ID
        every { authenticatedUser.companyId } returns corporateEntity.id
        every { authenticatedUser.visibilities } returns listOf(UserAccess("QUERY", mutableListOf("FULL")))
        
        // Mock corporate repository
        every { corporateRepository.findIdById(5) } returns EntityIdDto(corporateEntity.id!!)
        every { corporateRepository.findById(corporateEntity.id!!) } returns Optional.of(corporateEntity)
        
        // Mock query repository
        every { queryRepository.findByIdAndCreatedByCorporateId(queryId, 5) } returns Optional.of(newQueryEntity)
        every { queryRepository.findById(queryId) } returns Optional.of(newQueryEntity)

        // Mock other repositories
        every { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, corporateEntity.id!!) } returns Optional.of(newQueryEntity)
        every { queryProposalRepository.findAllByTypeAndReferenceId(ChatType.QUERY, newQueryEntity.id!!) } returns listOf(proposal)
        every { loginAccountRepository.getFirstNameLastNameById(any()) } returns "Test User"
        every { proposalService.getAllProposals(any(), any()) } returns listOf(proposal)
        every { queryRepository.findById(queryId) } returns Optional.of(newQueryEntity)

        val result = queryService.getProposalsByQuery(queryId, authenticatedUser)

        // Assertions
        assertNotNull(result)
        assertFalse(result!!.isEmpty())
        assertEquals(proposal.id, result[0].proposalId)
        
        // Verify
        verify { corporateRepository.findIdById(5) }
        verify { queryRepository.findByIdAndCreatedByCorporateId(queryId, 5) }
        verify { baseRepository.findByIdAndCreatedByCorporateId(queryEntity.id!!, 5) }
    }

}