package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.properties.AwsS3Properties
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.util.CorporateAccessUtil
import io.ktor.util.*
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime
import java.util.*

class AdminUserServiceTest {

    private lateinit var adminUserService: AdminUserService
    private val loginAccountRepository = mockk<LoginAccountRepository>()
    private val corporateUserRepository = mockk<CorporateUserRepository>()
    private val corporateRepository = mockk<CorporateRepository>()
    private val validationTokenRepository = mockk<ValidationTokenRepository>()
    private val expertUserRepository = mockk<ExpertUserRepository>()
    private val expertCompanyProfileRepository = mockk<ExpertCompanyProfileRepository>()
    private val passwordEncoder = mockk<PasswordEncoder>()
    private val onBoardingRepository = mockk<OnBoardingRepository>()
    private val backofficeUserRepository = mockk<BackofficeUserRepository>()
    private val tokenVerificationService = mockk<TokenVerificationService>()
    private val adminAuthoritiesRepository = mockk<AdminAuthoritiesRepository>()
    private val bandsRepository = mockk<BandsRepository>()
    private val clientDocRepository = mockk<ClientDocRepository>()
    private val reminderRepository = mockk<ReminderRepository>()
    private val passportVisaRepository = mockk<PassportVisaRepository>()
    private val clientDocFileRepository = mockk<ClientDocFileRepository>()
    private val openAIService = mockk<OpenAIService>()
    private val s3Prop:AwsS3Properties = mockk(relaxed = true)
    private val awsS3Service:AwsS3Service = mockk(relaxed = true)
    private val corporateAccessUtil: CorporateAccessUtil = mockk()

    @BeforeEach
    fun setUp() {
        adminUserService = AdminUserService(
            "user-profile-folder",
            listOf("image/png", "image/jpeg", "image/jpg"),
            "corporate-doc-folder",
            "user-doc-folder",
            loginAccountRepository,
            corporateUserRepository,
            corporateRepository,
            validationTokenRepository,
            expertUserRepository,
            expertCompanyProfileRepository,
            passwordEncoder,
            onBoardingRepository,
            backofficeUserRepository,
            tokenVerificationService,
            awsS3Service,
            adminAuthoritiesRepository,
            bandsRepository,
            clientDocRepository,
            reminderRepository,
            passportVisaRepository,
            clientDocFileRepository,
            openAIService,
            corporateAccessUtil
        )
    }

    @Test
    fun `deleteUser should delete corporate user`() {
        val userId = 1L
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccountEntity = mockk<LoginAccountEntity>()

        every { loginAccountEntity.id } returns 4

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.getUserType() } returns UserType.CORPORATE
        every { corporateUserRepository.deleteById(userId) } just Runs
        every { onBoardingRepository.deleteAllByRootUserId(any()) } returns Unit
        every { corporateUserRepository.deleteById(any()) } returns Unit
        every { bandsRepository.deleteByCorporateRootUserId(any()) } returns Unit
        every { corporateRepository.deleteAllByRootUserId(any()) } returns Unit
        every { validationTokenRepository.deleteAllByUserId(any()) } returns Unit

        val result = adminUserService.deleteUser(userId, authenticatedUser)

        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result.get())
        verify { corporateUserRepository.deleteById(4) }
    }

    @Test
    fun `deleteUser should delete backoffice user`() {
        val userId = 1L
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccountEntity = mockk<LoginAccountEntity>()

        every { loginAccountEntity.id } returns 4

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.getUserType() } returns UserType.BACKOFFICE
        every { corporateUserRepository.deleteById(userId) } just Runs
        every { onBoardingRepository.deleteAllByRootUserId(any()) } returns Unit
        every { corporateUserRepository.deleteById(any()) } returns Unit
        every { bandsRepository.deleteByCorporateRootUserId(any()) } returns Unit
        every { corporateRepository.deleteAllByRootUserId(any()) } returns Unit
        every { validationTokenRepository.deleteAllByUserId(any()) } returns Unit
        every { adminAuthoritiesRepository.deleteAllByUserId(any()) } returns Unit
        every { backofficeUserRepository.deleteById(any()) } returns Unit

        val result = adminUserService.deleteUser(userId, authenticatedUser)

        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result.get())
    }

    @Test
    fun `deleteUser should delete expert user`() {
        val userId = 1L
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccountEntity = mockk<LoginAccountEntity>()

        val expertUserEntity = ExpertUserEntity(
            id = 1,
            jobTitle = "SDE",
            countryRegionId = 2,
            expertiseId = 3,
            expertises = listOf(),
            bio = "",
            displayName = "",
            infoVideoUrl = "",
            contactNumber = "123456",
            contactEmail = "<EMAIL>",
            contactWebsite = "a.com",
            companyProfile = ExpertCompanyProfileEntity(
                id = 5,
                name = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                users = mutableListOf(),
                lastUpdatedBy = 1,
                companyNumber = "123",
                companyAddress = "",
                aboutBusiness = "",
                effectiveDate = LocalDateTime.now(),
                effectiveEndDate = LocalDateTime.now(),
                contractAcceptedDate = LocalDateTime.now(),
                feesCurrency = "",
                feesAmount = "",
                specialTerms = "",
                membershipStatus = "",
                territory = "",
                renewContract = false,
                services = "",
                account = AccountEntity(
                    id = 6,
                    name = "",
                    status = AccountStatus.ACTIVE,
                    description = "",
                    companyName = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    cases = mutableListOf()
                ),
                profileImage = "",
                expertContract =  null,
                status = AccountStatus.ACTIVE,
                questionsQuota = 13,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 6
            ),
            expertType = "",
            viewContract = false,
            case = mutableListOf(),
            profileImage = ""
        )

        every { loginAccountEntity.id } returns 4

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.getUserType() } returns UserType.EXPERT
        every { corporateUserRepository.deleteById(userId) } just Runs
        every { onBoardingRepository.deleteAllByRootUserId(any()) } returns Unit
        every { corporateUserRepository.deleteById(any()) } returns Unit
        every { bandsRepository.deleteByCorporateRootUserId(any()) } returns Unit
        every { corporateRepository.deleteAllByRootUserId(any()) } returns Unit
        every { validationTokenRepository.deleteAllByUserId(any()) } returns Unit
        every { expertUserRepository.findById(any()) } returns Optional.of(expertUserEntity)
        every { expertUserRepository.findAllByCompanyProfile(any()) } returns listOf(expertUserEntity)
        every { expertUserRepository.deleteById(any()) } returns Unit
        every { expertCompanyProfileRepository.deleteById(any()) } returns Unit

        val result = adminUserService.deleteUser(userId, authenticatedUser)

        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result.get())
    }

    @Test
    fun `temporaryPassword should generate and return temporary password`() {
        val userId = 1L
        val authenticatedUser = mockk<AuthenticatedUser>()
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)
        val validationToken = mockk<ValidationTokenEntity>(relaxed = true)

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccount)
        every { loginAccountRepository.save(any()) } returns loginAccount
        every { passwordEncoder.encode(any()) } returns "encodedPassword"
        every { validationTokenRepository.findByUserId(userId) } returns validationToken
        every { validationTokenRepository.save(validationToken) } returns validationToken

        val result = adminUserService.temporaryPassword(userId, authenticatedUser)

        assertNotNull(result)
        verify { validationTokenRepository.save(validationToken) }
    }

    @Test
    fun `uploadProfilePicture should upload and return profile picture URL`() {
        val userId = 1L
        val requestedByUserId = 2L
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)
        var file= mockk<MultipartFile>(relaxed = true)
        var awsS3FileMetadata: AwsS3FileMetadata = mockk<AwsS3FileMetadata>()

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccount)
        every { awsS3Service.uploadFile(any(), any(), any<Boolean>()) } returns awsS3FileMetadata
        every { loginAccountRepository.save(loginAccount) } returns loginAccount
        every { file.bytes } returns hex("89504E470D0A1A0A")
        every { loginAccount.profilePhotoUrl } returns ""
        every { awsS3FileMetadata.key } returns ""
        every { awsS3Service.getS3Url(any()) } returns ""

        val result = adminUserService.uploadProfilePicture(userId, requestedByUserId, file)

        assertNotNull(result)
        //verify { awsS3Service.uploadFile(photo, any(), any()) }
    }

    @Test
    fun `deleteProfilePicture should delete the profile picture and return success`() {
        val userId = 1L
        val requestedById = 2L
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)

        every { loginAccountRepository.findById(userId) } returns Optional.of(loginAccount)
        every { loginAccount.profilePhotoUrl } returns "some/photo/key"
        every { awsS3Service.deleteFileByS3Key(any()) } just Runs
        every { loginAccountRepository.save(any()) } returns loginAccount

        val result = adminUserService.deleteProfilePicture(userId, requestedById)

        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        verify { awsS3Service.deleteFileByS3Key("some/photo/key") }
        verify { loginAccountRepository.save(loginAccount) }
    }

    /*@Test
    fun `uploadDocumentCorporate should upload document and return true`() {
        val request = mockk<CorporateDocumentRequest>(relaxed = true)
        val corporateId = 1L
        val authenticatedUser = mockk<AuthenticatedUser>(relaxed = true)

        authenticatedUser.accesses = listOf(UserAccess("COMPANY_INFO", mutableListOf("VIEW_DOCUMENTS")))

        every { authenticatedUser.accesses } returns listOf(UserAccess("COMPANY_INFO", mutableListOf("VIEW_DOCUMENTS")))
        //every { UserAccessUtil.hasAccessToCompanyDocuments(authenticatedUser) } returns true

        every { clientDocRepository.save(any()) } returns mockk(relaxed = true)
        every { reminderRepository.save(any()) } returns mockk()

        every { s3Prop.defaultBucket } returns "test-bucket"
        every { s3Prop.publicBucketUri } returns "test-bucket"
        //every { awsS3Service.uploadFromTmp(any(), any()) } returns mockk()
        every { awsS3Service.uploadFromTmp(any(), any()) } answers { true }

        val result = adminUserService.uploadDocumentCorporate(request, corporateId, authenticatedUser)

        assert(result)
        verify { awsS3Service.uploadFromTmp(any(), any(), any()) }
        verify { clientDocRepository.save(any()) }
    }

    @Test
    fun `uploadDocumentUser should upload user document and return true`() {
        val request = mockk<UserDocumentRequest>(relaxed = true)
        val user = mockk<AuthenticatedUser>(relaxed = true)
        val userId = 1L

        every { loginAccountRepository.findByIdOrNull(any()) } returns mockk<CorporateUserEntity>(relaxed = true)
        every { clientDocRepository.save(any()) } returns mockk(relaxed = true)
        every { clientDocFileRepository.save(any()) } returns mockk(relaxed = true)
        every { awsS3Service.uploadFromTmp(any(), any()) } just Runs

        val result = adminUserService.uploadDocumentUser(request, user, userId)

        assertTrue(result)
        verify { clientDocRepository.save(any()) }
        verify { awsS3Service.uploadFromTmp(any(), any()) }
    }

    @Test
    fun `updatePassport should update passport details and return true`() {
        val request = mockk<PassportDocumentRequest>(relaxed = true)
        val user = mockk<AuthenticatedUser>(relaxed = true)
        val userId = 1L

        every { loginAccountRepository.findByIdOrNull(any()) } returns mockk<CorporateUserEntity>(relaxed = true)
        every { passportVisaRepository.save(any()) } returns mockk(relaxed = true)
        every { clientDocRepository.save(any()) } returns mockk(relaxed = true)
        every { clientDocFileRepository.save(any()) } returns mockk(relaxed = true)
        every { awsS3Service.uploadFromTmp(any(), any()) } just Runs

        val result = adminUserService.updatePassport(request, user, userId)

        assertTrue(result)
        verify { passportVisaRepository.save(any()) }
    }


    @Test
    fun `updateVisa should update visa details and return true`() {
        val request = mockk<VisaDocumentRequest>(relaxed = true)
        val user = mockk<AuthenticatedUser>(relaxed = true)
        val userId = 1L

        every { loginAccountRepository.findByIdOrNull(any()) } returns mockk<CorporateUserEntity>(relaxed = true)
        every { passportVisaRepository.save(any()) } returns mockk(relaxed = true)
        every { clientDocRepository.save(any()) } returns mockk(relaxed = true)
        every { clientDocFileRepository.save(any()) } returns mockk(relaxed = true)
        every { awsS3Service.uploadFromTmp(any(), any()) } just Runs

        val result = adminUserService.updateVisa(request, user, userId)

        assertTrue(result)
        verify { passportVisaRepository.save(any()) }
    }*/

}
