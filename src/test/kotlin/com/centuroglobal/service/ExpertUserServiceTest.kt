package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.CompanyRequest
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.CreateSecondaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateExpertUserRequest
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.ExpertUserReferenceData
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.service.mail.TemplateEngineWrapper
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDateTime
import java.util.*


class ExpertUserServiceTest {

    private val contractVersionRepository: ContractVersionRepository = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository = mockk()
    private val tokenVerificationService: TokenVerificationService = mockk()
    private val countryService: CountryService = mockk()
    private val awsS3Service: AwsS3Service = mockk()
    private val mailSendingService: MailSendingService = mockk()
    private val templateEngineWrapper: TemplateEngineWrapper = mockk()
    private val masterContentRepository: MasterContentRepository = mockk()
    private val expertiseService: ExpertiseService = mockk()
    private val accountEntityRepository: AccountEntityRepository = mockk()
    private val partnerRepository: PartnerRepository = mockk()
    private val expertContractRepository: ExpertContractRepository = mockk()
    private val userProfileFolder = "test-bucket"
    private val companyLogoFolder = "test"
    private val maxBioLength = 2
    private val expertContactToEmail = "testuser"
    private val expertContactCCEmail = listOf("test", "user")
    private val memberContractToEmail = "testmember"
    private val memberContractCCEmail = listOf("test", "user")
    private val memberContractS3Bucket = "testBucket"

    private val caseRepository:CaseRepository= mockk()
    private val validationTokenRepository: ValidationTokenRepository= mockk()
    private val clientViewRepository: ClientViewRepository= mockk()
    private val onBoardingRepository: OnBoardingRepository= mockk()
    private val corporateRepository: CorporateRepository = mockk()

    private val expertUserService = ExpertUserService(
        userProfileFolder,
        companyLogoFolder,
        maxBioLength,
        expertContactToEmail,
        expertContactCCEmail,
        memberContractToEmail,
        memberContractCCEmail,
        memberContractS3Bucket,
        contractVersionRepository,
        expertUserRepository,
        expertCompanyProfileRepository,
        tokenVerificationService,
        countryService,
        awsS3Service,
        mailSendingService,
        templateEngineWrapper,
        masterContentRepository,
        expertiseService,
        accountEntityRepository,
        partnerRepository,
        expertContractRepository,
        caseRepository,
        validationTokenRepository,
        clientViewRepository,
        onBoardingRepository,
        corporateRepository = corporateRepository
    )


    private val expertContractEntity = ExpertContractEntity(
        contractText = "abc"
    )

    private val corporateUserEntity: CorporateUserEntity = mockk()
    private val authenticatedUser: AuthenticatedUser = mockk()
    private val loginAccountEntity: LoginAccountEntity = mockk()
    private val corporateEntity = CorporateEntity(
        1,
        "test",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1,
        null,
        listOf(corporateUserEntity),
        1
    )

    @BeforeEach
    fun setup() {
        every { awsS3Service.updateProfilePicture(any(), any(), any()) } returns "test"
        every { countryService.retrieveByCountryCode(any()) } returns Country("IN", "test", "89", null)
        every { expertContractRepository.save(any()) } returns expertContractEntity
        every { expertCompanyProfileRepository.save(any()) } returns expertCompanyProfile
        every { expertCompanyProfileRepository.findById(any()) } returns Optional.of(expertCompanyProfile)
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }
    private val accountEntity = AccountEntity(
        name = "tet",
        status = AccountStatus.ACTIVE,
        corporate = null
    )
    private val expertUser2 = ExpertUserEntity(
        id = 1L,
        contactEmail = "<EMAIL>",
        contactNumber = "9123",
        contactWebsite = "1@test",
        displayName = "test",
        expertType = "ACTIVE",
        jobTitle = "string")

    private val expertUser = ExpertUserEntity(
        id = 1L,
        contactEmail = "<EMAIL>",
        contactNumber = "9123",
        contactWebsite = "1@test",
        displayName = "test",
        expertType = ExpertType.PRIMARY.toString(),
        jobTitle = "string")

    private val expertCompanyProfile = ExpertCompanyProfileEntity(
        2L,
        name = "tet",
        companyNumber = "34",
        companyAddress = "Arora",
        aboutBusiness = "mxs",
        effectiveDate = TimeUtil.fromInstant(5L / 1000),
        effectiveEndDate = TimeUtil.fromInstant(6L / 1000),
        feesCurrency = "INR",
        feesAmount = "9000",
        specialTerms = "NA",
        membershipStatus = "NA",
        services = "NA",
        territory = "kl",
        contractAcceptedDate = TimeUtil.fromInstant(8L / 1000),
        lastUpdatedBy = 2L,
        account = accountEntity,
        profileImage = "al",
        expertContract = expertContractEntity,
        logoKey = "",
        size = CompanySize.Size1,
        summary = "",
        users = mutableListOf(expertUser),
        renewContract = false,
        status = AccountStatus.ACTIVE,
        questionsQuota = 2L,
        associatedPartners = mutableListOf(),
        companyType = ExpertCompanyType.EXPERT,
        invitedBy = 2L
    )
    private val expertUserEntity = ExpertUserEntity(
        2L, "dev", 7, 7, listOf(), "kk", "hdhfj", "abdh", "876",
        "hkhk", "jijk", expertCompanyProfile,
        "hfdk", true, mutableListOf(), "djs"
    );



    private val updateExpertUserRequest = UpdateExpertUserRequest(
        firstName = "test",
        lastName = "user",
        jobTitle = "admin",
        country = "US",
        publicPicS3Key = "testKey"
    )

    private val partnerEntity = PartnerEntity(
        id = 4L,
        name = "",
        createdFrom = PartnerType.EXPERT,
        contractFromDate = LocalDateTime.now(),
        contractToDate = LocalDateTime.now(),
        casesManaged = PartnerCaseType.CG,
        queriesManaged = PartnerCaseType.CG,
        companyLogo = "",
        themePrimaryColor = "",
        themeSecondaryColor = "",
        rootUserId = 5L,
        status = CorporateStatus.ACTIVE,
        corporates = listOf(corporateEntity),
        partnerUsers = listOf(expertUserEntity).toMutableList(),
        country = "IN",
        band = null,
        referenceId = 5L,
        associatedCompanies = mutableListOf(),
        corporateAccess = ""
    )





    private fun generatePrimaryExpertUserRequest(): CreatePrimaryExpertUserRequest {
        return CreatePrimaryExpertUserRequest(
            "<EMAIL>",
            "test",
            "PJ",
            "Developer",
            "IN",
            "",
            CompanyRequest(
                "tet", "34", "Arora", "mxs", "qmw",
                "nl", "5", "small", 5L, 6L, "INR", "9000",
                "NA", "NA", "NA", "kl", 8L,
                6L, "abc", 0L
            ),
            aiMessageCount = 0
        )

    }

    private fun generateSecondaryExpertUserRequest(): CreateSecondaryExpertUserRequest {
        return CreateSecondaryExpertUserRequest("<EMAIL>", "test", "user", "dev",
            "IN", "dk", 4L, "",5L)
    }

    @Test
    fun `create Primary expert user`() {

        val request = generatePrimaryExpertUserRequest()
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { partnerRepository.findById(any()) } returns Optional.of(partnerEntity)
        every { expertUserRepository.save(any()) } returns expertUserEntity
        every {
            tokenVerificationService.createToken(
                expertUserEntity,
                ValidationType.INVITE_PARTNER_EXPERT
            )
        } returns Unit

        every { tokenVerificationService.createToken(any(),any()) } returns Unit

        val result = expertUserService.createPrimaryExpertUser(4L, request)
        assertNotNull(result)
    }

    @Test
    fun `create Primary expert user-exception flow`() {

        val request = generatePrimaryExpertUserRequest()
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { countryService.retrieveByCountryCode(any()) } returns Country("IN", "test", "89", null)
        every { partnerRepository.findById(any()) } returns Optional.of(partnerEntity)
        every { expertUserRepository.save(any()) } returns expertUserEntity
        every {
            tokenVerificationService.createToken(
                expertUserEntity,
                ValidationType.INVITE_BACKOFFICE
            )
        } returns Unit


        assertThrows<ApplicationException> { val result = expertUserService.createPrimaryExpertUser(4L, request) }
    }

    @Test
    fun `create Secondary expert user`() {

        val request = generateSecondaryExpertUserRequest()
        every { partnerRepository.findById(any()) } returns Optional.of(partnerEntity)
        every { expertUserRepository.save(any()) } returns expertUserEntity
        every { expertUserRepository.findById(any()) } returns Optional.of(expertUserEntity)

        every {
            tokenVerificationService.createToken(
                any(),
                ValidationType.INVITE_EXPERT
            )
        } returns Unit

        val result = expertUserService.createSecondaryExpertUser(4L, request)
        assertNotNull(result)
    }

    @Test
    fun `create Secondary expert user - exception flow`() {

        val request = generateSecondaryExpertUserRequest()
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { partnerRepository.findById(any()) } returns Optional.of(partnerEntity)

        every { expertUserRepository.save(any()) } returns expertUserEntity
        every { expertUserRepository.findById(any()) } returns Optional.of(expertUserEntity)

        every {
            tokenVerificationService.createToken(
                any(),
                ValidationType.INVITE_PARTNER_EXPERT
            )
        } returns Unit
        assertThrows<ApplicationException> {
            val result = expertUserService.createSecondaryExpertUser(4L, request)
        }
    }

    @Test
    fun `Retrieve Primary Expert and company Profile`() {
        every { expertUserRepository.findAllByExpertType(ExpertType.PRIMARY.toString()) } returns listOf(
            expertUserEntity
        )

        every { authenticatedUser.partnerId } returns 3

        every { expertUserRepository.findAllByExpertTypeAndCompanyProfileAssociatedPartnersId(any(), any()) } returns listOf(expertUserEntity)

        every { expertUserRepository.findByCompanyProfile(any()) } returns listOf(ExpertUserReferenceData(
            id = 5,
            email = "<EMAIL>",
            firstName = "a",
            lastName = "b"
        ))

        val primaryExpert = expertUserService.retrievePrimaryExpert(authenticatedUser.partnerId);
        val expertprofile = expertUserService.retrieveExpertUsers(expertCompanyProfile)
        assertEquals(primaryExpert.size, 1)
    }

    @Test
    fun `Retrieve  Expert Users`() {
        every { expertUserRepository.findAllByExpertType(ExpertType.PRIMARY.toString()) } returns listOf(
            expertUserEntity
        )

        every { authenticatedUser.partnerId } returns 3
        every { expertUserRepository.findAllByExpertTypeAndCompanyProfileAssociatedPartnersId(any(), any()) } returns listOf(expertUserEntity)

        val primaryExpert = expertUserService.retrievePrimaryExpert(authenticatedUser.partnerId);
        assertEquals(primaryExpert.size, 1)
    }

    @Test
    fun `Update Expert User info`() {
        val partnerId = 4L
        val id = 2L
        every { expertUserRepository.findByIdAndPartnerId(id, partnerId) } returns expertUserEntity
        every { expertUserRepository.save(any()) } returns expertUserEntity

        every { expertUserRepository.findById(any()) } returns Optional.of(expertUserEntity)

        val idExpertInfo = expertUserService.updateExpertUserInfo(updateExpertUserRequest, id, partnerId);
        assertEquals(idExpertInfo, id)
    }

    @Test
    fun `Update Expert User info-exception flow`() {
        val partnerId = 4L
        val id = 2L

        every { expertUserRepository.findByIdAndPartnerId(id, partnerId) } returns expertUserEntity
        assertThrows<ApplicationException> {
            val idExpertInfo = expertUserService.updateExpertUserInfo(updateExpertUserRequest, id, partnerId);
        }

    }

}