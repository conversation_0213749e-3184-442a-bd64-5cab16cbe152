package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.CircleEntity
import com.centuroglobal.shared.data.entity.CircleMemberEntity
import com.centuroglobal.shared.data.entity.CircleRequestEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.view.CircleView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.pojo.circle.CircleBannerUploadResponse
import com.centuroglobal.shared.data.pojo.circle.CreateUpdateCircleRequest
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.CircleViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import junit.framework.TestCase.assertEquals
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime
import java.util.*


class CircleServiceTest {
    private val circleId = 10L

    private val circleEntity = CircleEntity(
        id = circleId,
        name = "Test Circle",
        members = mutableListOf(),
        about = "This is a private circle",
        circleType = CircleType.PUBLIC,
        status = CircleStatus.ACTIVE,
        lastUpdatedBy = circleId,
        createdBy=circleId
    )
    private val circleView=  CircleView(
        id = circleId,
        name = "Test Circle",
        members = 15,
        about = "This is a private circle",
        circleType = CircleType.PUBLIC,
        status = CircleStatus.ACTIVE,
        expertiseIds = "15",
        countryCodesString= "String trial",
        createdDate= LocalDateTime.now(),
        expertiseId = 15,
        invitee = 1,
        requests = 1

    )


    private val circleRepository: CircleRepository = mockk()
    private val expertiseService: ExpertiseService = mockk<ExpertiseService>()
    private val circleMemberRepository: CircleMemberRepository = mockk<CircleMemberRepository>()
    private val circleRequestRepository: CircleRequestRepository = mockk<CircleRequestRepository>()
    private val circleResponseTrailRepository: CircleResponseTrailRepository = mockk()
    private val circleViewRepository: CircleViewRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val expertUserService: ExpertUserService = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val awsS3Service: AwsS3Service = mockk()
    private val circleBannerFolder: String = ""
    private val circleService = CircleService(
        circleBannerFolder,
        circleRepository,
        expertiseService,
        circleMemberRepository,
        circleRequestRepository,
        circleResponseTrailRepository,
        circleViewRepository,
        loginAccountRepository,
        expertUserService,
        expertUserRepository,
        awsS3Service
    )

    private val authenticatedUser: AuthenticatedUser = mockk {
        every { userId } returns 10L
        every { companyId } returns 5L
        every { email } returns "<EMAIL>"
        every { userType } returns "EXPERT"
    }

    private val expertCompanyProfile =ExpertCompanyProfile(
        name = "name",
        companyNumber = "*********",
        companyAddress = "companyAddress",
        aboutBusiness = "aboutBusiness",
        effectiveDate =TimeUtil.toEpochMillis(LocalDateTime.now()),
        effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
        feesCurrency = "feesCurrency",
        feesAmount = "feesAmount",
        specialTerms = "specialTerms",
        membershipStatus = "membershipStatus",
        services = "services",
        territory = "territory",
        size = CompanySize.Size0,
        logoFullUrl = "logofullurl",
        sizeName = "M",
        summary = "summary"
    )

    private val expertUsers = ExpertUserEntity(
        id = 1L,
        contactEmail = "<EMAIL>",
        contactNumber = "*********",
        contactWebsite = "mail.com",
        displayName = "Trial",
        infoVideoUrl = "url.com",
        jobTitle = "Manager",
        expertType = "USER"
    )


    @BeforeEach
    fun setup() {
        every { circleRepository.save(circleEntity) } returns circleEntity
        every {
            circleRequestRepository.findTopByCircleAndUserIdOrderByIdDesc(
                circleEntity,
                10L
            )
        } returns CircleRequestEntity(
            id = 1L,
            circle = circleEntity,
            lastUpdatedBy = 10L,
            userId = 10L

        )
        every { circleRequestRepository.save(any()) } returns CircleRequestEntity(
            id = 1,
            userId = 10,
            circleRequestStatus = CircleRequestStatus.REQUESTED,
            circle = circleEntity,
            lastUpdatedBy = 10,
            isActive = false
        )


        every { authenticatedUser.userId } returns 10L
        every { authenticatedUser.companyId } returns 5L
        every { authenticatedUser.email } returns "<EMAIL>"

        every { circleRepository.findByIdOrNull(circleId) } returns circleEntity
        every { expertiseService.retrieveExpertiseByIds(any()) } returns emptyList()
        every { circleMemberRepository.findTopByCircleAndUserId(any(), any()) } returns CircleMemberEntity(circle=circleEntity, userId = 1L, lastUpdatedBy = 1L)
        every { circleMemberRepository.deleteAll(emptyList<CircleMemberEntity>()) } returns Unit
        every {authenticatedUser.userType} returns "EXPERT"

        every { circleMemberRepository.findTopByCircle_IdAndUserId(any(), any()) } returns CircleMemberEntity(id= 10L, circle=circleEntity, userId = authenticatedUser.userId,lastUpdatedBy = authenticatedUser.userId)
        every { circleEntity.id?.let { circleMemberRepository.findTopByCircle_IdAndUserId(it, authenticatedUser.userId) } } returns CircleMemberEntity(id= 10L, circle=circleEntity, userId = authenticatedUser.userId, lastUpdatedBy = authenticatedUser.userId)
        every { circleMemberRepository.findTop5ByCircle_IdAndCircleMemberStatus(any(), any()) } returns listOf(
            CircleMemberEntity(
                id = 1L,
                circle = circleEntity,
                userId = 1L,
                lastUpdatedBy = 1L
            ),
            CircleMemberEntity(
                id = 2L,
                circle = circleEntity,
                userId = 2L,
                lastUpdatedBy = 2L
            ),
            CircleMemberEntity(
                id = 3L,
                circle = circleEntity,
                userId = 3L,
                lastUpdatedBy = 3L
            ),
            CircleMemberEntity(
                id = 4L,
                circle = circleEntity,
                userId = 4L,
                lastUpdatedBy = 4L
            ),
            CircleMemberEntity(
                id = 5L,
                circle = circleEntity,
                userId = 5L,
                lastUpdatedBy = 5L
            )
        )


        every { expertUserService.retrieveProfileSummary(1L) } returns ExpertProfileSummary(
            id = 1L,
            bio = "Bio",
            companyProfile = expertCompanyProfile,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        every { expertUserService.retrieveProfileSummary(2L) } returns ExpertProfileSummary(
            id = 2L,
            bio = "Bio",
            companyProfile = expertCompanyProfile,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        every { expertUserService.retrieveProfileSummary(3L) } returns ExpertProfileSummary(
            id = 3L,
            bio = "Bio",
            companyProfile = expertCompanyProfile,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        every { expertUserService.retrieveProfileSummary(4L) } returns ExpertProfileSummary(
            id = 4L,
            bio = "Bio",
            companyProfile =expertCompanyProfile,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        every { expertUserService.retrieveProfileSummary(5L) } returns ExpertProfileSummary(
            id = 5L,
            bio = "Bio",
            companyProfile = expertCompanyProfile,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )


    }


//    @Test
//    fun `create Circle test`() {
//        val userId = 10L
//        val request = CreateUpdateCircleRequest(
//            name = "Private Circle",
//            about = "This is a private circle",
//            circleAccessType = CircleType.PUBLIC,
//            status = CircleStatus.ACTIVE,
//            countryCode = listOf("US"),
//            members = listOf(1L, 2L, 3L),
//            expertiseIds = listOf()
//        )
//        val userEntity = CircleEntity(
//            id = userId,
//            name = request.name,
//            about = request.about,
//            circleType = request.circleAccessType,
//            status = request.status,
//            lastUpdatedBy = 10L,
//            createdBy = 10L,
//            countryCodes = request.countryCode
//        )
//
//        val expertSearchFilter1 = ExpertSearchFilter(
//            search = null,
//            countryCode = null,
//            expertiseId = null,
//            expertiseIds = emptyList(),
//            countryCodes = listOf("US")
//        )
//        every { expertUserRepository.searchByCriteriaAndIdInForCircleOrderByProfilePhotoUrlDesc(expertSearchFilter1, AccountStatus.ACTIVE, any<Pageable>()) } returns PageImpl(listOf(expertUsers))
//
//        //every { circleRepository.save(any<CircleEntity>()) } returns userEntity
//
//
//        val result = circleService.createCircle(request, authenticatedUser)
//        Assertions.assertNotNull(result)
//        Assertions.assertEquals(userEntity.name, result.name)
//        Assertions.assertEquals(userEntity.circleType, result.circleAccessType)
//        Assertions.assertEquals(userEntity.status, result.status)
//    }



    @Test
    fun `create cirlce throws exception when unexpected error occurs`() {
        val userId = 10L
        val request = CreateUpdateCircleRequest(
            name = "Private Circle",
            about = "This is a private circle",
            circleAccessType = CircleType.PUBLIC,
            status = CircleStatus.ACTIVE,
            countryCode = listOf("US"),
            members = listOf(1L, 2L, 3L),
            expertiseIds = listOf(1, 2, 3)
        )
        val userEntity = CircleEntity(
            id = userId,
            name = request.name,
            about = request.about,
            circleType = request.circleAccessType,
            status = request.status,
            lastUpdatedBy = 1L,
            createdBy = 1L,
            countryCodes = request.countryCode
        )
        every { circleRepository.save(any()) } throws Exception()
        assertThrows<Exception> {
            circleService.createCircle(request, authenticatedUser)
        }
    }

    @Test
    fun `update Circle test`() {
        val userId = 10L

        val request = CreateUpdateCircleRequest(
            name = "Private Circle",
            about = "This is a private circle",
            circleAccessType = CircleType.PUBLIC,
            status = CircleStatus.ACTIVE,
            countryCode = null,
            members = listOf(1L, 2L, 3L),
            expertiseIds = listOf(1, 2, 3)
        )

        val userEntity = CircleEntity(
            id = userId,
            name = request.name,
            about = request.about,
            circleType = request.circleAccessType,
            status = request.status,
            lastUpdatedBy = 1L,
            createdBy = 1L,
            countryCodes = request.countryCode
        )



        //



        val expertsPage = PagedResult(listOf(ExpertProfileSummary(
            id = 1L, bio = "Bio", companyProfile =expertCompanyProfile,
            contactEmail = "<EMAIL>", contactNumber = "*********", contactWebsite = "mail.com", countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"

            ),
            ExpertProfileSummary(
                id = 2L,
                bio = "Bio",
                companyProfile =expertCompanyProfile,
                contactEmail = "<EMAIL>",
                contactNumber = "*********",
                contactWebsite = "mail.com",
                countryCode = "US",
                countryName = "US",
                displayName = "Trial",
                infoVideoUrl = "url.com",
                jobTitle = "Manager",
                profilePictureFullUrl = "url.com"
            ),
            ExpertProfileSummary(
                id = 3L,
                bio = "Bio",
                companyProfile = expertCompanyProfile,
                contactEmail = "<EMAIL>",
                contactNumber = "*********",
                contactWebsite = "mail.com",
                countryCode = "US",
                countryName = "US",
                displayName = "Trial",
                infoVideoUrl = "url.com",
                jobTitle = "Manager",
                profilePictureFullUrl = "url.com"
            )),currentPage=1, totalElements = 2L, totalPages = 3)



        every { authenticatedUser.userId } returns 10L
        every { authenticatedUser.companyId } returns 5L
        every { authenticatedUser.email } returns "<EMAIL>"
        every { circleRepository.findByIdOrNull(circleId) } returns circleEntity
        every { circleRepository.save(any<CircleEntity>()) } returns circleEntity
        every { circleViewRepository.findFirstById(circleId) } returns circleView

        every { circleEntity.id?.let { circleRepository.findById(it) } } returns Optional.of(circleEntity)

        val expertSearchFilter1 = ExpertSearchFilter(
            search = null,
            countryCode = null,
            expertiseId = null,
            expertiseIds = listOf(1,2,3),
            countryCodes = null
        )
        every { expertUserRepository.searchByCriteriaAndIdInForCircleOrderByProfilePhotoUrlDesc(expertSearchFilter1, AccountStatus.ACTIVE, any<Pageable>()) } returns PageImpl(listOf(expertUsers))


        every { circleRepository.save(any<CircleEntity>()) } returns circleEntity

        val pageRequest = PageRequest.of(0, **********,  Sort.unsorted())

        val expertSearchFilter2 = ExpertSearchFilter(
            search = null,
            countryCode = null,
            expertiseId = null,
            expertiseIds = null,
            countryCodes = null
        )
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(expertSearchFilter2, pageRequest, emptyList()) } returns expertsPage

        // perform the test

        val result = circleService.updateCircle(userId, request, authenticatedUser)
        Assertions.assertNotNull(result)
        Assertions.assertEquals(userEntity.circleType, result.circleAccessType)
        Assertions.assertEquals(userEntity.status, result.status)
    }

    @Test
    fun `update circle throws Exception on unexpected error`() {
        val userId = 10L

        val request = CreateUpdateCircleRequest(
            name = "Private Circle",
            about = "This is a private circle",
            circleAccessType = CircleType.PUBLIC,
            status = CircleStatus.ACTIVE,
            countryCode = listOf("US"),
            members = listOf(1L, 2L, 3L),
            expertiseIds = listOf(1, 2, 3)
        )

        val userEntity = CircleEntity(
            id = userId,
            name = request.name,
            about = request.about,
            circleType = request.circleAccessType,
            status = request.status,
            lastUpdatedBy = 1L,
            createdBy = 1L,
            countryCodes = request.countryCode
        )
        every { circleRepository.findByIdOrNull(userId) } returns userEntity
        every { circleMemberRepository.deleteAll(any<List<CircleMemberEntity>>()) } returns Unit
        every { circleRepository.save(any()) } throws Exception()
        assertThrows<Exception> {
            circleService.updateCircle(userId, request, authenticatedUser)
        }
    }

    @Test
    fun `delete circle`() {
        val circleId = 1L
        val circleEntity = CircleEntity(
            id = circleId,
            name = "Test Circle",
            about = "Test Circle Description",
            circleType = CircleType.PUBLIC,
            status = CircleStatus.ACTIVE,
            lastUpdatedBy = authenticatedUser.userId,
            createdBy = authenticatedUser.userId,
            countryCodes = listOf("US")
        )
        every { circleRepository.findByIdOrNull(circleId) } returns circleEntity
        every { circleRepository.saveAndFlush(circleEntity) } returns circleEntity
        every { circleRepository.delete(circleEntity) } returns Unit

        circleService.deleteCircle(circleId, authenticatedUser)

        verify { circleRepository.delete(circleEntity) }
    }
    @Test
    fun `circle status update test`() {
        val circleId = 10L
        val circleStatus = CircleStatus.ACTIVE

        every { circleRepository.findByIdOrNull(circleId) } returns CircleEntity(
            id = circleId,
            name = "Test Circle",
            about = "Test Circle Description",
            circleType = CircleType.PUBLIC,
            status = CircleStatus.INACTIVE,
            lastUpdatedBy = 1L,
            createdBy = 1L,
            countryCodes = listOf("US")
        )
        every { circleRepository.saveAndFlush(any<CircleEntity>()) } returns mockk()

        circleService.circleStatusUpdate(circleId, circleStatus, authenticatedUser)

        verify { circleRepository.findByIdOrNull(circleId) }
        verify { circleRepository.saveAndFlush(any<CircleEntity>()) }
    }



    @Test
    fun `approve circle member test`() {
        val circleId = 10L
        val memberId = 10L


        val circleMemberEntity = CircleMemberEntity(
                id = 10L,
                circle = circleEntity,
                userId = 10L,
                lastUpdatedBy = 10L
            )
        val circleRequestEntity = CircleRequestEntity(
            id = 1L,
            circle = circleEntity,
            userId = memberId,
            lastUpdatedBy = 10L
        )

        every { circleRepository.findByIdOrNull(circleId) } returns circleEntity
        every { circleMemberRepository.findTopByCircleAndUserId(circleEntity, memberId) } returns circleMemberEntity
        every { circleEntity.id?.let { circleRequestRepository.findTopByCircle_IdAndUserIdOrderByIdDesc(it, memberId) } } returns circleRequestEntity

        every { circleMemberRepository.saveAndFlush(any<CircleMemberEntity>()) } returns circleMemberEntity

        circleService.approveMember(true, circleId, memberId, authenticatedUser)

        // Verify that the member is added to the circle and the request is deleted
        verify { circleMemberRepository.saveAndFlush(circleMemberEntity.apply { circleMemberStatus = CircleMemberStatus.ACTIVE }) }
        verify { circleRequestRepository.save(circleRequestEntity.apply { isActive = false }) }
    }

    @Test
    fun `uploadCoverPicture should return success response`() {

        val file = mockk<MultipartFile>()
        val circleBannerUploadResponse = CircleBannerUploadResponse(
            "test-file.jpg",
            "test-file.jpg"
        )


        every { awsS3Service.uploadFile(file, any(), false, any()) } returns AwsS3FileMetadata(
            "test-file.jpg",
            "test-file.jpg",
            "test-file.jpg",
            "test-file.jpg",
            false
            )
        every { file.originalFilename } returns "test-file.jpg"

        every { awsS3Service.getS3Url(any()) } returns "test-file.jpg"

        val response = circleService.uploadCoverPicture(10L, authenticatedUser.userId, file)

        // Verify
        assertEquals(circleBannerUploadResponse, response)
    }



    }
