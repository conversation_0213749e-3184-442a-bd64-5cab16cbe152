package com.centuroglobal.service

//import com.centuroglobal.shared.data.pojo.UpdatePartnerCorporateRequest
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.*
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import com.centuroglobal.shared.data.pojo.AccountSearchFilter
import com.centuroglobal.shared.data.pojo.CorporateUser
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.repository.stripe.StripeAccountRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.stripe.StripeService
import com.centuroglobal.shared.service.subscription.SharedSubscriptionService
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime
import java.util.*

class CorporateServiceTest {
    private val corporateRepository: CorporateRepository = mockk()
    private val corporateUserRepository: CorporateUserRepository = mockk()
    private val stripeAccountRepository: StripeAccountRepository = mockk()
    private val corporateUserService: CorporateUserService = mockk()
    private val countryService: CountryService = mockk()
    private val stripeService: StripeService = mockk()
    private val tokenVerificationService: TokenVerificationService = mockk()
    private val s3Service: AwsS3Service = mockk()
    private val restTemplate: RestTemplate = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val onBoardingRepository: OnBoardingRepository = mockk()
    private val accountEntityRepository: AccountEntityRepository = mockk()
    private val bandsRepository: BandsRepository = mockk()
    private val caseDashboardService: CaseDashboardService = mockk()
    private val recaptchaService: RecaptchaService = mockk()
    private val partnerRepository: PartnerRepository = mockk()
    //private val corporate : Corporate = mockk()
    private val userProfileFolder = "test-bucket"
    private val caseRepository: CaseRepository = mockk()
    private val queryRepository : QueryRepository = mockk()
    private val rfpRepository : RfpRepository = mockk()
    private val clientViewRepository: ClientViewRepository= mockk()
    private val validationTokenRepository: ValidationTokenRepository= mockk()
    private val clientDocRepository: ClientDocRepository = mockk()
    private val subscriptionService: SubscriptionService = mockk()
    private val bandsDetailsRepository: BandsDetailsRepository = mockk()
    private val sharedSubscriptionService: SharedSubscriptionService = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()


    private val corporateService = CorporateService(
        userProfileFolder,
        "",
        corporateRepository,
        corporateUserRepository,
        //stripeAccountRepository,
        corporateUserService,
        countryService,
        //stripeService,
        tokenVerificationService,
        restTemplate,
        loginAccountRepository,
        onBoardingRepository,
        accountEntityRepository,
        bandsRepository,
        partnerRepository,
        s3Service,
        caseRepository,
        queryRepository,
        rfpRepository,
        clientViewRepository,
        validationTokenRepository,
        clientDocRepository,
        subscriptionService,
        bandsDetailsRepository,
        sharedSubscriptionService,
        userProfileUtil
    )

    private val signUpRequest = SignUpRequest(
        "<EMAIL>",
        "fName",
        "lName",
        "jobTitle",
        "corporateName",
        "US",
        null,
        false,
        "recaptcha-response",
        subscriptionPlan="1"
    )

    private val corporateUserRequest = CreateCorporateUserRequest(
        signUpRequest.email,
        signUpRequest.firstName,
        signUpRequest.lastName,
        signUpRequest.jobTitle,
        0L,
        signUpRequest.referralCode,
        signUpRequest.keepMeInformed,
        signUpRequest.countryCode
    )


    private val corporateEntity = CorporateEntity(
        0,
        "cName",
        "US",
        CorporateStatus.PENDING_VERIFICATION,
        false,
        null,
        0L,
        listOf(),
        listOf(),
        1L
    )

    private val bandsEntity = BandsEntity(
        corporateEntity.id,
        "bands1",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null
    )

    private val accountEntity = AccountEntity(
        corporateEntity.id,
        "accountEntity",
        AccountStatus.PENDING_VERIFICATION,
        null,
        null,
        corporateEntity
    )

    private val corporateUserEntity = CorporateUserEntity(
        corporateEntity,
        setOf(accountEntity),
        "jobTitle",
        false,
        listOf(),
        bandsEntity,
        mutableListOf(NotificationPreferencesEntity(
            1L, NotificationType.CASE_GCHAT_EMAIL, true,
            corporateUser = CorporateUserEntity()
        )),
        null
    )

    private val authenticatedUser: AuthenticatedUser = mockk()


    private val country = Country(
        "US",
        "United State",
        null,
        null
    )

    private val corporateUser = CorporateUser(
        0L,
        signUpRequest.email,
        signUpRequest.firstName!!,
        signUpRequest.lastName!!,
        signUpRequest.jobTitle!!,
        null,
        AccountStatus.PENDING_VERIFICATION,
        Role.ROLE_CORPORATE,
        TimeUtil.toEpochMillis(LocalDateTime.now())
    )


    private val onBoardingEntity = OnBoardingEntity(
        corporateEntity.id,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        1,
        TimeUtil.toEpochMillis(LocalDateTime.now())
    )


    private val updateCorporateInfoRequest = UpdateCorporateInfoRequest(
        signUpRequest.firstName!!,
        signUpRequest.lastName!!,
        signUpRequest.jobTitle!!,
        signUpRequest.corporateName,
        signUpRequest.countryCode,
        signUpRequest.keepMeInformed,
        "007",
        "007",
        0L,
        false,
        0L,
        listOf(),
        profilePicS3Key = "",
        educationQualification ="",
        salary = "11111",
        relevantExperience = "11",
        bandId = 3,
        managerUserIds = listOf(),
        accounts = listOf(),
        email = signUpRequest.email
    )

    private val updateCorporateStatusRequest = UpdateCorporateStatusRequest(
        "PENDING_VERIFICATION"
    )

    private val corporateUserInfoRequest = CorporateUserInfoRequest(
        bandsEntity.id!!,
        listOf(),
        listOf()
    )

    private val updateCorporateProfileRequest = UpdateCorporateProfileRequest(
        updateCorporateInfoRequest,
        updateCorporateStatusRequest,
        corporateUserInfoRequest
    )

    private val updatePartnerCorporateRequest=UpdatePartnerCorporateRequest(
        "TestUser",
        "US",
        "Green",
        "Blue","test"

    )

    private val addAccountRequest = AddAccountRequest(
        corporateEntity.id,
        corporateEntity.name,
        null,
        null,
        AccountStatus.PENDING_VERIFICATION
    )


    @Test
    fun createCorporateAndReturnDataTest() {
        every { corporateRepository.save(any()) } returns corporateEntity
        every { loginAccountRepository.countEmailDomain(signUpRequest.email, Role.ROLE_CORPORATE) } returns 0L
        every { countryService.retrieveByCountryCode(signUpRequest.countryCode) } returns country
        every { corporateUserService.createCorporateRootUser(corporateUserRequest, true) } returns corporateUser
        every { corporateUserRepository.findById(0) } returns Optional.of(corporateUserEntity)
        every { bandsRepository.findByCorporateId(-1) } returns listOf()
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { bandsRepository.findByNameAndCorporate("Super Admin (free)", corporateEntity) } returns bandsEntity
        every { accountEntityRepository.findById(0) } returns Optional.of(accountEntity)
        every { corporateUserRepository.save(any()) } returns corporateUserEntity
        every { corporateRepository.saveAndFlush(any()) } returns corporateEntity
        every { corporateRepository.findById(0) } returns Optional.of(corporateEntity)
        every { recaptchaService.verifyRecaptcha(any()) } returns true
        every { subscriptionService.createDefaultSubscription(any(), any(), any(), any(), any()) }returns
                SubscriptionPlanEntity(
                    id = 3,
                    name = "abc",
                    currency = "",
                    companyId = 4,
                    companyType ="",
                    price = 123.4F,
                    isActive = true,
                    modules = mutableListOf(),
                    startDate = LocalDateTime.now(),
                    endDate = LocalDateTime.now()
                )
        every { sharedSubscriptionService.createEmptyUsageForCurrentMonth(any(),any()) } returns Unit


        val createCorporate = corporateService.createCorporateAndReturnData(signUpRequest, true)
        assertEquals(createCorporate.first, corporateEntity.id)

    }

    @Test
    fun `createCorporateAndReturnDataTest Throws Exception When Data Not Return`() {
        every { corporateRepository.save(any()) } returns corporateEntity
        every { loginAccountRepository.countEmailDomain(signUpRequest.email, Role.ROLE_CORPORATE) } returns 1L
        every { countryService.retrieveByCountryCode(signUpRequest.countryCode) } returns country
        every { corporateUserService.createCorporateRootUser(corporateUserRequest, true) } returns corporateUser
        every { corporateUserRepository.findById(0) } returns Optional.of(corporateUserEntity)
        every { bandsRepository.findByCorporateId(-1) } returns listOf()
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { bandsRepository.findByNameAndCorporate("Super Admin (free)", corporateEntity) } returns bandsEntity
        every { accountEntityRepository.findById(0) } returns Optional.of(accountEntity)
        every { corporateUserRepository.save(any()) } returns corporateUserEntity
        every { corporateRepository.saveAndFlush(any()) } returns corporateEntity
        every { corporateRepository.findById(0) } returns Optional.of(corporateEntity)
        every { recaptchaService.verifyRecaptcha(any()) } returns true

        assertThrows<ApplicationException> {
            corporateService.createCorporateAndReturnData(signUpRequest, true)
        }
    }

    @Test
    fun retrieveCorporateTest() {
        every { corporateUserRepository.findById(1) } returns Optional.of(corporateUserEntity)
        every { onBoardingRepository.findFirstByRootUserId(0) } returns onBoardingEntity

        val retrieveCorporate = corporateService.retrieveCorporate(1)
        assertEquals(corporateUserEntity.id, retrieveCorporate.id)
        assertEquals(corporateUserEntity.email, retrieveCorporate.email)
        assertEquals(corporateUserEntity.firstName, retrieveCorporate.firstName)
        assertEquals(corporateUserEntity.lastName, retrieveCorporate.lastName)
        assertEquals(corporateUserEntity.jobTitle, retrieveCorporate.jobTitle)
        assertEquals(corporateUserEntity.keepMeInformed, retrieveCorporate.keepMeInformed)
        assertEquals(bandsEntity.id, retrieveCorporate.bandId)
        assertEquals(corporateUserEntity.countryCode, retrieveCorporate.countryCode)
        assertEquals(corporateUserEntity.status, retrieveCorporate.status)
        assertEquals(corporateUserEntity.keepMeInformed, retrieveCorporate.keepMeInformed)
    }

    @Test
    fun updateCorporateTest() {
        every { corporateUserRepository.findById(0) } returns Optional.of(corporateUserEntity)
        every { countryService.retrieveByCountryCode(signUpRequest.countryCode) } returns country
        every { bandsRepository.findById(0) } returns Optional.of(bandsEntity)
        every { corporateUserRepository.saveAndFlush(any()) } returns corporateUserEntity

        val updateCorporate =
            corporateService.updateCorporate(corporateUser.id!!, 1, "BACKOFFICE", updateCorporateProfileRequest)
        assertEquals(corporateUserEntity.id, updateCorporate.id)
        assertEquals(corporateUserEntity.email, updateCorporate.email)
        assertEquals(corporateUserEntity.firstName, updateCorporate.firstName)
        assertEquals(corporateUserEntity.lastName, updateCorporate.lastName)
        assertEquals(corporateUserEntity.jobTitle, updateCorporate.jobTitle)
        assertEquals(corporateUserEntity.keepMeInformed, updateCorporate.keepMeInformed)
        assertEquals(bandsEntity.id, updateCorporate.bandId)
        assertEquals(corporateUserEntity.countryCode, updateCorporate.countryCode)
        assertEquals(corporateUserEntity.status, updateCorporate.status)
        assertEquals(corporateUserEntity.keepMeInformed, updateCorporate.keepMeInformed)
    }

    @Test
    fun updatePartnerCorporateTest() {
        every { corporateRepository.findByIdAndPartnerId(0,1) } returns corporateEntity
        every { corporateRepository.save(corporateEntity) } returns corporateEntity
        every { s3Service.updateProfilePicture(any(), any(), any()) } returns UUID.randomUUID().toString()
        val updateCorporate =
            corporateService.updatePartnerCorporate(0, 1, "BACKOFFICE",1, updatePartnerCorporateRequest)
            assertEquals(updateCorporate,0)
    }
    @Test
    fun updatePartnerCorporateThrowsApplicationException() {
        every { corporateRepository.findByIdAndPartnerId(0,1) } returns corporateEntity
        assertThrows<ApplicationException>
        {
        val updateCorporate =
            corporateService.updatePartnerCorporate(0, 1, "BACKOFFICE",1, updatePartnerCorporateRequest)
       }
    }


    @Test
    fun resendTest() {
        corporateUserEntity.status = AccountStatus.PENDING_VERIFICATION
        every { corporateUserRepository.findById(corporateUser.id!!) } returns Optional.of(corporateUserEntity)
        every { tokenVerificationService.resend(0L, ValidationType.CORPORATE_SIGNUP) } returns "Success"
        val resend = corporateService.resend(corporateUserEntity.id!!)
        val returnMessage = "Success"
        assertEquals(returnMessage, resend)
    }

    @Test
    fun retrieveCorporateUsersTest() {
        every { corporateUserRepository.findAll() } returns listOf()
        every { corporateUserRepository.findUsersByCorporateId(any()) } returns listOf()

        val result = corporateService.retrieveCorporateUsers(
            corporateId = 1L
        )
        assertNotNull(result)
    }

    @Test
    fun checkUserExistsTest() {
        every { loginAccountRepository.findByEmail(signUpRequest.email) } returns corporateUserEntity
        val result = corporateService.checkUserExists(signUpRequest.email)
        assertNotNull(result)
    }

    @Test
    fun addAccountTest() {
        every { corporateRepository.findById(corporateEntity.id!!) } returns Optional.of(corporateEntity)
        every { accountEntityRepository.save(any()) } returns accountEntity

        val result = corporateService.addAccount(addAccountRequest, corporateEntity.id!!, authenticatedUser)
        assertEquals(corporateEntity.id, result)
    }

    @Test
    fun retrieveAccountsTest() {
        every { corporateRepository.findById(1).get() } returns corporateEntity
        every { authenticatedUser.companyId } returns 1
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { accountEntityRepository.findAllByCorporate(corporateEntity) } returns listOf(accountEntity)

        val page = PageImpl<AccountEntity>(listOf(accountEntity), PageRequest.of(0, 10), 10)

        every { accountEntityRepository.searchByCriteriaForAdmin(any(), any(), any()) } returns page

        val resultList = corporateService.retrieveAccounts(
            AccountSearchFilter("",AccountStatus.ACTIVE),
            PageRequest.of(0, 10),
            authenticatedUser,1
        )
        assertNotNull(resultList)
    }

    @Test
    fun updateAccountTest() {
        every { accountEntityRepository.findById(0) } returns Optional.of(accountEntity)
        every { accountEntityRepository.saveAndFlush(any()) } returns accountEntity

        val result = corporateService.updateAccount(accountEntity.id!!, authenticatedUser, addAccountRequest)
        assertEquals(accountEntity.id, result.id)
        assertEquals(accountEntity.companyName, result.companyName)
        assertEquals(accountEntity.description, result.description)
        assertEquals(accountEntity.status, result.status)
        assertEquals(accountEntity.name, result.name)
        assertEquals(accountEntity.corporate, result.corporate)
    }

    @Test
    fun retrieveCorporateAccountUsersTest() {
        every { corporateRepository.findById(1).get() } returns corporateEntity
        every { authenticatedUser.companyId } returns 1
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { accountEntityRepository.findAllByCorporate(corporateEntity) } returns listOf(accountEntity)

        val resultList = corporateService.retrieveForCorporateAccountUsers(authenticatedUser,1)
        assertNotNull(resultList)

    }

    @Test
    fun canUpdateUserTest()
    {
        every { corporateUserRepository.findById(0) } returns Optional.of(corporateUserEntity)
        every { corporateRepository.findById(1).get() } returns corporateEntity
        every { authenticatedUser.companyId } returns 0
        every { authenticatedUser.visibilities} returns listOf(UserAccess("USER", mutableListOf("FULL")))
        every { authenticatedUser.accesses } returns listOf(UserAccess("USER", mutableListOf("FULL")))
       every { accountEntityRepository.findAllByCorporate(corporateEntity)} returns  listOf(accountEntity)

        val result = corporateService.canUpdateUser(0,authenticatedUser)
        assertEquals(true,result)
    }

    @Test
    fun getAccountDetailsTest() {
        every { accountEntityRepository.findById(0) } returns Optional.of(accountEntity)
        val result = corporateService.getAccountDetails(accountEntity.id!!)
        assertNotNull(result)
    }

    @Test
    fun retrieveSecondaryCorporateUsersTest() {
        every { corporateUserRepository.findByCorporateId(corporateEntity.id!!) } returns listOf(corporateUserEntity)
        val result = corporateService.retrieveSecondaryCorporateUsers(corporateEntity.id!!)
        assertNotNull(result)
    }

    @Test
    fun retrieveCorporatesTest() {
        every { corporateRepository.findAll() } returns listOf(corporateEntity)

        every { corporateRepository.findAllByPartnerId(any()) } returns listOf()
        val result = corporateService.retrieveCorporates(
            isPartnerCompany = true,
            partnerId = 1L
        )
        assertNotNull(result)
    }

    @Test
    fun retrieveCorporateUserTest() {
        every { corporateUserRepository.findByCorporateId(corporateEntity.id!!) } returns listOf(corporateUserEntity)

        every { corporateRepository.findAllByPartnerId(any()) } returns listOf()
        every { corporateUserRepository.findUsersByCorporateId(any()) } returns listOf()

        val result = corporateService.retrieveCorporateUsers(corporateEntity.id!!)
        assertNotNull(result)

    }

    @Test
    fun retrieveCorporateUserAccountsTest() {
        every { corporateUserRepository.findById(corporateEntity.id!!) } returns Optional.of(corporateUserEntity)
        every { accountEntityRepository.findByCorporateUsersId(any()) } returns listOf()
        val result = corporateService.retrieveCorporateUserAccounts(corporateEntity.id!!)
        assertNotNull(result)
    }

    @Test
    fun retrieveCorporateAccountsTest() {
        every { corporateRepository.findById(corporateEntity.id!!) } returns Optional.of(corporateEntity)
        every { accountEntityRepository.findByCorporateId(any()) } returns listOf()
        val result = corporateService.retrieveCorporateAccounts(corporateEntity.id!!)
        assertNotNull(result)
    }


    private fun createCorporateRequest(
        email: String, fname: String, lname: String, jobTitle: String,
        corporateName: String, countryCode: String, referralCode: String, keepMeInformed: Boolean
    ): SignUpRequest {
        return SignUpRequest(
            email = email,
            firstName = fname,
            lastName = lname,
            jobTitle = jobTitle,
            corporateName = corporateName,
            countryCode = countryCode,
            referralCode = referralCode,
            keepMeInformed = keepMeInformed,
            recaptchaResponse   = "recaptcha-response"
        )
    }

    private val corporateRequest = createCorporateRequest(
        "<EMAIL>", "fname", "lname",
        "JobTitle", "CorporateName", "IN", "REF", false
    )

    private val createCorporateUserRequest = CreateCorporateUserRequest(
        signUpRequest.email,
        signUpRequest.firstName,
        signUpRequest.lastName,
        signUpRequest.jobTitle,
        corporateUser.id,
        signUpRequest.referralCode,
        signUpRequest.keepMeInformed,
        signUpRequest.countryCode
    )


    @BeforeEach
    fun setup() {

        every { corporateUserService.getUserManagers(any()) } returns listOf()
        every { corporateUserRepository.findAllByCorporateIdAndBandNameIn(any(), listOf("Super Admin (free)", "Super Admin")) } returns listOf()
//        every { caseDashboardService.retrieveProfile(any() ) } returns UserProfile(LOGGED_IN_USER_ID, LOGGED_IN_USER_EMAIL, "test", "user", AccountStatus.ACTIVE, Role.ROLE_ADMIN, null, null)
        //every {corporateUserService.createCorporateUserRequest()}
        //every{ corporateUserService.createCorporateRootUser(corporateRequest,true)}
    }
//    val corporateUser = corporateUserService.createCorporateRootUser(corporateRequest,true)
    @Test
    fun createCorporateTest(){

        val country = Country(corporateRequest.countryCode,"IN",null,null)
        val corporateRootUser = CorporateUserEntity()
        every { countryService.retrieveByCountryCode(corporateRequest.countryCode)} returns country
        every { corporateRepository.save(any()) } returns corporateEntity
        every { recaptchaService.verifyRecaptcha(any())} returns  true
        every { corporateUserService.createCorporateRootUser(createCorporateUserRequest,true) } returns corporateUser
        every{ loginAccountRepository.countEmailDomain(signUpRequest.email,Role.ROLE_CORPORATE) } returns 0L
        every { countryService.retrieveByCountryCode(signUpRequest.countryCode)} returns country
        every { corporateUserRepository.findById(0)} returns Optional.of(corporateUserEntity)
        every { bandsRepository.findByCorporateId(-1)} returns listOf(bandsEntity)
        every { bandsRepository.save(any()) } returns bandsEntity
        every { accountEntityRepository.save(any()) } returns accountEntity
        every { bandsRepository.findByNameAndCorporate("Super Admin (free)",corporateEntity) } returns bandsEntity
        every { accountEntityRepository.findById(0) } returns Optional.of(accountEntity)
        every { corporateUserRepository.save(any())} returns corporateUserEntity
        every { corporateRepository.saveAndFlush(corporateEntity) } returns corporateEntity

        every { subscriptionService.createDefaultSubscription(any(), any(), any(), any(), any()) } returns
        SubscriptionPlanEntity(
            id = 1,
            name = "",
            currency = "",
            companyId = 3,
            companyType = "",
            price = 123.4F,
            isActive = true,
            modules = mutableListOf(),
            startDate = LocalDateTime.now(),
            endDate = LocalDateTime.now()
        )

        every { sharedSubscriptionService.createEmptyUsageForCurrentMonth(any(), any()) } returns Unit

        var result = corporateService.createCorporate(signUpRequest,true)
        assertEquals(corporateEntity.id, result)
    }
}
