app:
  cors:
    domains: "http://localhost:4200, https://qat-goglobal.centuroglobal.com, https://centuroglobal.com, https://designprefect.co.in"

  authentication:
    mfa:
      static-token: 456789
  api:
    python:
      base-url: https://qat-python-api.centuroglobal.com

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

spring:
  cloud:
    openfeign:
      client:
        config:
          PythonApiClient:
            connectTimeout: 300000
            readTimeout: 300000

  mail:
    cg-support:
      case:
        to: <EMAIL>, <EMAIL>
      query:
        to: <EMAIL>, <EMAIL>
      rfp:
        to: <EMAIL>, <EMAIL>
  aws:
    s3:
      default-bucket: cg-goglobal-qat
      proxied-url:
      master-data-bucket:  cg-goglobal-admin-qat
      member-contract-bucket: cg-goglobal-contracts-qat
      case-doc-bucket: case-doc-repo-qat


#logging:
#  level:
#    org:
#      hibernate:
#        SQL: DEBUG
#        orm:
#          jdbc:
#            bind: TRACE