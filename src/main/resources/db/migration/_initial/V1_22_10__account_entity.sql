CREATE TABLE `corporate_accounts` (
   `id` bigint(11) NOT NULL AUTO_INCREMENT,
   `status` varchar(100) NOT NULL,
   `name` varchar(300) DEFAULT NULL,
   `created_date` datetime(3) NOT NULL,
   `last_updated_date` datetime(3) NOT NULL,
   `corporate_id` bigint(11) NOT NULL,
   `description` varchar(4000) DEFAULT NULL,
   `company_name` varchar(300) DEFAULT NULL,
   PRIMARY KEY (`id`),
   FOREIGN KEY (`corporate_id`) REFERENCES `corporate`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;