ALTER TABLE `corporate_team` DROP FOREIGN KEY `corporate_team_ibfk_1`;
;
ALTER TABLE `corporate_team`
ADD CONSTRAINT `corporate_team_ibfk_1`
  FOREIGN KEY (`corporate_id`)
  REFERENCES `corporate` (`id`)
  ON DELETE CASCADE
  ON UPDATE NO ACTION,
ADD CONSTRAINT `corporate_team_ibfk_2`
  FOREIGN KEY (`user_id`)
  REFERENCES `login_account` (`id`)
  ON DELETE CASCADE
  ON UPDATE NO ACTION;

ALTER TABLE `login_history` ADD INDEX `user_id_idx` (`user_id` ASC) VISIBLE;