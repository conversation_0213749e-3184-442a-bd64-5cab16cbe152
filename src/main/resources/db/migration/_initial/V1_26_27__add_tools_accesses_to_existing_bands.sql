INSERT INTO band_details(band_id, access_id)
SELECT b.id, mam.id FROM bands b, micro_access_master mam
WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin', 'Partner Admin') AND
    mam.feature_key='TAX_CALC' AND
    b.id NOT IN (SELECT band_id FROM band_details WHERE access_id=mam.id);

INSERT INTO band_details(band_id, access_id)
SELECT b.id, mam.id FROM bands b, micro_access_master mam
WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin', 'Partner Admin') AND
    mam.feature_key='PLAYBOOK' AND
    b.id NOT IN (SELECT band_id FROM band_details WHERE access_id=mam.id);

INSERT INTO band_details(band_id, access_id)
SELECT b.id, mam.id FROM bands b, micro_access_master mam
WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin', 'Partner Admin') AND
    mam.feature_key='VISA_ASSESSMENT' AND
    b.id NOT IN (SELECT band_id FROM band_details WHERE access_id=mam.id);

INSERT INTO band_details(band_id, access_id)
SELECT b.id, mam.id FROM bands b, micro_access_master mam
WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin', 'Partner Admin') AND
    mam.feature_key='CG_AI' AND
    b.id NOT IN (SELECT band_id FROM band_details WHERE access_id=mam.id);

INSERT INTO band_details(band_id, access_id)
SELECT b.id, mam.id FROM bands b, micro_access_master mam
WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin', 'Partner Admin') AND
    mam.feature_key='COST_OF_LIVING' AND
    b.id NOT IN (SELECT band_id FROM band_details WHERE access_id=mam.id);
