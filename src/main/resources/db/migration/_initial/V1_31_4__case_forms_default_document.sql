ALTER TABLE `case_form` ADD COLUMN field_count BIGINT(11);
ALTER TABLE `case_form` ADD COLUMN default_documents VARCHAR(500);

/*---- accesses for case forms */

INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE_FORM', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE_FORM', 'OWN');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE_FORM', 'REPORTEES');

/*-- add case form visibilities */

/*--- Super admin will have full access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='CASE_FORM' AND vm.visibility='FULL')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin');

/*---- Account manager will have reportees access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='CASE_FORM' AND vm.visibility='REPORTEES')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager');

/*---- Applicant will have own access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='CASE_FORM' AND vm.visibility='OWN')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');