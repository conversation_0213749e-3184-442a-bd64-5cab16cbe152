CREATE TABLE case_documents_audit(`id` bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `case_id` bigint(20),
    `document_id` bigint(20),
    `created_date` DATETIME(3),
    `created_by` bigint(20),
    `action` varchar(20) NOT NULL
);

CREATE TABLE case_document_file (
        `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `document_id` bigint,
        `file_name` varchar(255) DEFAULT NULL,
        `file_type` varchar(255) DEFAULT NULL,
        `file_key` varchar(255) DEFAULT NULL,
        `file_size` bigint(20) DEFAULT NULL,
        `is_locked` boolean DEFAULT FALSE,
        `created_date` DATETIME(3),
        FOREIGN KEY (`document_id`) REFERENCES `case_documents` (`id`)
);