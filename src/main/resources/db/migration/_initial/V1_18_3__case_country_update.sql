ALTER TABLE bank_account CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE office_space CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE assignee_cases CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE payroll_cases CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE hr_and_employment_support CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE intellectual_property CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE single_immigration_visa CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE multiple_immigration_visa CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE entity_setup CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE tax_support CHANGE country case_country varchar(3) DEFAULT NULL;
ALTER TABLE entity_setup CHANGE company_name setup_company_name varchar(255) DEFAULT NULL;