CREATE TABLE travel_history (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    origin_country VARCHAR(50) NOT NULL,
    destination_country VARCHAR(50) NOT NULL,
    arrival DATETIME(3) NOT NULL,
    departure DATETIME(3) NOT NULL,
    period_of_stay BIGINT NOT NULL,
    stay_unit VARCHAR(255) NOT NULL,
    purpose VARCHAR(255) NOT NULL,
    user_id BIGINT(20) NOT NULL,
    created_date  DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    created_by BIGINT(11) NOT NULL,
    updated_by BIGINT(11),
    FOREIGN KEY(user_id) REFERENCES login_account(id)
);
