CREATE TABLE IF NOT EXISTS `case_form` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `country` VARCHAR(3),
    `category` VARCHAR(255),
    `fields` TEXT,
    `visibility` VARCHAR(255),
    `status` VARCHAR(50),
    `partner_id` BIGINT(11),
    `created_by` BIGINT(11),
    `created_date` DATETIME,
    `updated_by` BIGINT(11),
    `last_updated_date` DATETIME
);

CREATE TABLE IF NOT EXISTS `case_form_country` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(3) NOT NULL,
    `case_form_id` BIGINT(11),
    `created_by` BIGINT(11),
    `created_date` DATETIME,
    `updated_by` BIGINT(11),
    `last_updated_date` DATETIME,
     FOREI<PERSON><PERSON> KEY (`case_form_id`) REFERENCES `case_form`(`id`)
);


INSERT INTO `authorities`(`key`,`display_name`) VALUES ('CASE_FORM', 'Case Form');
INSERT INTO `feature_master`(`feature_key`, `feature_value`, `is_default`) VALUES('CASE_FORM', 'Case forms', 0);
