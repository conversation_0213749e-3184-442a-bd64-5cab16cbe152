ALTER TABLE cases ADD COLUMN visa_issue_date BIGINT DEFAULT NULL;
ALTER TABLE cases ADD COLUMN visa_expiry_date BIGINT DEFAULT NULL;
ALTER TABLE cases ADD COLUMN visa_renewal_date BIGINT DEFAULT NULL;
ALTER TABLE cases ADD COLUMN visa_type VARCHAR(255) DEFAULT NULL;


UPDATE cases c INNER JOIN business_visa v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;

UPDATE cases c INNER JOIN dependent_visa v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;

UPDATE cases c INNER JOIN entry_visa v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;

UPDATE cases c INNER JOIN multiple_immigration_visa v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;

UPDATE cases c INNER JOIN right_to_work_check v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;

UPDATE cases c INNER JOIN single_immigration_visa v ON c.id=v.id
SET
c.visa_issue_date = v.visa_issue_date,
c.visa_expiry_date = v.visa_expiry_date,
c.visa_renewal_date = v.visa_renewal_date,
c.visa_type = v.visa_type;