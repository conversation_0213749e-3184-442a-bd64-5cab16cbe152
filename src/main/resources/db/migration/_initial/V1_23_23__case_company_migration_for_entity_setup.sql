
-- Set expert company name as case company name if case created by expert
UPDATE cases ca
INNER JOIN expert_user eu ON ca.created_by=eu.id
INNER JOIN expert_company_profile ec ON eu.company_profile_id=ec.id
SET ca.company_name = ec.name
WHERE ca.case_type='ENTITY_SETUP' AND ca.company_name IS NULL;

-- Set corporate name as case name if case company created by corporate
UPDATE cases ca
INNER JOIN corporate_user cu ON ca.created_by=cu.id
INNER JOIN corporate c ON cu.corporate_id=c.id
SET ca.company_name = c.name
WHERE ca.case_type='ENTITY_SETUP' AND ca.company_name IS NULL;
