CREATE TABLE travel_assessment (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    origin_country VARCHAR(50) NOT NULL,
    destination_country VARCHAR(50) NOT NULL,
    arrival DATETIME(3) NOT NULL,
    departure DATETIME(3),
    period_of_stay BIGINT NOT NULL,
    stay_unit VARCHAR(255) NOT NULL,
    purpose VARCHAR(1000) NOT NULL,
    user_id BIGINT(20) NOT NULL,
    data TEXT,
    status VARCHAR(255),
    partner_id BIGINT(20),
    created_date  DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    created_by BIGINT(11) NOT NULL,
    updated_by BIGIN<PERSON>(11),
    FOREIGN KEY(user_id) REFERENCES corporate_user(id)
);

CREATE TABLE travel_assessment_tracking (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    step VARCHAR(100) NOT NULL,
    request TEXT NOT NULL,
    response TEXT,
    assessment_id BIGINT(11),
    created_date DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    created_by <PERSON>IGINT(11) NOT NULL,
    updated_by BIGINT(11),
    FOREIGN KEY(assessment_id) REFERENCES travel_assessment(id)
);
