/*---- accesses for tasks */
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('TASK', 'Task');

INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('TASK', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('TASK', 'OWN');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('TASK', 'REPORTEES');

/*-- add task visibilities */

/*--- Super admin will have full access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='TASK' AND vm.visibility='FULL')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin');

/*---- Account manager will have reportees access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='TASK' AND vm.visibility='REPORTEES')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager');

/*---- Applicant will have own access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='TASK' AND vm.visibility='OWN')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');



