CREATE TABLE `case_milestones` (
   `id` bigint(11) NOT NULL AUTO_INCREMENT,
   `created_date` datetime(3) NOT NULL,
   `last_updated_date` datetime(3) NOT NULL,
   `last_updated_by` bigint(11) NOT NULL,
   `case_id` bigint(11) NOT NULL,
   `milestone_id` bigint(11) NOT NULL,
   PRIMARY KEY (`id`),
   FOREIGN KEY (`case_id`) REFERENCES `cases`(`id`),
   FOREIGN KEY (`milestone_id`) REFERENCES `milestones`(`sequence`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;