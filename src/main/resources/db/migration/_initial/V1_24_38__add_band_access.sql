INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES("EVENT","VIEW","View Event");

INSERT INTO band_details(band_id, access_id)
SELECT b.id, (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level="VIEW" and mam.feature_key = "EVENT")
FROM bands b;

INSERT INTO corporate
(id, name, country_code, status, subscription_active, root_user_id, created_date, last_updated_date, last_updated_by)
VALUES(-2, 'Dummy partner corporate', 'US', 'SUSPENDED', FALSE, -2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, -2);


INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Super Admin', '(Single User) Company Admin', 'PURPLE', 'ACTIVE', -2, current_timestamp);

INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Super Admin (free)', '(Single User) Company Admin', 'ORANGE', 'ACTIVE', -2, current_timestamp);

INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Account Manager', '(Single User) Company Admin', 'PINK', 'ACTIVE', -2, current_timestamp);

INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Applicant', '(Single User) Company Admin', 'BLUE', 'ACTIVE', -2, current_timestamp);

INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Account Manager (free)', 'Account Manager (free)', 'PURPLE', 'ACTIVE', -2, current_timestamp);

INSERT INTO band_details(access_id, visibility_id, band_id) SELECT access_id, visibility_id,
(SELECT id FROM bands WHERE name = 'Super Admin' AND corporate_id = -2) as band_id
FROM band_details where band_id in (SELECT id FROM bands WHERE name = 'Super Admin' AND corporate_id = -1);

INSERT INTO band_details(access_id, visibility_id, band_id) SELECT access_id, visibility_id,
(SELECT id FROM bands WHERE name = 'Super Admin (free)' AND corporate_id = -2) as band_id
FROM band_details where band_id in (SELECT id FROM bands WHERE name = 'Super Admin (free)' AND corporate_id = -1);

INSERT INTO band_details(access_id, visibility_id, band_id) SELECT access_id, visibility_id,
(SELECT id FROM bands WHERE name = 'Account Manager' AND corporate_id = -2) as band_id
FROM band_details where band_id in (SELECT id FROM bands WHERE name = 'Account Manager' AND corporate_id = -1);

INSERT INTO band_details(access_id, visibility_id, band_id) SELECT access_id, visibility_id,
(SELECT id FROM bands WHERE name = 'Applicant' AND corporate_id = -2) as band_id
FROM band_details where band_id in (SELECT id FROM bands WHERE name = 'Applicant' AND corporate_id = -1);

INSERT INTO band_details(access_id, visibility_id, band_id) SELECT access_id, visibility_id,
(SELECT id FROM bands WHERE name = 'Account Manager (free)' AND corporate_id = -2) as band_id
FROM band_details where band_id in (SELECT id FROM bands WHERE name = 'Account Manager (free)' AND corporate_id = -1);

DELETE FROM band_details WHERE id IN (SELECT id FROM (SELECT id FROM band_details WHERE band_id IN
(SELECT id FROM bands WHERE corporate_id=-2) AND access_id in
    (SELECT id FROM micro_access_master WHERE feature_key = "EVENT")) AS id);

