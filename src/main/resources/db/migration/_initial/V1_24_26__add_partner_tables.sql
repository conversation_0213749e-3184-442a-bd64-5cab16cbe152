CREATE TABLE IF NOT EXISTS `partner` (
   `id` bigint(11) NOT NULL AUTO_INCREMENT,
   `name` varchar(50) NOT NULL,
   `contract_from_date` datetime(3) NOT NULL,
   `contract_to_date` datetime(3) NOT NULL,
   `cases_managed` varchar(20) NOT NUll,
   `queries_managed` varchar(20) NOT NUll,
   `company_logo` varchar(255) DEFAULT NULL,
   `theme_primary_color` varchar(50),
   `theme_secondary_color` varchar(50),
   `root_user_id` bigint(11) DEFAULT NULL,
   `status` varchar(20) NOT NULL,
   `country` varchar(20) NOT NULL,
   `reference_id` bigint DEFAULT NULL,
   `created_from` varchar(20) NOT NULL,
   `created_date` DATETIME(3),
   `last_updated_date` DATETIME(3),
   `created_by` bigint(11) NOT NULL,
   `updated_by` bigint(11) NOT NULL,
   `band_id` bigint,
    FOREIGN KEY(`band_id`) REFERENCES `bands`(`id`),
    PRIMARY KEY (`id`),
    UNIQUE KEY `name_idx` (`name`)
) ;

CREATE TABLE IF NOT EXISTS `partner_user` (
   `id` bigint(11) NOT NULL,
   `partner_id` bigint(11),
   `job_title` varchar(255) NOT NULL,
   `band_id` bigint(11),
    PRIMARY KEY (`id`),
    FOREIGN KEY (`id`) REFERENCES `login_account`(`id`),
    FOREIGN KEY (`partner_id`) REFERENCES `partner` (`id`),
    FOREIGN KEY(`band_id`) REFERENCES `bands`(`id`)
) ;

ALTER TABLE corporate
ADD COLUMN partner_id bigint(11),
ADD FOREIGN KEY (partner_id) REFERENCES partner(id);

ALTER TABLE expert_company_profile
ADD COLUMN partner_id bigint(11),
ADD FOREIGN KEY (partner_id) REFERENCES partner(id);

