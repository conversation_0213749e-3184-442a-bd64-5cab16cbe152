INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES('CASE', 'VIEW_DOCUMENT', 'View case documents');
INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES('CASE', 'DOWNLOAD_DOCUMENT', 'Download case documents');
INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES('CASE', 'DELETE_DOCUMENT', 'Delete case documents');

-- Populate existing corporate bands to add case document accesses
INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='VIEW_DOCUMENT')
    FROM bands b
    WHERE b.name='Account Manager' OR b.name = 'Applicant' OR b.name = 'Super Admin (free)' OR b.name = 'Super Admin';

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='DELETE_DOCUMENT')
    FROM bands b
    WHERE b.name='Account Manager' OR b.name = 'Applicant' OR b.name = 'Super Admin (free)' OR b.name = 'Super Admin';