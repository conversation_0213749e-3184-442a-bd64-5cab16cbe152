CREATE TABLE notification_preferences(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    notification_key VARCHAR(50) NOT NULL,
    value BOOLEAN DEFAULT TRUE,
    user_id bigint(11) NOT NULL,
    created_date DATETIME(3),
    last_updated_date DATETIM<PERSON>(3),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`user_id`) REFERENCES `corporate_user` (`id`)
);

-- migration for existing corporate users
INSERT INTO `notification_preferences`(notification_key, value, created_date, last_updated_date, user_id)
SELECT "CASE_UPDATE_EMAIL" AS notification_key, true AS value, current_timestamp AS created_date, current_timestamp AS last_updated_date,
id AS user_id FROM corporate_user;

INSERT INTO `notification_preferences`(notification_key, value, created_date, last_updated_date, user_id)
SELECT "CASE_GCHAT_EMAIL" AS notification_key, true AS value, current_timestamp AS created_date, current_timestamp AS last_updated_date,
id AS user_id FROM corporate_user;

INSERT INTO `notification_preferences`(notification_key, value, created_date, last_updated_date, user_id)
SELECT "DOC_UPLOAD_EMAIL" AS notification_key, true AS value, current_timestamp AS created_date, current_timestamp AS last_updated_date,
id AS user_id FROM corporate_user;

INSERT INTO `notification_preferences`(notification_key, value, created_date, last_updated_date, user_id)
SELECT "QUERY_GCHAT_EMAIL" AS notification_key, true AS value, current_timestamp AS created_date, current_timestamp AS last_updated_date,
id AS user_id FROM corporate_user;