UPDATE `micro_access_master` SET access_level='MANAGER', `access_level_value`='Manager Dashboard' WHERE feature_key='DASHBOARD' AND access_level='ADMIN';
UPDATE `micro_access_master` SET access_level='ADMIN', `access_level_value`='Admin Dashboard' WHERE feature_key='DASHBOARD' AND access_level='VIEW_DOCUMENTS';

-- Remove manager access from super admin bands
DELETE bd FROM `band_details` bd
INNER JOIN `bands` b ON b.id=bd.band_id
INNER JOIN `micro_access_master` mam ON mam.id=bd.access_id
WHERE mam.feature_key='DASHBOARD' AND mam.access_level='MANAGER' AND b.name IN ('Super Admin (free)', 'Super Admin');

INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('COMPANY_INFO', 'VIEW_DOCUMENTS', 'View Documents');

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='COMPANY_INFO' AND mam.access_level='VIEW_DOCUMENTS')
    FROM bands b WHERE b.name IN ('Super Admin (free)', 'Super Admin');