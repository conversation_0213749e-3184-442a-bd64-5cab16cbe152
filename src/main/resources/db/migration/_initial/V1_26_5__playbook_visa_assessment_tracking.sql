CREATE TABLE IF NOT EXISTS `usage_playbook` (
  `id` bigint(20) auto_increment,
  `country` varchar(10) NOT NULL,
  `industry` varchar(100) NOT NULL,
  `accessed_by` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_visa_assessment` (
  `id` bigint(20) auto_increment,
  `issue_country` varchar(10) NOT NULL,
  `destination_country` varchar(10) NOT NULL,
  `accessed_by` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIG<PERSON> KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

INSERT INTO subscription_details(subscription_id, name, code, threshold, unit, overage_rate, tracking_duration, created_date, last_updated_date, created_by, updated_by, is_unlimited)
SELECT s.id,
'Visa Eligibility Assessment', 'VISA_ELIGIBILITY_ASSESSMENT', '2147483647', 'ASSESSMENT', '0', 'MONTH', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1', '1', '1'
FROM subscription_plan s where s.id not in (select subscription_id from subscription_details where code='VISA_ELIGIBILITY_ASSESSMENT');

INSERT INTO subscription_details(subscription_id, name, code, threshold, unit, overage_rate, tracking_duration, created_date, last_updated_date, created_by, updated_by, is_unlimited)
SELECT s.id,
'Market Entry Playbook', 'PLAYBOOK', '2147483647', 'COUNTRIES', '0', 'YEAR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1', '1', '1'
FROM subscription_plan s where s.id not in (select subscription_id from subscription_details where code='PLAYBOOK');

UPDATE subscription_details SET tracking_duration='YEAR' WHERE code='BLUEPRINT';
UPDATE subscription_usage_details SET tracking_duration='YEAR' WHERE code='BLUEPRINT';