ALTER TABLE partner ADD COLUMN corporate_access VARCHAR(500) NOT NULL;

UPDATE partner AS p INNER JOIN
    (SELECT la.partner_band_id AS partner_band_id, concat(GROUP_CONCAT(DISTINCT mam.feature_key), ',', GROUP_CONCAT(DISTINCT vm.feature_key)) AS corporate_access FROM login_account la INNER JOIN band_details bd ON la.partner_band_id=bd.band_id left join
    micro_access_master mam ON mam.id=bd.access_id LEFT JOIN visibility_master vm ON vm.id=bd.visibility_id GROUP BY la.partner_band_id) t1
ON t1.partner_band_id=p.band_id
SET p.corporate_access=t1.corporate_access;