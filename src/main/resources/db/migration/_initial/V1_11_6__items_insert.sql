INSERT INTO items(item_id,name,category) VALUES (1,'Meal, Inexpensive Restaurant','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (2,'Meal for 2 People, Mid-range Restaurant, Three-course','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (3,'McMeal at McDonalds (or Equivalent Combo Meal)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (4,'Domestic Beer (0.5 liter draught)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (5,'Imported Beer (0.33 liter bottle)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (6,'Coke/Pepsi (0.33 liter bottle)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (7,'Water (0.33 liter bottle)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (8,'Milk (regular), (1 liter)','Markets');
INSERT INTO items(item_id,name,category) VALUES (9,'Loaf of Fresh White Bread (500g)','Markets');
INSERT INTO items(item_id,name,category) VALUES (11,'Eggs (regular) (12)','Markets');
INSERT INTO items(item_id,name,category) VALUES (12,'Local Cheese (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (13,'Water (1.5 liter bottle)','Markets');
INSERT INTO items(item_id,name,category) VALUES (14,'Bottle of Wine (Mid-Range)','Markets');
INSERT INTO items(item_id,name,category) VALUES (15,'Domestic Beer (0.5 liter bottle)','Markets');
INSERT INTO items(item_id,name,category) VALUES (16,'Imported Beer (0.33 liter bottle)','Markets');
INSERT INTO items(item_id,name,category) VALUES (17,'Cigarettes 20 Pack (Marlboro)','Markets');
INSERT INTO items(item_id,name,category) VALUES (18,'One-way Ticket (Local Transport)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (20,'Monthly Pass (Regular Price)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (110,'Apples (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (26,'Apartment (1 bedroom) in City Centre','Rent Per Month');
INSERT INTO items(item_id,name,category) VALUES (27,'Apartment (1 bedroom) Outside of Centre','Rent Per Month');
INSERT INTO items(item_id,name,category) VALUES (28,'Apartment (3 bedrooms) in City Centre','Rent Per Month');
INSERT INTO items(item_id,name,category) VALUES (29,'Apartment (3 bedrooms) Outside of Centre','Rent Per Month');
INSERT INTO items(item_id,name,category) VALUES (30,'Basic (Electricity, Heating, Cooling, Water, Garbage) for 85m2 Apartment','Utilities (Monthly)');
INSERT INTO items(item_id,name,category) VALUES (32,'1 min. of Prepaid Mobile Tariff Local (No Discounts or Plans)','Utilities (Monthly)');
INSERT INTO items(item_id,name,category) VALUES (33,'Internet (60 Mbps or More, Unlimited Data, Cable/ADSL)','Utilities (Monthly)');
INSERT INTO items(item_id,name,category) VALUES (100,'Price per Square Meter to Buy Apartment in City Centre','Buy Apartment Price');
INSERT INTO items(item_id,name,category) VALUES (101,'Price per Square Meter to Buy Apartment Outside of Centre','Buy Apartment Price');
INSERT INTO items(item_id,name,category) VALUES (105,'Average Monthly Net Salary (After Tax)','Salaries And Financing');
INSERT INTO items(item_id,name,category) VALUES (106,'Mortgage Interest Rate in Percentages (%), Yearly, for 20 Years Fixed-Rate','Salaries And Financing');
INSERT INTO items(item_id,name,category) VALUES (24,'Gasoline (1 liter)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (40,'Fitness Club, Monthly Fee for 1 Adult','Sports And Leisure');
INSERT INTO items(item_id,name,category) VALUES (42,'Tennis Court Rent (1 Hour on Weekend)','Sports And Leisure');
INSERT INTO items(item_id,name,category) VALUES (44,'Cinema, International Release, 1 Seat','Sports And Leisure');
INSERT INTO items(item_id,name,category) VALUES (19,'Chicken Fillets (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (25,'Volkswagen Golf 1.4 90 KW Trendline (Or Equivalent New Car)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (60,'1 Pair of Jeans (Levis 501 Or Similar)','Clothing And Shoes');
INSERT INTO items(item_id,name,category) VALUES (62,'1 Summer Dress in a Chain Store (Zara, H&M, ...)','Clothing And Shoes');
INSERT INTO items(item_id,name,category) VALUES (64,'1 Pair of Nike Running Shoes (Mid-Range)','Clothing And Shoes');
INSERT INTO items(item_id,name,category) VALUES (66,'1 Pair of Men Leather Business Shoes','Clothing And Shoes');
INSERT INTO items(item_id,name,category) VALUES (107,'Taxi Start (Normal Tariff)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (108,'Taxi 1km (Normal Tariff)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (109,'Taxi 1hour Waiting (Normal Tariff)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (111,'Oranges (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (112,'Potato (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (113,'Lettuce (1 head)','Markets');
INSERT INTO items(item_id,name,category) VALUES (114,'Cappuccino (regular)','Restaurants');
INSERT INTO items(item_id,name,category) VALUES (115,'Rice (white), (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (116,'Tomato (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (118,'Banana (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (119,'Onion (1kg)','Markets');
INSERT INTO items(item_id,name,category) VALUES (121,'Beef Round (1kg) (or Equivalent Back Leg Red Meat)','Markets');
INSERT INTO items(item_id,name,category) VALUES (206,'Toyota Corolla Sedan 1.6l 97kW Comfort (Or Equivalent New Car)','Transportation');
INSERT INTO items(item_id,name,category) VALUES (224,'Preschool (or Kindergarten), Full Day, Private, Monthly for 1 Child','Childcare');
INSERT INTO items(item_id,name,category) VALUES (228,'International Primary School, Yearly for 1 Child','Childcare');