ALTER TABLE playbook_sessions ADD COLUMN type varchar(100);

UPDATE playbook_sessions AS ps
INNER JOIN (SELECT id FROM playbook_sessions GROUP BY playbook_id) t1
ON t1.id=ps.id
SET ps.type='CREATE';

UPDATE playbook_sessions SET type='VIEW' WHERE type IS NULL;

ALTER TABLE playbook_chat ADD COLUMN playbook_session_id BIGINT(20), ADD FOREIGN KEY(playbook_session_id) REFERENCES playbook_sessions(id);

UPDATE playbook_chat pc
JOIN playbook_sessions ps ON pc.session_id=ps.session_id
SET pc.playbook_session_id=ps.id;

ALTER TABLE playbook_chat DROP COLUMN session_id;