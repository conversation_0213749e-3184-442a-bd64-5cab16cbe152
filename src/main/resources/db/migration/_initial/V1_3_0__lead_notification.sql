CREATE TABLE IF NOT EXISTS `lead_notification` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT,
    `type` varchar(30) NOT NULL,
    `state` varchar(30) NOT NULL,
    `lead_id` bigint(11) NOT NULL,
    `user_id` bigint(11) NOT NULL,
    `created_date` datetime(3) NOT NULL,
    `last_updated_date` datetime(3) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

UPDATE `expert_user` SET `expertise_id`='1' WHERE `id`='8';
