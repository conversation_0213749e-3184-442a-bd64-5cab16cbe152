alter table login_account
    add referred_by bigint(11) null after onboard;

create table if not exists referral_code
(
    id bigint(11) not null auto_increment,
    referral_code varchar(255) not null,
    expiry datetime null,
    root_user_id bigint(11) not null,
    created_date datetime(3) not null,
    last_updated_date datetime(3) not null,
    last_updated_by bigint(11) null,
    constraint referral_code_pk
        primary key (id)
);

