ALTER TABLE `case_fees_approval_history` MODIFY COLUMN details_apostille_fees FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_certificate_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_dependent_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_government_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_professional_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_third_party_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_total_fees` FLOAT;
ALTER TABLE `case_fees_approval_history` MODIFY COLUMN `details_translation_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN details_apostille_fees FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_certificate_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_dependent_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_government_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_professional_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_third_party_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_total_fees` FLOAT;
ALTER TABLE `cases` MODIFY COLUMN `details_translation_fees` FLOAT;