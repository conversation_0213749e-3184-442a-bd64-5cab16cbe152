ALTER TABLE cases ADD deal_id bigint(10) DEFAULT NULL;
ALTER TABLE cases ADD fees_currency varchar(10) DEFAULT NULL;
ALTER TABLE case_fees_approval_history ADD fees_currency varchar(10) DEFAULT NULL;
ALTER TABLE case_status_master ADD deal_status_id bigint(20) DEFAULT NULL;
-- migration to populate currency symbol
UPDATE cases SET fees_currency=SUBSTRING(details_total_fees, 1, 1) where details_total_fees IS NOT NULL AND details_total_fees!='';

