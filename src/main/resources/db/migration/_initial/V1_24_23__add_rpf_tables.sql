CREATE TABLE IF NOT EXISTS  `rfp`(
    `id` bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `heading` varchar(500),
    `description` varchar(500),
    `status` varchar(500) NOT NULL,
    `resolved_date` datetime(6),
    `created_by` bigint(11) NOT NULL,
    `created_date` DATETIME(3),
    `last_updated_date` DATETIME(3)
   );

CREATE TABLE IF NOT EXISTS  `rfp_service`(
        `id` bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `service_name` varchar(500),
        `rfp_id` bigint(20),
         FOREIGN KEY (`rfp_id`) REFERENCES `rfp` (`id`)
  );

 CREATE TABLE IF NOT EXISTS  `rfp_country`(
        `id` bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
        `country` varchar(20),
        `service_id` bigint(20),
         <PERSON>OR<PERSON><PERSON><PERSON> KEY (`service_id`) REFERENCES `rfp_service` (`id`)
    );

CREATE TABLE IF NOT EXISTS `rfp_document` (
        `id` bigint(11) NOT NULL AUTO_INCREMENT,
        `file_name` varchar(255) DEFAULT NULL,
        `file_type` varchar(255) DEFAULT NULL,
        `file_size` bigint(20) DEFAULT NULL,
        `file_upload_date` DATETIME(3),
        `rfp_id` bigint DEFAULT NULL,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`rfp_id`) REFERENCES `rfp` (`id`)
);

CREATE TABLE rfp_assignee(
    `id` bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `rfp_id` bigint(20),
    `user_id` bigint(20),
    FOREIGN KEY (`rfp_id`) REFERENCES `rfp` (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `login_account` (`id`)
);

/*---- accesses for queries */
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('RFP', 'Rfp');

INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('RFP', 'INITIATE', 'Initiate a Rfp');

INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('RFP', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('RFP', 'OWN');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('RFP', 'REPORTEES');

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='RFP' AND mam.access_level='INITIATE')
    FROM bands b
    WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin');

/*-- add rfp visibilities */

/*--- Super admin will have full access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='RFP' AND vm.visibility='FULL')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin');

/*---- Account manager will have reportees access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='RFP' AND vm.visibility='REPORTEES')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager');

/*---- Applicant will have own access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='RFP' AND vm.visibility='OWN')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');



