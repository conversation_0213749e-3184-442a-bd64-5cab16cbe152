CREATE TABLE IF NOT EXISTS `dependent_visa` (
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) ,
  `last_name` varchar(255) NOT NULL,
  `relation_with_primary` varchar(255) NOT NULL,
  `nationality` varchar(255) NOT NULL,
  `residence_country` varchar(255) ,
  `contact_no` varchar(255) ,
  `email_address` varchar(255) ,
  `share_applicant_info` boolean ,
  `applicant_stay_type` varchar(255),
  `applicant_stay` bigint(20) ,
  `travel_purpose` varchar(255) ,
  `child_age` bigint(20) ,
  `travel_country` varchar(255) NOT NULL,
  `id` bigint(20),
   PRIMARY KEY (`id`),
   CONSTRAINT `FK3k4u6a7kskz4ld5gg3g3je40vbn` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('DEPENDENT_VISA', 'Dependent Visa', 'IMMIGRATION_GM', 'Immigration GM');