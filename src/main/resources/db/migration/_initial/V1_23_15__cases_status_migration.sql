-- ENTITY SETUP
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=2;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=2;
UPDATE cases SET status='PREPARING_APPLICATION' WHERE status='SUBMITTING_APPLICATION' AND category_id=2;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=2;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=2;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=2;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=2);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=2);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=2);
UPDATE case_milestones SET milestone_key='DOCUMENTS_DRAFTED' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=2);

-- BANK ACCOUNT
UPDATE cases SET status='REQUESTED_INFORMATION' WHERE status='AWAITING_INFORMATION' AND category_id=3;
UPDATE cases SET status='AWAITING_APPLICATION_DECISION' WHERE status='AWAITING_GOVERNMENT_RESPONSE' AND category_id=3;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=3;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=3;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=3;

UPDATE case_milestones SET milestone_key='CASE_INITIATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=3);
UPDATE case_milestones SET milestone_key='CASE_INITIATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=3);

-- OFFICE_SPACE
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=4;
UPDATE cases SET status='SUBMITTED_OFFER' WHERE status='SUBMITTING_APPLICATION' AND category_id=4;
UPDATE cases SET status='SUBMITTED_OFFER' WHERE status='AWAITING_GOVERNMENT_RESPONSE' AND category_id=4;
UPDATE cases SET status='PREPARING_AGREEMENT' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=4;
UPDATE cases SET status='PREPARING_AGREEMENT' WHERE status='PROCESSING_RESIDENCY' AND category_id=4;
UPDATE cases SET status='PREPARING_AGREEMENT' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=4;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='BEGAN_SEARCH' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='OFFER_SUBMITTED' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='OFFER_SUBMITTED' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='OFFER_SUBMITTED' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='OFFER_SUBMITTED' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=4);
UPDATE case_milestones SET milestone_key='OFFER_ACCEPTED' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=4);

-- COMPLEX_REQUIREMENT
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=5;
UPDATE cases SET status='IN_PROGRESS' WHERE status='SUBMITTING_APPLICATION' AND category_id=5;
UPDATE cases SET status='IN_PROGRESS' WHERE status='AWAITING_GOVERNMENT_RESPONSE' AND category_id=5;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=5;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=5;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=5;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=5);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=5);

-- RIGHT_TO_WORK
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=26;
UPDATE cases SET status='IN_PROGRESS' WHERE status='SUBMITTING_APPLICATION' AND category_id=26;
UPDATE cases SET status='IN_PROGRESS' WHERE status='AWAITING_GOVERNMENT_RESPONSE' AND category_id=26;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=26;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=26;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=26;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=26);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=26);

-- MANAGE_ASSIGNESS
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=9;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=9;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=9;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=9;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=9;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=9);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=9);

-- MANAGE_PAYROLL
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=11;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=11;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=11;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=11;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=11;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=11);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=11);

-- HR_EMP_SUPPORT
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=12;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=12;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=12;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=12;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=12;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=12);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=12);

-- INTELLECTUAL_PROPERTY
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=17;
UPDATE cases SET status='PREPARING_APPLICATION' WHERE status='VERIFYING_DOCUMENTS' AND category_id=17;
UPDATE cases SET status='PREPARING_APPLICATION' WHERE status='SUBMITTING_APPLICATION' AND category_id=17;
UPDATE cases SET status='AWAITING_OFFICIAL_RESPONSE' WHERE status='AWAITING_GOVERNMENT_RESPONSE' AND category_id=17;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=17;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=17;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=17;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=17);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=17);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=17);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=17);
UPDATE case_milestones SET milestone_key='APPLICATION_APPROVED' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=17);

-- DATA_PROTECTION
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=18;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=18;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=18;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=18;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=18;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=18);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=18);

-- RISK_MANAGEMENT
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=22;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=22;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=22;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=22;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=22;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=22);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=22);

-- COM_LAW_CONTRACTS
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=19;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=19;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=19;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=19;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=19;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=19);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=19);

-- EMP_BENEFITS_INSURANCE
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=20;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=20;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=20;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=20;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=20;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=20);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=20);

-- TAX_SUPPORT
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=24;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=24;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=24;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=24;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=24;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=24);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=24);

-- INSURANCE
UPDATE cases SET status='AWAITING_INFORMATION' WHERE status='REQUESTED_INFORMATION' AND category_id=23;
UPDATE cases SET status='PLANNING_IN_PROGRESS' WHERE status='IN_PROGRESS' AND category_id=23;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_ENTRY_VISA' AND category_id=23;
UPDATE cases SET status='IN_PROGRESS' WHERE status='PROCESSING_RESIDENCY' AND category_id=23;
UPDATE cases SET status='IN_PROGRESS' WHERE status='REQUEST_FOR_EVIDENCE' AND category_id=23;

UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PROCESS_RECOMMENDATION' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='EXPERT_CONSULTATION' WHERE milestone_key='PERSONALISED_EMPLOYEE_CONNECTION' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='COLLECTING_INFORMATION' WHERE milestone_key='DOCUMENTS_COLLECTED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_PREPARED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='APPLICATION_SUBMITTED' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
UPDATE case_milestones SET milestone_key='WORK_IN_PROGRESS' WHERE milestone_key='OFFICIAL_AGENCY_RESPONSE' AND case_id IN(SELECT id FROM cases WHERE category_id=23);
