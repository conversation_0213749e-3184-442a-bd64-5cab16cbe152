ALTER TABLE cases ADD contact_first_name  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD contact_last_name  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD contact_title  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD contact_number  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD contact_country  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD other_information  varchar(1000) DEFAULT NULL;
ALTER TABLE cases ADD contact_occupation  varchar(100) DEFAULT NULL;

drop table if exists bank_account;
CREATE TABLE IF NOT EXISTS `bank_account` (
  `country` varchar(3) NOT NULL,
  `entity_type` varchar(255) NOT NULL,
  `trading` bit(1) NOT NULL,
  `business_activity` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `address` varchar(300) NOT NULL,
  `full_time` bigint(20) NOT NULL,
  `part_time` bigint(20) NOT NULL,
  `non_contract_workers` bigint(20) NOT NULL,
  `expected_turnover` varchar(300) NOT NULL,
  `tax_status` varchar(255) NOT NULL,
  `income_come` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf710` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

drop table if exists office_space;
CREATE TABLE IF NOT EXISTS `office_space` (
  `country` varchar(3) NOT NULL,
  `assistance_finding_office` bit(1) NOT NULL,
  `contract` varchar(255) NOT NULL,
  `space_type` varchar(255) NOT NULL,
  `seating_space` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf711` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `assignee_cases` (
  `accommodation_support` bit(1) NOT NULL,
  `country` varchar(3) NOT NULL,
  `shipping_support` bit(1) NOT NULL,
  `education_support` bit(1) NOT NULL,
  `assignees_pay_type` varchar(255) NOT NULL,
  `tax_support` bit(1) NOT NULL,
  `expert_tax_services` varchar(100) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf712` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `payroll_cases` (
  `country` varchar(3) NOT NULL,
  `entity_in_jurisdiction` bit(1) NOT NULL,
  `staff_count` bigint(20) NOT NULL,
  `staff_type` varchar(255) NOT NULL,
  `salary_range` varchar(255) NOT NULL,
  `benefits_support` bit(1) NOT NULL,
  `employment_support` bit(1) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf713` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `hr_and_employment_support` (
  `country` varchar(3) NOT NULL,
  `entity_in_jurisdiction` bit(1) NOT NULL,
  `contracts_support` bit(1) NOT NULL,
  `policy_support` bit(1) NOT NULL,
  `liability_insurance` bit(1) NOT NULL,
  `employment_support` bit(1) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf714` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `intellectual_property` (
  `country` varchar(3) NOT NULL,
  `ip_support` varchar(255) NOT NULL,
  `interested_ip` varchar(500) NOT NULL,
  `protect_word_logo` varchar(255) NOT NULL,
  `trademark_name` varchar(255) NOT NULL,
  `copyright_nature` varchar(255) NOT NULL,
  `copyright_details` varchar(1000) NOT NULL,
  `patent_description` varchar(1000) NOT NULL,
  `design_description` varchar(1000) NOT NULL,
  `ip_assign_support` bit(1) NOT NULL,
  `ip_dispute_support` bit(1) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf715` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `single_immigration_visa` (
  `visa_type` varchar(255) NOT NULL,
  `nationality` varchar(255) NOT NULL,
  `country` varchar(3) NOT NULL,
  `from_country` varchar(255) NOT NULL,
  `trip_purpose` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `covid_vaccinated` varchar(255) NOT NULL,
  `dependant_travelling` varchar(255) NOT NULL,
  `applicant_stay` bigint(20) NOT NULL,
  `estimated_salary` DOUBLE NOT NULL,
  `paid_country` varchar(255) NOT NULL,
  `entity_in_host_country` bit(1) NOT NULL,
  `duties` varchar(255) NOT NULL,
  `qualification` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf716` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `multiple_immigration_visa` (
  `visa_type` varchar(255) NOT NULL,
  `country` varchar(3) NOT NULL,
  `number_visa_permit` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf717` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `traveller` (
  `full_name` varchar(255) NOT NULL,
  `nationality` varchar(255) NOT NULL,
  `travel_country` varchar(255) NOT NULL,
  `estimated_salary` DOUBLE NOT NULL,
  `duration_of_stay` bigint(20) NOT NULL,
  `duties` varchar(255) NOT NULL,
  `qualification` varchar(255) NOT NULL,
  `dependant_travelling` varchar(255) NOT NULL,
  `dependant_duration_of_stay` varchar(255) NOT NULL,
  `multiple_immigration_visa_case_id` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`multiple_immigration_visa_case_id`) REFERENCES `multiple_immigration_visa` (`id`)
);

CREATE TABLE IF NOT EXISTS `entity_setup` (
  `country` varchar(3) NOT NULL,
  `entity_type` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `business_activities` varchar(255) NOT NULL,
  `hq_country` varchar(3) NOT NULL,
  `hq_state` varchar(255) NOT NULL,
  `hq_city` varchar(255) NOT NULL,
  `hq_zipcode` varchar(255) NOT NULL,
  `hq_address` varchar(255) NOT NULL,

  `foreign_owned` bit(1) NOT NULL,
  `require_address` bit(1) NOT NULL,
  `support_tax_registration` bit(1) NOT NULL,

  `cs_full_Name` varchar(255) NOT NULL,
  `cs_dob` varchar(255) NOT NULL,
  `cs_occupation` varchar(255) NOT NULL,
  `cs_nationality` varchar(255) NOT NULL,
  `cs_residential_address` varchar(255) NOT NULL,
  `cs_service_address` varchar(255) NOT NULL,

  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf718` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `director` (
  `full_Name` varchar(255) NOT NULL,
  `dob` varchar(255) NOT NULL,
  `occupation` varchar(255) NOT NULL,
  `nationality` varchar(255) NOT NULL,
  `residential_address` varchar(255) NOT NULL,
  `service_address` varchar(255) NOT NULL,
  `entity_setup_case_id` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`entity_setup_case_id`) REFERENCES `entity_setup` (`id`)
);

CREATE TABLE IF NOT EXISTS `shareholder` (
  `full_Name` varchar(255) NOT NULL,
  `percentage_share` DOUBLE NOT NULL,
  `service_address` varchar(255) NOT NULL,
  `entity_setup_case_id` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`entity_setup_case_id`) REFERENCES `entity_setup` (`id`)
);



CREATE TABLE IF NOT EXISTS `data_protection` (
  `data_protection` bit(1) NOT NULL,
  `data_officer` bit(1) NOT NULL,
  `data_controller` bit(1) NOT NULL,
  `data_compliance` bit(1) NOT NULL,
  `data_policies` bit(1) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf719` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `commercial_support` (
  `legal_advice` varchar(255) NOT NULL,
  `current_contracts` varchar(255) NOT NULL,
  `purchase_contract` varchar(255) NOT NULL,
  `commercial_needs` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf720` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `risk_management` (
  `risk_policy` varchar(255) NOT NULL,
  `last_policy_updated` date NOT NULL,
  `identified_potential_risks` varchar(255) NOT NULL,
  `potential_risks` varchar(1000) NOT NULL,
  `strategy_advice` varchar(255) NOT NULL,
  `insurance_for_jurisdiction` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf721` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `employee_benefits_and_insurance` (
  `country` varchar(3) NOT NULL,
  `employees_health_care` varchar(255) NOT NULL,
  `existing_insurance` varchar(255) NOT NULL,
  `insurance_partner` varchar(255) NOT NULL,
  `local_insurance` varchar(255) NOT NULL,
  `physical_products` varchar(255) NOT NULL,
  `physical_office` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf722` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `tax_support` (
  `tax_support` varchar(255) NOT NULL,
  `estimated_revenue` varchar(255) NOT NULL,
  `country` varchar(3) NOT NULL,
  `payroll` varchar(255) NOT NULL,
  `employee_benefits` varchar(2000) NOT NULL,
  `benefits_taxable` varchar(255) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf723` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);