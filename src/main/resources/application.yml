# Server port
server.port: 8080
server.forward-headers-strategy: framework

spring:
  cloud:
    openfeign:
      client:
        config:
          PythonApiClient:
            connectTimeout: 300000
            readTimeout: 300000

  devtools:
    add-properties: true # set this to false in production
  flyway:
    locations:
      - classpath:db/migration/_initial
    clean-on-validation-error: false
    out-of-order: true

  mvc:
    async:
      request-timeout: 90000 # set async timeout (in millis)
    throw-exception-if-no-handler-found: true
#    pathmatch.matching-strategy: ANT_PATH_MATCHER

  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false

  servlet.multipart:
    max-file-size: 10MB
    max-request-size: 10MB

  datasource:
    url: ************************************************************************************
    username: root
    password: root
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      connectionTimeout: 30000    # 30 sec (Timeout settings for getting a connection from the pool)
      idleTimeout: 60000          # 60sec (Timeout for releasing idle connections from the pool)
      maxLifetime: 180000         # 180sec (The max lifetime of a connection. Connection exceeding this will be release from the pool.)
      minimumIdle: 5              # min 5 idle connections (The min connections to keep in the pool. This value should be small.)
      maximumPoolSize: 10         # max 10 connections in pool

  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

  mail:
    default-encoding: UTF-8
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: changeme!!!
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    protocol: smtp
    test-connection: false
    sender-email-address: <EMAIL>
    mock-sending: false
    subject-prefix: "[Centuro Global - LOCAL MODE]"
    expert-contact-email:
      to: <EMAIL>
      cc: <EMAIL>, <EMAIL>, <EMAIL>
    member-contract-email:
      to: <EMAIL>
      cc: <EMAIL>, <EMAIL>
    case-initiate-email:
      to: <EMAIL>
      cc: <EMAIL>
    renewal-contract-acceptance:
      to: <EMAIL>
    query-submission-email:
      bcc: <EMAIL>
    rfp-submission-email:
      bcc: <EMAIL>
    cg-support:
      case:
        to: <EMAIL>
      query:
        to: <EMAIL>
      rfp:
        to: <EMAIL>

# actuator properties
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: health, prometheus, httptrace, metrics, info, beans, configprops, env
      base-path: /actuator

linkedin:
  client-id: 8610khfpyykwih
  client-secret: Ccq6fUKJKzdlYcxX

# swagger properties
#springfox:
#  documentation:
#    swagger:
#      v2:
#        path: /swagger-api-docs

# logs configuration
logging:
  file-enabled: false  # disable file logging
  console-as-json: false  # set this to true to log as json.
  pattern:
    level: "%clr(%5p){faint} %clr([%-32.32X{xid}]){faint}"
  level:
    root: ERROR
    com.amazonaws: INFO
    org.springframework.boot: INFO
    org.springframework.web: INFO
    com.centuroglobal: DEBUG

# app properties
app:

  api:
    python:
      #base-url: http://127.0.0.1:8000/extract-doc-details
      base-url: https://qat-python-api.centuroglobal.com

  server-url: http://localhost:${server.port}
  web-url: http://localhost:4200

  recaptcha.secretKey: 6Ldp71EmAAAAAAh7WJfYeNrsSx4J_duQDwIqV_o_

  authentication:
    jwt:
      secret: 2Jmma7ETKdKnT2oDiFzB
      temp-token-expiry-in-secs: 1800   # 30min token
      access-token-expiry-in-secs: 7200 # 2hour token

    mfa:
      token-expiry-in-secs: 600   # 10min

  # App-Cache Settings (Unit = Milliseconds)
  app-cache.settings:
    country-region: 600000
    expertise: 600000
    stripe-product: 600000

  verification:
    token-expiry-in-days: 7
    cleanup-after-expiry-in-days: 30
    cron-schedule: '0/10 * * * * *'

  subscription:
    cron-schedule: '0/10 * * * * *'
    expiry-days: 7
    receipt-request-mail:
      cc: "<EMAIL>"

  lead-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    centuro-address: '<EMAIL>'

  concierge-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    contact-address: '<EMAIL>'

  completed-case-archive:
    cron-schedule: "0 0 1 * * *"
    diff-days: 7

  case-fees-reminder-email:
    cron-schedule: "0 0 2 * * *"

  case-status-reminder-email:
    cron-schedule: "0 30 8 * * *"
    start-date: 2023-01-10 00:00

  case-visa-expiry-reminder-email:
    cron-schedule: "0 0 3 * * *"

  case-group-chat-notification-email:
    cron-schedule: "0 0 5,18 * * *"

  query-group-chat-notification-email:
    cron-schedule: "0 0 6,19 * * *"

  password-policy:
    min-length: 6
    max-length: 15
    require-lowercase: true
    require-uppercase: true
    require-digit: true
    require-special-char: true
    validation-error-message: "Password must be between 6 - 15 chars and must contain at least 1 uppercase char, 1 lowercase char, 1 digit and 1 special char"

  expert-profile:
    bio-preview-max-chars: 100

  lead:
    recent-preview-count: 6

  blueprint:
    free-to-view: SG

  executor:
    default:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 10

  scheduler:
    task:
      pool-size: 5

  aws:
    access-key:
    secret-key: 
    s3:
      default-bucket: dummy
      user-profile-folder: user_profile
      company-logo-folder: company_logo
      blueprint-folder: blueprint
      proxied-url:
      circle-banner-folder: circle_banner
      event-banner-folder: event_banner
      master-data-bucket:  cg-goglobal-admin-uat
      member-contract-bucket: cg-goglobal-contracts-uat
      case-doc-bucket: case-doc-repo-qat
      onboarding-doc-folder: onboarding_docs
      partner-onboarding-doc-folder: partner_onboarding_docs
      corporate-doc-folder: corporate_docs
      user-doc-folder: user_docs
      public-bucket-uri: https://d36494guqocud0.cloudfront.net

  stripe:
    mock-mode: false
    public-key: pk_test_dummy
    secret-key: sk_test_dummy
    product:
      corporate: prod_dummy
    events:
      checkout-session:
        supported-types: checkout.session.completed # for info purposes, See Stripe dashboard webhooks
        secret: whsec_dummy01
      invoice:
        supported-types: invoice.payment_failed,invoice.payment_succeeded
        secret: whsec_dummy02
      payment:
        supported-types: payment_intent.payment_failed,payment_intent.succeeded
        secret: whsec_dummy03
      subscription:
        supported-types: customer.subscription.deleted,customer.subscription.updated
        secret: whsec_dummy04
    scheduler-fixed-rate:
      process-events: 800 #msec
  costOfLiving:
    expiry:
      city_price_days: 30
      city_day: 30
    apiKey: i08vx9gsyopobr
  hubspot:
    apiKey: Bearer ********************************************
    cron-schedule: '0 0 */6 * * *'
    email-recipients: '<EMAIL>, <EMAIL>'
  news:
    expiry:
      minutes: 90
    apiToken: REwQR48copW0DbMSqfnQfn5XZ5w0Wm2fnNAggCm1
  indices:
    expiry:
      days: 180
  cors:
    domains: "*"
  openapi:
    api-key: ***************************************************
    prompt-message: 'You are an advanced chatbot named Centuro AI.
      \n You should only refer to yourself as the Centuro AI. \n
      You can help users with any questions they have about expanding their business into new countries, immigration, moving international staff around, regarding Global Mobility, Tax and Accounting, Legal and Insurance.
      If there are any questions for topics like sports, news, entertainment, medical or transport, please show: Sorry, we cannot help you with this answer. However, to speak to our expert, please click this link to book in a free call - https://meetings.hubspot.com/benjamin166.
      If you want to advise the user to check information or consult with or contact an embassy or consulate or local firm you should instead suggest they consult with a Centuro Expert for more detailed information. Centuro Global experts can support with all necessary applications as well as book appointments with embassies.
      If a user asks about how much something costs or estimates, then at the end of your response you should ask the user if they would like to submit a request for a detail breakdown of the costs including professional fees.
      If a user would like to request pricing regarding Right to Work or Immigration, they should fill in this form: https://forms.gle/WVDBUcpHPjfTX2Dt7
      For all response from your side, please don’t mention the year for which the data or your response belongs to.
      For the first response to any user, please start with the following line: Welcome to Centuro Global! I am Centuro AI to help you advise on topics related to Business Expansion, Immigration, Global Mobility, Tax, Legal and Insurance.'
    assistant-id: "asst_L2O3hifYB7OOLl5Dhq9LuTjJ"
    file-assistant-id: "asst_Ow2FTERRVlVd9GqOnCIgCzf7"
    message-read-timeout: 120 # message read timeout in seconds
---
spring.config.activate.on-profile: test

spring:
  jpa:
    properties:
      hibernate.show_sql: true
      hibernate.format_sql: true

  flyway:
    locations:
      - classpath:db/migration/_initial
      - classpath:db/migration/_test

  mail:
    mock-sending: true
    subject-prefix: "[Centuro Global - TEST MODE]"

  datasource:
    # we will be using test containers' mariadb
    url: jdbc:tc:mariadb:10.2:///centuro_global_test?useLegacyDatetimeCode=false&serverTimezone=UTC
    driver-class-name: org.testcontainers.jdbc.ContainerDatabaseDriver

app:
  app-cache.settings:
    expertise: 0 # not caching expertise for test
  aws:
    s3:
      user-profile-folder: test_user_profile
      company-logo-folder: test_company_logo
      blueprint-folder: test_blueprint

  stripe:
    mock-mode: true