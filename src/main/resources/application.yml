# Server port
server.port: 8080
server.forward-headers-strategy: framework

spring:
  datasource:
    url: ************************************************************************************
    username: root
    password: root
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      connectionTimeout: 30000    # 30 sec (Timeout settings for getting a connection from the pool)
      idleTimeout: 60000          # 60sec (Timeout for releasing idle connections from the pool)
      maxLifetime: 180000         # 180sec (The max lifetime of a connection. Connection exceeding this will be release from the pool.)
      minimumIdle: 5              # min 5 idle connections (The min connections to keep in the pool. This value should be small.)
      maximumPoolSize: 10         # max 10 connections in pool
  mail:
    default-encoding: UTF-8
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: changeme!!!
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    protocol: smtp
    test-connection: false
    sender-email-address: <EMAIL>
    mock-sending: false
    subject-prefix: "[Centuro Global - LOCAL MODE]"
    expert-contact-email:
      to: <EMAIL>
      cc: <EMAIL>
    member-contract-email:
      to: <EMAIL>
      cc: <EMAIL>
    case-initiate-email:
      to: <EMAIL>
      cc: <EMAIL>
    renewal-contract-acceptance:
      to: <EMAIL>

# actuator properties
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: health, prometheus, httptrace, metrics, info, beans, configprops, env
      base-path: /actuator

# logs configuration
logging:
  file-enabled: false  # disable file logging
  console-as-json: false  # set this to true to log as json.
  pattern:
    level: "%clr(%5p){faint} %clr([%-32.32X{xid}]){faint}"
  level:
    root: ERROR
    com.amazonaws: INFO
    org.springframework.boot: INFO
    org.springframework.web: INFO
    com.centuroglobal: DEBUG

# app properties
app:
  server-url: http://localhost:${server.port}
  web-url: http://localhost:4200

  recaptcha.secretKey: 6Ldp71EmAAAAAAh7WJfYeNrsSx4J_duQDwIqV_o_

  authentication:
    jwt:
      secret: 2Jmma7ETKdKnT2oDiFzB
      temp-token-expiry-in-secs: 1800   # 30min token
      access-token-expiry-in-secs: 7200 # 2hour token

  scheduler:
    task:
      pool-size: 5

  aws:
    access-key:
    secret-key:
    s3:
      default-bucket: dummy
      user-profile-folder: user_profile
      company-logo-folder: company_logo
      blueprint-folder: blueprint
      proxied-url:
      circle-banner-folder: circle_banner
      event-banner-folder: event_banner
      master-data-bucket: cg-goglobal-admin-qat
      member-contract-bucket: cg-goglobal-contracts-qat
      case-doc-bucket: case-doc-repo-qat
      public-bucket-uri: https://d36494guqocud0.cloudfront.net


  completed-case-archive:
    cron-schedule: "0 0 1 * * *"
    diff-days: 7

  case-fees-reminder-email:
    cron-schedule: "0 0 2 * * *"

  case-status-reminder-email:
    cron-schedule: "0 30 8 * * *"
    start-date: 2023-01-10 00:00

  case-visa-expiry-reminder-email:
    cron-schedule: "0 0 3 * * *"

  case-group-chat-notification-email:
    cron-schedule: "0 */5 * * * *"

  query-group-chat-notification-email:
    cron-schedule: "0 */5 * * * *"

  partner-supplier-contract.expiry-reminder-email:
      cron-schedule: "0 0 4 * * *"

  subscription-billing:
    cron-schedule: "0 0 0 1 * *"
#    cron-schedule: "0 45 9 3 * *"

  task-dueDate-reminder-email:
    cron-schedule: "0 0 9 * * *"

  task-daily-reminder-email:
    cron-schedule: "0 0 5 * * *"

# At 01:00 AM, Monday through Friday. skip weekends
  task-workflow:
    cron-schedule: "0 0 1 * * 1-5"

  hubspot:
    apiKey: Bearer pat-na1-************************************
    cron-schedule: '0 0 */6 * * *'
    email-recipients: '<EMAIL>, <EMAIL>'
    deals:
      cron-schedule: '0 0 4-8 * * *'

  stripe:
    mock-mode: false
    public-key: pk_test_dummy
    secret-key: sk_test_dummy
    product:
      corporate: prod_dummy
    events:
      checkout-session:
        supported-types: checkout.session.completed # for info purposes, See Stripe dashboard webhooks
        secret: whsec_dummy01
      invoice:
        supported-types: invoice.payment_failed,invoice.payment_succeeded
        secret: whsec_dummy02
      payment:
        supported-types: payment_intent.payment_failed,payment_intent.succeeded
        secret: whsec_dummy03
      subscription:
        supported-types: customer.subscription.deleted,customer.subscription.updated
        secret: whsec_dummy04
    scheduler-fixed-rate:
      process-events: 800 #msec

  verification:
    token-expiry-in-days: 7
    cleanup-after-expiry-in-days: 30
    cron-schedule: '0/10 * * * * *'

    subscription:
      cron-schedule: '0/10 * * * * *'
      expiry-days: 7

  lead-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    centuro-address: '<EMAIL>'

  concierge-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    contact-address: '<EMAIL>'

  expert-profile:
    bio-preview-max-chars: 100

  password-policy:
    min-length: 6
    max-length: 15
    require-lowercase: true
    require-uppercase: true
    require-digit: true
    require-special-char: true
    validation-error-message: "Password must be between 6 - 15 chars and must contain at least 1 uppercase char, 1 lowercase char, 1 digit and 1 special char"
  api:
    python:
      base-url: https://qat-python-api.centuroglobal.com