package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.task.TaskCompanyType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskView
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.task.dto.*
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskCountResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryCategoryRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.task.SharedTaskWorkflowService
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlin.jvm.optionals.getOrNull


private val log = KotlinLogging.logger {}

@Service
class TaskService(
    private val taskEmailService: TaskEmailService,
    private val taskRepository: TaskRepository,
    private val taskAssigneeRepository: TaskAssigneeRepository,
    private val taskReminderRepository: TaskReminderRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val userProfileUtil: UserProfileUtil,
    private val caseRepository: CaseRepository,
    private val queryRepository: QueryRepository,
    private val queryCategoryRepository: QueryCategoryRepository,
    private val rfpRepository: RfpRepository,
    private val partnerRepository: PartnerRepository,
    private val corporateRepository: CorporateRepository,
    private val accountRepository: AccountEntityRepository,
    private val caseService: CaseService,
    private val taskWorkflowService: TaskWorkflowService,
    private val sharedTaskWorkflowService: SharedTaskWorkflowService

) : BaseService<TaskEntity, Long>() {

    @Transactional
    fun createTask(createTaskRequest: CreateTaskRequest, authenticatedUser: AuthenticatedUser): Long {

        validateReferenceId(createTaskRequest, authenticatedUser.partnerId)

        val companyIdType = getCompanyIdAndType(authenticatedUser)

        var task = TaskEntity(
            name = createTaskRequest.name,
            description = createTaskRequest.description,
            referenceId = createTaskRequest.referenceId,
            referenceType = createTaskRequest.referenceType,
            visibility = createTaskRequest.visibility,
            dueDate = createTaskRequest.dueDate?.let { TimeUtil.fromInstantMillis(it) },
            completedDate = createTaskRequest.completedDate?.let { TimeUtil.fromInstantMillis(it) },
            priority = createTaskRequest.priority,
            status = createTaskRequest.status,
            partner = authenticatedUser.partnerId?.let { partnerRepository.getReferenceById(it)},
            companyId = companyIdType.first,
            companyType = companyIdType.second,
            instruction = createTaskRequest.instruction,
            usefulLinks = createTaskRequest.usefulLinks,
            createdBy = authenticatedUser.userId,
            updatedBy = authenticatedUser.userId
        )

        task = taskRepository.save(task)

        task.assignee = createTaskRequest.taskAssignee.map {
            val user = loginAccountRepository.findByIdOrNull(it.userId)?: throw ApplicationException(ErrorCode.NOT_FOUND)
            taskAssigneeRepository.save(
                TaskAssigneeEntity(
                    task = task,
                    assigneeId = user,
                    type = it.assigneeType,
                    companyId = it.companyId,
                    corporatePartnerId = getCorporatePartnerId(user)
                )
            )
        }.toMutableList()

        task.reminders = createTaskRequest.taskReminder.map { it2 ->
            val reminderDate = it2.dateTime?.let { TimeUtil.fromInstantMillis(it) }
            validateReminder(reminderDate, task.dueDate!!)
            taskReminderRepository.save(
                TaskReminderEntity(
                    task = task,
                    dateTime = reminderDate
                )
            )
        }.toMutableList()

        if (task.assignee != null) {

            val createdByUser = task.createdBy?.let { loginAccountRepository.findById(it).get() }

            task.assignee!!.forEach { taskAssigneeEntity->
                taskAssigneeEntity.assigneeId?.let {
                    if(createdByUser!=null && createdByUser.id!=taskAssigneeEntity.assigneeId!!.id){
                        val performerFullName=
                            createdByUser.firstName+ " " +createdByUser.lastName
                        taskEmailService.taskEmail(task, it.firstName, performerFullName,  it.email, AppConstant.CONST_NEW_TASK)
                    }
                }
            }
        }

        return task.id?.let { task.id } ?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
    }

    private fun getCorporatePartnerId(user: LoginAccountEntity): Long {
        return if(user is CorporateUserEntity) {
            user.corporate.partner?.id?:-1
        }
        else {
            -1
        }
    }

    private fun validateReminder(reminderDate: LocalDateTime?, dueDate: LocalDateTime) {
        if (reminderDate!=null && (reminderDate.isAfter(dueDate) || reminderDate.isBefore(LocalDateTime.now()))) {
            log.error("reminder date should be between current date and due date")
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
    }

    private fun validateReferenceId(createTaskRequest: CreateTaskRequest, partnerId: Long?) {
        when (createTaskRequest.referenceType) {
            ReferenceType.CASE -> {
                if (partnerId != null) {
                    createTaskRequest.referenceId?.let { caseRepository.findByIdAndPartnerId(it, partnerId) }?.getOrNull()
                }
                else {
                    createTaskRequest.referenceId?.let { caseRepository.findByIdOrNull(it) }
                } ?: throw ApplicationException(ErrorCode.REFERENCE_ID_NOT_FOUND)
            }
            ReferenceType.QUERY -> {
                if (partnerId != null) {
                    createTaskRequest.referenceId?.let { queryRepository.findByIdAndPartnerId(it, partnerId) }?.getOrNull()
                }
                else {
                    createTaskRequest.referenceId?.let { queryRepository.findByIdOrNull(it) }
                } ?: throw ApplicationException(ErrorCode.REFERENCE_ID_NOT_FOUND)
            }
            ReferenceType.RFP -> {
                if (partnerId != null) {
                    createTaskRequest.referenceId?.let { rfpRepository.findByIdAndPartnerId(it, partnerId) }?.getOrNull()
                }
                else {
                    createTaskRequest.referenceId?.let { rfpRepository.findByIdOrNull(it) }
                } ?: throw ApplicationException(ErrorCode.REFERENCE_ID_NOT_FOUND)
            }
            ReferenceType.OPEN -> {
                createTaskRequest.referenceId = null
            }
            ReferenceType.WORKFLOW -> {

            }
            ReferenceType.OTHER -> throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }
    }

    @Transactional
    fun updateTask(taskId:Long,
                   createTaskRequest: CreateTaskRequest, authenticatedUser: AuthenticatedUser): Long {

        val taskEntity = getTask(taskId, authenticatedUser)

        validateReferenceId(createTaskRequest, authenticatedUser.partnerId)

        taskEntity.name = createTaskRequest.name
        taskEntity.description = createTaskRequest.description
        taskEntity.referenceId = createTaskRequest.referenceId
        taskEntity.referenceType = createTaskRequest.referenceType
        taskEntity.visibility = createTaskRequest.visibility
        taskEntity.dueDate = createTaskRequest.dueDate?.let { TimeUtil.fromInstantMillis(it) }
        taskEntity.completedDate = createTaskRequest.completedDate?.let { TimeUtil.fromInstantMillis(it) }
        taskEntity.priority = createTaskRequest.priority
        taskEntity.status = createTaskRequest.status
        taskEntity.usefulLinks = createTaskRequest.usefulLinks
        taskEntity.instruction = createTaskRequest.instruction
        taskEntity.updatedBy = authenticatedUser.userId
        val oldAssigneeEntity = if(taskEntity.assignee == null) emptyList<TaskAssigneeEntity>().toMutableList() else taskEntity.assignee
        taskEntity.assignee?.clear()
        taskEntity.reminders?.clear()
        taskRepository.save(taskEntity)
        taskRepository.flush()

        taskAssigneeRepository.deleteByTaskId(taskId)
        taskReminderRepository.deleteByTask(taskEntity)

        val taskEntityRef = taskRepository.getReferenceById(taskId)

        val taskAssigneeEntityList: MutableList<TaskAssigneeEntity> = emptyList<TaskAssigneeEntity>().toMutableList()
        createTaskRequest.taskAssignee.forEach {
            val user = loginAccountRepository.getReferenceById(it.userId)
            taskAssigneeEntityList.add(TaskAssigneeEntity(
                task = taskEntityRef,
                assigneeId = user,
                type = it.assigneeType,
                companyId = it.companyId,
                corporatePartnerId = getCorporatePartnerId(user)
            ))
        }

        taskAssigneeEntityList.forEach {
            taskAssigneeRepository.save(it)
        }

        taskEntity.reminders = createTaskRequest.taskReminder.map { it2 ->
            val reminderDate = it2.dateTime?.let { TimeUtil.fromInstantMillis(it) }
            validateReminder(reminderDate, taskEntity.dueDate!!)
            taskReminderRepository.save(
                TaskReminderEntity(
                    task = taskEntity,
                    dateTime = reminderDate
                )
            )
        }.toMutableList()

        val updatedAssigneesForMail = removeMatchingAssignees(oldAssigneeEntity!!, taskAssigneeEntityList)

        val createdByUser = taskEntity.createdBy?.let { loginAccountRepository.findById(it).get() }

        updatedAssigneesForMail?.forEach { user->
            user.assigneeId?.let {
                if(createdByUser!=null && createdByUser.id!=user.assigneeId!!.id){

                    val performerFullName=createdByUser.firstName+ " " + createdByUser.lastName

                    taskEmailService.taskEmail(taskEntity, it.firstName, performerFullName, it.email,AppConstant.CONST_NEW_TASK)
                }
            }
        }

        return taskEntity.id?.let { taskEntity.id } ?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
    }

    fun removeMatchingAssignees(oldAssigneeEntity: MutableList<TaskAssigneeEntity>, taskAssigneeEntityList: MutableList<TaskAssigneeEntity>?): List<TaskAssigneeEntity>? {
        val userIdsInOldList = oldAssigneeEntity.mapNotNull { it.assigneeId?.id }
        if (taskAssigneeEntityList != null) {
            return taskAssigneeEntityList.filterNot { it.assigneeId?.id in userIdsInOldList }
        }
        return emptyList()
    }

    @Transactional(readOnly = true)
    fun retrieve(taskId: Long, authenticatedUser: AuthenticatedUser): TaskDetails {

        val task = getTask(taskId, authenticatedUser)

        return TaskDetails(
            id = task.id!!,
            name = task.name,
            description = task.description,
            status = task.status,
            referenceId = task.referenceId,
            referenceType = task.referenceType,
            visibility = task.visibility,
            dueDate = task.dueDate?.let { TimeUtil.toEpochMillis(it) },
            completedDate = task.completedDate?.let { TimeUtil.toEpochMillis(it) },
            priority = task.priority,
            assignee = task.assignee?.map { it2 -> TaskAssigneeDto(it2.type, it2.assigneeId!!.id!!, userProfileUtil.retrieveProfile(it2.assigneeId!!.id!!), it2.companyId) }?.toMutableList(),
            createdBy = task.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
            category = listOf(),
            overDueDays = null,
            createdAt = TimeUtil.toEpochMillis(task.createdDate),
            reminders = task.reminders?.map { TaskReminderDto(it.dateTime?.let { it1 -> TimeUtil.toEpochMillis(it1) }) },
            usefulLinks = task.usefulLinks,
            instruction = task.instruction,
            progress = task.progress,
            caseMilestone = task.caseMilestone,
            plannedStartDate = task.plannedStartDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
            startDate = task.startDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
            expectedDueDate = task.expectedDueDate?.let { it1 -> TimeUtil.toEpochMillis(it1) }
        )
    }

    /**
     * Note: DO NOT USE super.get to get tasks.
     * Since the BaseService.get(id, repository, accessKey) implementation
     * can fetch the entities for PARTNER and CG ADMIN/SUPER ADMIN roles only. This method can also return the task for
     * Expert and Supplier users. This method needs to return task which is assigned to current user but is public.
     * Since all these implementation was specific to tasks we can not use/modify super method.
     *
     */
    private fun getTask(taskId: Long, authenticatedUser: AuthenticatedUser): TaskEntity {

        if(authenticatedUser.role == Role.ROLE_CORPORATE.name) {
            return getTaskForCorporateById(taskId, authenticatedUser)
        }

        val companyIdType = getCompanyIdAndType(authenticatedUser)

        val caseIds = if(AdminAccessUtil.isExpert(authenticatedUser.role)) {
            getExpertsCaseIds(authenticatedUser.userId)
        }
        else {
            null
        }

        return taskRepository.getTask(
            taskId,
            authenticatedUser.userId,
            companyIdType.first,
            companyIdType.second,
            TaskVisibility.PUBLIC,
            caseIds,
            authenticatedUser.partnerId ?: -1
        ).orElseThrow {
            throw ApplicationException(ErrorCode.NOT_FOUND)
        }

    }

    private fun getTaskForCorporateById(taskId: Long, authenticatedUser: AuthenticatedUser): TaskEntity {

        var reportees = super.getReportees(authenticatedUser, "TASK")
        if(reportees?.isEmpty() == true) {
            reportees = null
        }

        return taskRepository.getTaskForCorporate(
            taskId,
            authenticatedUser.companyId!!,
            authenticatedUser.userId,
            reportees
        ) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
    }

    @Transactional
    fun updateStatus(taskId: Long, taskStatus: TaskStatus, authenticatedUser: AuthenticatedUser): Boolean? {
        val taskEntity = getTask(taskId, authenticatedUser)
        taskEntity.status = taskStatus
        if (TaskStatus.COMPLETED == taskStatus) {
            taskEntity.completedDate = LocalDateTime.now()

            if (taskEntity.referenceType == ReferenceType.WORKFLOW) {
                val workflow = taskEntity.taskTemplate!!.workflow

                /*val nextTask = taskEntity.taskTemplate!!.workflow!!.taskTemplates.firstOrNull {
                    it.displayOrder == taskEntity.taskTemplate!!.displayOrder!!.plus(1) }
                nextTask?.let {
                    it.task!!.startDate = TimeUtil.getNthWorkingDay(LocalDateTime.now(), 1)

                    it.task!!.expectedDueDate = TimeUtil.getNthWorkingDay(it.task!!.startDate!!, it.expectedTimeline-1)


                }*/
                val completedTaskTemplate = taskEntity.taskTemplate!!
                val filteredTasks = workflow!!.taskTemplates.sortedBy { it1 -> it1.displayOrder }.filter {
                        it2 -> it2.displayOrder!! > completedTaskTemplate.displayOrder!!
                }
                val dueDate = taskEntity.completedDate!!
                sharedTaskWorkflowService.populateTimelines(filteredTasks, dueDate)

                if (workflow.referenceType == "CASE") {
                    val case = caseService.fetchCaseEntity(workflow.referenceId!!, authenticatedUser)
                    case.percentCompletion = (taskEntity.progress?.toInt() ?: 0)
                    caseService.updateCaseStatusAndMilestone(
                        case,
                        taskEntity.caseStatus!!,
                        "",
                        authenticatedUser.userId,
                        taskEntity.caseMilestone,
                        false,
                        authenticatedUser.email
                    )
                    val tasks = listOf(taskEntity, filteredTasks.getOrNull(0)?.task)
                    taskWorkflowService.populateMilestone(tasks, workflow.referenceId!!, false)
                }
            }

        } else if(TaskStatus.IN_PROGRESS == taskStatus) {

            taskEntity.inProgressDate = LocalDateTime.now()
            taskEntity.completedDate = null

            // update task timelines if task is started early
            if (taskEntity.referenceType == ReferenceType.WORKFLOW &&
                taskEntity.inProgressDate!!.toLocalDate().isBefore(taskEntity.startDate!!.toLocalDate())) {
                val workflow = taskEntity.taskTemplate!!.workflow

                val filteredTasks = workflow!!.taskTemplates.sortedBy { it1 -> it1.displayOrder }.filter {
                        it2 -> it2.displayOrder!! > taskEntity.taskTemplate!!.displayOrder!!
                }
                // since task marked in progress start date will become in progress date
                taskEntity.startDate = taskEntity.inProgressDate
                taskEntity.expectedDueDate = TimeUtil.getNthWorkingDay(taskEntity.startDate!!, taskEntity.taskTemplate!!.expectedTimeline-1)
                val dueDate = taskEntity.expectedDueDate!!
                sharedTaskWorkflowService.populateTimelines(filteredTasks, dueDate)
            }

        }  else {
            taskEntity.completedDate = null
        }

        taskEntity.updatedBy = authenticatedUser.userId
        taskRepository.save(taskEntity)
        val user = taskEntity.createdBy?.let { loginAccountRepository.findById(it).get() }
        if (user != null && user.id!=authenticatedUser.userId) {
            taskEmailService.taskEmail(taskEntity, user.firstName,
                authenticatedUser.firstName +" " +authenticatedUser.lastName,user.email,AppConstant.CONST_UPDATE_TASK)
        }
        return true
    }

    private fun populateTimelines(workflow: TaskWorkflowEntity?, taskEntity: TaskEntity) {

        if (workflow!=null) {
            val templates =
                workflow.taskTemplates.filter { it.displayOrder!! > taskEntity.taskTemplate?.displayOrder!! }
                    .sortedBy { it.displayOrder }
            var expectedDueDate = taskEntity.expectedDueDate!!
            val tasks = mutableListOf<TaskEntity>()
            templates.forEach {
                expectedDueDate = TimeUtil.getNthWorkingDay(expectedDueDate, it.expectedTimeline)
                it.task!!.expectedDueDate = expectedDueDate
                tasks.add(it.task!!)
            }
            taskRepository.saveAll(tasks)
        }
    }

    @Transactional(readOnly = true)
    fun listTasks(
        searchFilter: TaskSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<TaskDetails> {

        searchFilter.currentUserPartnerId = authenticatedUser.partnerId ?: -1
        if (searchFilter.taskView == "CORPORATE" && AdminAccessUtil.isExpert(authenticatedUser.role)) {
            filterCorporateTasksForExperts(searchFilter)
        }

        val tasks = super.list(searchFilter, authenticatedUser, pageRequest, taskRepository, "TASK")

        return toTaskDetails(tasks)
    }

    private fun filterCorporateTasksForExperts(searchFilter: TaskSearchFilter) {
        val caseIds = getExpertsCaseIds(searchFilter.currentUser!!)
        searchFilter.expertAssignedCases = caseIds
    }

    private fun getExpertsCaseIds(userId: Long): List<Long> {
        return caseRepository.findAllByAssigneeId(userId).map { it.id }
    }

    fun toTaskDetails(tasks: Page<TaskEntity>): PagedResult<TaskDetails> {

        val taskDetailsList = getTaskDetailsList(tasks.toList())

        return PagedResult.ModelMapper.from(tasks, taskDetailsList)
    }

    private fun getTaskDetailsList(tasks: List<TaskEntity>) : List<TaskDetails>{
        val taskDetails = tasks.map {
            var overDueDays: Long = ChronoUnit.DAYS.between(it.expectedDueDate?:it.dueDate, LocalDateTime.now())

            if(overDueDays<=0)
                overDueDays = 0

            var categories = mutableListOf<String>()

            if(it.referenceType == ReferenceType.CASE || it.referenceType == ReferenceType.WORKFLOW){
                val case = it.referenceId?.let { it1 -> caseRepository.findById(it1) }
                val category = case?.getOrNull()?.category
                if (category != null) {
                    categories = mutableListOf(category.subCategoryId)
                }
            } else if(it.referenceType == ReferenceType.QUERY){
                val qCategories = queryCategoryRepository.findByQueryId(it.referenceId)
                if (qCategories != null) {
                    categories=qCategories.map { category->category.name }.toMutableList()
                }
            } else if(it.referenceType == ReferenceType.RFP) {
                val rfp = it.referenceId?.let { it1 -> rfpRepository.findById(it1) }
                val listCategories = rfp?.getOrNull()?.serviceDetails?.map { it2 -> it2.serviceName }
                if(listCategories != null)
                    categories = listCategories.toMutableList()
            }
            var isActive = false

            if(it.taskTemplate!=null && it.status!=TaskStatus.COMPLETED) {
                isActive = isPreviousTaskComplete(it.taskTemplate!!)
            }

            TaskDetails(
                id=it.id!!,
                name = it.name,
                description = it.description,
                createdBy = it.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
                referenceType = it.referenceType,
                referenceId = it.referenceId,
                visibility = it.visibility,
                dueDate = it.dueDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                overDueDays = overDueDays,
                completedDate = it.completedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                priority = it.priority,
                status = it.status,
                category = categories,
                assignee = it.assignee?.map { it2 -> TaskAssigneeDto(it2.type, it2.assigneeId!!.id!!, userProfileUtil.retrieveProfile(it2.assigneeId!!.id!!), it2.companyId) }?.toMutableList(),
                createdAt = TimeUtil.toEpochMillis(it.createdDate),
                reminders = it.reminders?.map { reminder->TaskReminderDto(reminder.dateTime?.let { it1 -> TimeUtil.toEpochMillis(it1) }) },
                usefulLinks = it.usefulLinks,
                instruction = it.instruction,
                progress = it.progress,
                caseMilestone = it.caseMilestone,
                plannedStartDate = it.plannedStartDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                startDate = it.startDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                expectedDueDate = it.expectedDueDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                isActive = isActive

            )
        }

        return taskDetails.toList()
    }

    private fun isPreviousTaskComplete(taskTemplate: TaskTemplateEntity): Boolean {

        val workflow = taskTemplate.workflow!!
        val templates = workflow.taskTemplates.sortedBy { it.displayOrder }

        if (templates[0].displayOrder == taskTemplate.displayOrder) {
            return true
        }

        val previousTaskTemplate = templates.first { it.displayOrder == taskTemplate.displayOrder?.minus(1) }
        return previousTaskTemplate.task!!.status == TaskStatus.COMPLETED
    }

    @Transactional(readOnly = true)
    fun listTasksCount(searchFilter: TaskSearchFilter, authenticatedUser: AuthenticatedUser): List<TaskCountResponse>? {
        val result = if (authenticatedUser.role == Role.ROLE_CORPORATE.name) {
            searchFilter.reportees = super.getReportees(authenticatedUser, "TASK")
            taskRepository.getStatusCountForCorporate(searchFilter)
        }
        else {
            taskRepository.getStatusCount(searchFilter)
        }
        val tasks = result.map {
            TaskCountDto(
                dt = it.getDt(),
                status = it.getStatus(),
                count = it.getCount(),
            )
        }

        val groupByMap = tasks.groupBy { it.dt }

        val taskResponseList = groupByMap.flatMap { entry ->
            val date = entry.key
            val taskCountResponses = entry.value

            var inProgressCount = 0L
            var notStartedCount =  0L
            var completedCount = 0L

            for(dto in taskCountResponses){
                if(dto.status.toString() == "IN_PROGRESS")
                    inProgressCount = dto.count!!.plus(inProgressCount)

                else if(dto.status.toString() == "NOT_STARTED")
                    notStartedCount = dto.count!!.plus(notStartedCount)

                else if(dto.status.toString() == "COMPLETED")
                    completedCount = dto.count!!.plus(completedCount)
            }


            listOf(TaskCountResponse(
                date?.toLocalDate(), completedCount,
                inProgressCount, notStartedCount))
        }

        return taskResponseList
    }

    @Transactional(readOnly = true)
    fun getTaskDetails(
        searchFilter: TaskSearchFilter,
        authenticatedUser: AuthenticatedUser
    ): List<TaskDetails>? {

        val tasks = if (authenticatedUser.role == Role.ROLE_CORPORATE.name) {
            searchFilter.reportees = super.getReportees(authenticatedUser, "TASK")
            if(searchFilter.reportees?.isEmpty() == true) {
                searchFilter.reportees = null
            }
            taskRepository.searchByDueDateCriteriaForCorporate(searchFilter)
        }
        else {
            taskRepository.searchByDueDateCriteria(searchFilter)
        }
        return getTaskDetailsList(tasks)
    }

    fun getCompanyIdAndType(authenticatedUser: AuthenticatedUser): Pair<Long, TaskCompanyType> {
        val user = loginAccountRepository.findById(authenticatedUser.userId).get()
        return if (AdminAccessUtil.isPartner(user) && authenticatedUser.partnerId!=null) {
            Pair(authenticatedUser.partnerId!!, TaskCompanyType.PARTNER)
        } else if (AdminAccessUtil.isAdmin(authenticatedUser)) {
            Pair(0, TaskCompanyType.CG)
        } else if (AdminAccessUtil.isExpert(authenticatedUser.role)) {
            Pair(authenticatedUser.companyId!!, TaskCompanyType.EXPERT)
        } else if (authenticatedUser.companyId != null) {
            Pair(authenticatedUser.companyId!!, TaskCompanyType.CORPORATE)
        } else {
            throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }

    }

    fun delete(taskId: Long, authenticatedUser: AuthenticatedUser): Boolean? {

        val task = try {
            getTask(taskId, authenticatedUser)
        }
        catch (ex: ApplicationException) {
            if (ex.error.errorCode != ErrorCode.NOT_FOUND.errorCode) {
                throw ex
            } else {
                null
            }
        }
        var canDelete = task!=null && task.status == TaskStatus.NOT_STARTED && authenticatedUser.userId == task.createdBy

        if (authenticatedUser.role == Role.ROLE_SUPER_ADMIN.name) {
            canDelete = true
        } else if (task!=null && authenticatedUser.role == Role.ROLE_PARTNER.name) {
            canDelete = true
        }

        if (canDelete) {
            taskRepository.deleteById(taskId)
            return true
        }
        else {
            throw ApplicationException(ErrorCode.CAN_NOT_DELETE_TASK)
        }

    }
    fun listPendingTasks(authenticatedUser: AuthenticatedUser, userId: Long?, accountId: Long?, referenceId: Long? = null): List<TaskDetails> {

        val companyIdType = getCompanyIdAndType(authenticatedUser)

        val searchFilter = TaskSearchFilter.Builder.build(
            null, null, null, referenceId, listOf(TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS)
            , authenticatedUser.userId, TaskVisibility.PUBLIC, TaskView.ALL_TASK.name, null,
            null, null, userId, companyIdType.first, companyIdType.second
        )

        if (accountId != null) {
            val corporate = corporateRepository.getReferenceById(authenticatedUser.companyId!!)
            searchFilter.accountUsers =
                accountRepository.findByIdAndCorporate(accountId, corporate)?.corporateUsers?.map { it.id!! }
        }

        return listTasks(
            searchFilter,
            PageRequest.of(0, 5, SearchConstant.SORT_ORDER("dueDate", "ASC")),
            authenticatedUser
        ).rows

    }
}