package com.centuroglobal.service

import com.centuroglobal.shared.data.pojo.RecaptchaResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class RecaptchaService(
    @Value("\${app.recaptcha.secretKey}")
    private val secretKey: String,
    private val restTemplate: RestTemplate
) {
    fun verifyRecaptcha(response: String): Boolean {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_FORM_URLENCODED

        val entity = HttpEntity("secret=$secretKey&response=$response", headers)

        val url = "https://www.google.com/recaptcha/api/siteverify"
        val verificationResponse = restTemplate.exchange(
            url,
            HttpMethod.POST,
            entity,
            RecaptchaResponse::class.java
        ).body
        return verificationResponse!!.success
    }
}
