package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.ValidationState
import com.centuroglobal.shared.data.payload.account.signup.OnBoardingProceedRequest
import com.centuroglobal.shared.data.payload.account.signup.OnBoardingRequest
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.StringUtil
import mu.KotlinLogging
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}

@Service
class OnBoardingService(
    private val tokenVerificationService: TokenVerificationService,
    private val validationTokenRepository: ValidationTokenRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateRepository: CorporateRepository,
    private val onBoardingRepository: OnBoardingRepository,
    private val passwordService: PasswordService,
    private val countryService: CountryService,
    private val partnerRepository: PartnerRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository
) {
    @Transactional
    fun onBoarding(onBoardingRequest: OnBoardingRequest): TokenResult? {
        val validationToken = tokenVerificationService.getValidTokenEntity(onBoardingRequest.code)
        val verifiedLoginAccount = verifyEntity(validationToken, onBoardingRequest)
        return tokenVerificationService.generateLoginToken(verifiedLoginAccount)
    }

    private fun verifyEntity(entity: ValidationTokenEntity, request: OnBoardingRequest): LoginAccountEntity {
        val loginAccount = loginAccountRepository.findByIdOrNull(entity.userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        entity.state = ValidationState.VERIFIED
        validationTokenRepository.save(entity)

        loginAccount.firstName = request.firstName.trim()
        loginAccount.lastName = request.lastName.trim()
        loginAccount.status = AccountStatus.ACTIVE
        loginAccount.lastUpdatedBy = entity.userId
        loginAccount.password = passwordService.validateAndEncodePassword(request.password)
        loginAccount.passwordCreationDate = LocalDateTime.now()
        loginAccount.joinedDate = LocalDateTime.now()

        when (AdminAccessUtil.getUserType(loginAccount)) {
            UserType.CORPORATE -> {
                loginAccount as CorporateUserEntity
//                loginAccount.lastTermsViewDate = LocalDateTime.now()
                loginAccount.jobTitle = request.jobTitle.trim()
                loginAccount.corporate.lastUpdatedBy = entity.userId
                loginAccount.corporate.name = StringUtil.trimSpaceIfNullOrBlankThrows(request.corporateName)
                loginAccount.corporate.status = CorporateStatus.ACTIVE
                try {
                    corporateRepository.saveAndFlush(loginAccount.corporate)
                    // create Stripe customer object and persist in stripe_account
//                    stripeService.createCustomer(loginAccount.id!!, loginAccount.email)
                } catch (ex: Exception) {
                    when (ex) {
                        is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.CORPORATE_ALREADY_EXISTS)
                        is ApplicationException -> throw ex
                        else -> {
                            log.error("Unexpected Exception", ex)
                            throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
                        }
                    }
                }
            }
            UserType.EXPERT -> {
                loginAccount as ExpertUserEntity
                loginAccount.jobTitle = request.jobTitle.trim()
                loginAccount.displayName = "${request.firstName} ${request.lastName}".trim()
                loginAccount.companyProfile!!.name = StringUtil.trimSpaceIfNullOrBlankThrows(request.companyName)
                loginAccount.companyProfile!!.lastUpdatedBy = entity.userId
                loginAccount.companyProfile!!.status = AccountStatus.ACTIVE
                try {
                    loginAccount.countryCode = countryService.retrieveByCountryCode(
                        StringUtil.trimSpaceIfNullOrBlankThrows(request.countryCode)).code
                } catch (ex: Exception) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                }
                expertCompanyProfileRepository.save(loginAccount.companyProfile!!)
            }
            UserType.BACKOFFICE -> {
                // nothing to update separately
            }
            UserType.PARTNER -> {

//                loginAccount.lastTermsViewDate = LocalDateTime.now()
                loginAccount.partnerJobTitle = request.jobTitle.trim()
                loginAccount.partner?.status = CorporateStatus.ACTIVE
                try {
                    partnerRepository.saveAndFlush(loginAccount.partner!!)
                    if (loginAccount is ExpertUserEntity) {
                        loginAccount.companyProfile?.status = AccountStatus.ACTIVE
                        loginAccount.companyProfile?.let { expertCompanyProfileRepository.save(it) }
                    }
                } catch (ex: Exception) {
                    when (ex) {
                        is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.CORPORATE_ALREADY_EXISTS)
                        is ApplicationException -> throw ex
                        else -> {
                            log.error("Unexpected Exception", ex)
                            throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
                        }
                    }
                }

            }
            else -> throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }
        return loginAccount
    }

    fun onBoardingProceed(onBoardingProceedRequest: OnBoardingProceedRequest, authenticatedUser: AuthenticatedUser) {
        var onBoardingEntity: OnBoardingEntity? = onBoardingRepository.findFirstByRootUserId(authenticatedUser.userId)

        if (onBoardingEntity != null) {
            onBoardingEntity.challenge = onBoardingProceedRequest.challenge
            onBoardingEntity.userType = onBoardingProceedRequest.userType
            onBoardingEntity.companySize = onBoardingProceedRequest.companySize
            onBoardingEntity.countryCode = onBoardingProceedRequest.countryCode
            onBoardingEntity.expansion = onBoardingProceedRequest.expansion
            onBoardingEntity.market = onBoardingProceedRequest.market
            onBoardingEntity.organization = onBoardingProceedRequest.organization
            onBoardingEntity.stage = onBoardingProceedRequest.stage
            onBoardingEntity.lastUpdatedBy = authenticatedUser.userId
            onBoardingEntity.platformUser = onBoardingProceedRequest.platformUser
        } else {
            onBoardingEntity = OnBoardingEntity(
                userType = onBoardingProceedRequest.userType,
                challenge = onBoardingProceedRequest.challenge,
                companySize = onBoardingProceedRequest.companySize,
                countryCode = onBoardingProceedRequest.countryCode,
                expansion = onBoardingProceedRequest.expansion,
                market = onBoardingProceedRequest.market,
                rootUserId = authenticatedUser.userId,
                lastUpdatedBy = authenticatedUser.userId,
                organization = onBoardingProceedRequest.organization,
                platformUser = onBoardingProceedRequest.platformUser,
                stage = onBoardingProceedRequest.stage
            )
        }
        onBoardingRepository.save(onBoardingEntity)

        loginAccountRepository.findById(authenticatedUser.userId).ifPresent {
            run {
                it.onboard = true
                log.info("saving changes for the onboarding to true")
                loginAccountRepository.saveAndFlush(it)
            }
        }
        Thread.sleep(1000)

        val loginAccount = loginAccountRepository.findByIdOrNull(authenticatedUser.userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        log.info("loginAccount onboard = " + loginAccount.onboard)

        if (!onBoardingProceedRequest.regJobTitle.isNullOrEmpty() && loginAccount is CorporateUserEntity) {
            loginAccount.jobTitle = onBoardingProceedRequest.regJobTitle?.trim() ?: ""
            loginAccount.status = AccountStatus.ACTIVE
            corporateUserRepository.save(loginAccount)
        }
        if (!onBoardingProceedRequest.regCompanyName.isNullOrBlank()) {
            val corporateEntity: CorporateEntity? = corporateRepository.findTopByRootUserId(authenticatedUser.userId)
            if (corporateEntity != null) {
                corporateEntity.name = onBoardingProceedRequest.regCompanyName.trim()
                corporateEntity.countryCode = onBoardingProceedRequest.regCountryCode ?: "US"
                corporateEntity.status = CorporateStatus.ACTIVE
                corporateRepository.saveAndFlush(corporateEntity)
            }
        }
    }
}