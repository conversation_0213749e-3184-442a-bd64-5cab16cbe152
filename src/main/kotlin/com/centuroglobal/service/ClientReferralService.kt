package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.ClientReferralEntity
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.referral.ClientReferralCreateRequest
import com.centuroglobal.shared.data.pojo.referral.ClientReferralDetails
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSearchFilter
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSummary
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.ClientReferralRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}
private const val UNEXPECTED_EXCEPTION = "Unexpected Exception"

@Service
class ClientReferralService(
    private val clientReferralRepository: ClientReferralRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val expertUserService: ExpertUserService,
    private val countryService: CountryService
) {

    @Transactional
    fun createReferral(clientReferralCreateRequest: ClientReferralCreateRequest, authenticatedUser: AuthenticatedUser)
            : ClientReferralDetails {
        val clientReferralEntity = ClientReferralEntity(
            title = clientReferralCreateRequest.title,
            clientName = clientReferralCreateRequest.clientName,
            clientCountryCode = clientReferralCreateRequest.clientCountryCode,
            clientEmail = clientReferralCreateRequest.clientEmail,
            clientCompanyName = clientReferralCreateRequest.clientCompanyName,
            clientContactNumber = clientReferralCreateRequest.clientContactNumber,
            description = clientReferralCreateRequest.description,
            dealCurrency = clientReferralCreateRequest.dealCurrency,
            dealValue = clientReferralCreateRequest.dealValue,
            createdBy = authenticatedUser.userId,
            lastUpdatedBy = authenticatedUser.userId
        )
        clientReferralEntity.experts = clientReferralCreateRequest.experts.map {
            expertUserRepository.findByIdOrNull(it) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        }.toMutableList()

        /*val userType = UserType.valueOf(authenticatedUser.userType)
        //If expert is creating this referral add him/her by default.
        if(userType == UserType.EXPERT){
            var exist = false
            for (expert in clientReferralEntity.experts) {
                if(expert.id == authenticatedUser.userId){
                    exist = true
                    break
                }
            }
            if(!exist)
                clientReferralEntity.experts.add(expertUserRepository.getById(authenticatedUser.userId))
        }*/

        val savedEntity = clientReferralRepository.save(clientReferralEntity)
        return getReferralDetails(savedEntity.id, authenticatedUser)
    }

    @Transactional
    fun updateReferral(
        clientReferralId: Long,
        clientReferralCreateRequest: ClientReferralCreateRequest,
        authenticatedUser: AuthenticatedUser
    )
            : ClientReferralDetails {
        val clientReferralEntity =
            clientReferralRepository.findByIdOrNull(clientReferralId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val userType = UserType.valueOf(authenticatedUser.userType)
        if (userType != UserType.BACKOFFICE) {
            if (userType == UserType.EXPERT) {
                if (clientReferralEntity.createdBy != authenticatedUser.userId) {
                    throw ApplicationException(ErrorCode.UNAUTHORIZED)
                }
            } else throw ApplicationException(ErrorCode.UNAUTHORIZED)
        }
        clientReferralEntity.title = clientReferralCreateRequest.title
        clientReferralEntity.clientName = clientReferralCreateRequest.clientName
        clientReferralEntity.clientCountryCode = clientReferralCreateRequest.clientCountryCode
        clientReferralEntity.clientEmail = clientReferralCreateRequest.clientEmail
        clientReferralEntity.clientCompanyName = clientReferralCreateRequest.clientCompanyName
        clientReferralEntity.clientContactNumber = clientReferralCreateRequest.clientContactNumber
        clientReferralEntity.description = clientReferralCreateRequest.description
        clientReferralEntity.dealCurrency = clientReferralCreateRequest.dealCurrency
        clientReferralEntity.dealValue = clientReferralCreateRequest.dealValue
        clientReferralEntity.lastUpdatedBy = authenticatedUser.userId

        clientReferralEntity.experts = clientReferralCreateRequest.experts.map {
            expertUserRepository.findByIdOrNull(it) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        }.toMutableList()
        clientReferralRepository.save(clientReferralEntity)
        return getReferralDetails(clientReferralEntity.id, authenticatedUser)
    }

    @Transactional
    fun delete(clientReferralId: Long, userId: Long): String {
        try {
            val clientReferralEntity = clientReferralRepository.findByIdOrNull(clientReferralId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            clientReferralEntity.status = LeadStatus.DELETED
            clientReferralEntity.lastUpdatedBy = userId

            clientReferralRepository.save(clientReferralEntity)
            return AppConstant.SUCCESS_RESPONSE_STRING

        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_DELETE_FAIL)
                }
            }
        }
    }

    @Transactional
    fun getReferralDetails(
        clientReferralId: Long?,
        authenticatedUser: AuthenticatedUser,
        read: Boolean = false
    ): ClientReferralDetails {
        val clientReferralEntity =
            clientReferralRepository.findByIdOrNull(clientReferralId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val userType = UserType.valueOf(authenticatedUser.userType)
        if (userType != UserType.BACKOFFICE) {
            if (userType == UserType.EXPERT) {
                if (clientReferralEntity.createdBy != authenticatedUser.userId && read) {
                    val exist = clientReferralEntity.readBy.map { it.id }.contains(authenticatedUser.userId)
                    if (!exist) {
                        clientReferralEntity.readBy.add(expertUserRepository.getReferenceById(authenticatedUser.userId))
                        clientReferralRepository.save(clientReferralEntity)
                    }
                }
            } else throw ApplicationException(ErrorCode.UNAUTHORIZED)
        }
        val clientReferralDetails = ClientReferralDetails.ModelMapper.from(clientReferralEntity)
        clientReferralDetails.createdBy =
            expertUserService.retrieveProfileSummary(clientReferralEntity.createdBy).displayName
        clientReferralDetails.experts =
            expertUserService.retrieveActiveExpertsSummaryFromIdIn(ExpertSearchFilter(), Pageable.unpaged(),
                clientReferralEntity.experts.map { it.id ?: 0 }).rows.map { it.displayName }
        clientReferralDetails.readBy =
            expertUserService.retrieveActiveExpertsSummaryFromIdIn(ExpertSearchFilter(), Pageable.unpaged(),
                clientReferralEntity.readBy.map { it.id ?: 0 }).rows.map { it.displayName }
        return clientReferralDetails
    }

    @Transactional
    fun listingForExpert(
        filter: ClientReferralSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<ClientReferralDetails> {
        val referrals = clientReferralRepository.searchByCriteriaForExpert(
            authenticatedUser.userId, arrayOf(
                LeadStatus.ACTIVE,
                LeadStatus.RESOLVED,
                LeadStatus.UNRESOLVED
            ),
            filter, pageRequest
        )
        return PagedResult.ModelMapper.from(
            referrals,
            referrals.map { getReferralDetails(it.id, authenticatedUser) }.toList()
        )
    }

    @Transactional(readOnly = true)
    fun search(
        filter: ClientReferralSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<ClientReferralSummary>? {
        val referrals = clientReferralRepository.searchByCriteria(
            arrayOf(
                LeadStatus.ACTIVE,
                LeadStatus.RESOLVED,
                LeadStatus.UNRESOLVED
            ),
            filter, pageRequest
        )
        return PagedResult.ModelMapper.from(referrals, referrals.map {
            val referredTo = if (it.experts.size == 1) {
                "${it.experts[0].firstName} ${it.experts[0].lastName}"
            } else {
                "${it.experts.size} Experts"
            }
            ClientReferralSummary(
                id = it.id,
                companyName = it.clientCompanyName,
                summary = it.description,
                clientLocation = countryService.retrieveByCountryCode(it.clientCountryCode).name,
                createdOn = TimeUtil.toEpochMillis(it.createdDate),
                from = expertUserRepository.findByIdOrNull(it.createdBy)?.displayName ?: "",
                referredTo = referredTo
            )
        }.toList())
    }

    @Transactional(readOnly = true)
    fun referralMemberSearch(
        clientReferralId: Long, filter: ExpertSearchFilter,
        pageable: Pageable,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<ExpertProfileSummary> {
        val clientReferralEntity = clientReferralRepository.findByIdOrNull(clientReferralId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val userIds = clientReferralEntity.experts.map { it.id ?: 0 }.toList()
        return expertUserService.retrieveActiveExpertsSummaryFromIdIn(filter, pageable, userIds)
    }

}