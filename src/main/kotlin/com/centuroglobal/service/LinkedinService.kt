package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.service.CorporateService
import com.fasterxml.jackson.databind.JsonNode
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.client.RestTemplate


private val log = KotlinLogging.logger {}

@Service
class LinkedinService(
    @Value("\${linkedin.client-id}")
    private val clientId: String,
    @Value("\${linkedin.client-secret}")
    private val clientSecret: String,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateService: CorporateService,
    private val corporateUserRepository: CorporateUserRepository
) {

    @Transactional
    fun auth(code: String, redirectURL: String, keepMeInformed: String?): LoginAccountEntity? {
        try {
            log.info("keepMeInformed flag value $keepMeInformed")
            val restTemplate = RestTemplate()
            val params: Map<String, String> = mapOf(
                "grant_type" to "authorization_code",
                "redirect_uri" to redirectURL,
                "client_id" to clientId,
                "client_secret" to clientSecret,
                "code" to code
            )
            val jsonNode: JsonNode = restTemplate.getForObject(
                "https://www.linkedin.com/oauth/v2/accessToken?grant_type={grant_type}&redirect_uri={redirect_uri}&client_id={client_id}&client_secret={client_secret}&code={code}",
                JsonNode::class.java,
                params
            )
                ?: throw ApplicationException(ErrorCode.LOGIN_FAIL)
            val accessToken: String = jsonNode.get("access_token").asText("")
            val email: String? = getEmail(accessToken)
            var loginAccountEntity: LoginAccountEntity? = email?.let { loginAccountRepository.findByEmail(it) }
            if (loginAccountEntity != null) {
                if (loginAccountEntity.getUserType() != UserType.CORPORATE) {
                    throw ApplicationException(ErrorCode.LINKEDIN_NOT_SUPPORTED)
                }
                loginAccountEntity.isLinkedin = true
                loginAccountEntity = loginAccountRepository.save(loginAccountEntity)
            } else {
                //Signup
                val headers = HttpHeaders()
                headers.set("Authorization", "Bearer $accessToken")
                val entity:HttpEntity<Any> = HttpEntity(headers)
                val responseEntity: ResponseEntity<JsonNode> = restTemplate.exchange("https://api.linkedin.com/v2/me", HttpMethod.GET,entity,JsonNode::class.java)
                val jsonNode = responseEntity.body
                val firstName: String = jsonNode?.get("localizedFirstName")?.asText()?:""
                val lastName: String = jsonNode?.get("localizedLastName")?.asText()?:""
                val userEmail: String = email?:throw ApplicationException(ErrorCode.LOGIN_FAIL)
                val countryCode: String = (jsonNode?.get("localizedLastName")?.get("country")?.asText())?:"US"
                val corporateId: Long = corporateService.createCorporate(SignUpRequest(userEmail,firstName,lastName,
                    null,"",countryCode,null, keepMeInformed?.let { it.toBoolean() }?: false),false)
                loginAccountEntity = corporateUserRepository.findFirstByCorporateId(corporateId)?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
                loginAccountEntity.isLinkedin = true
                loginAccountEntity = loginAccountRepository.save(loginAccountEntity)
            }
            return loginAccountEntity
        } catch (e: Exception) {
            log.warn("Exception in signup by linkedin", e)
            e.printStackTrace()
            throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
        }
    }

    private fun getEmail(accessToken: String): String? {
        val emailUrl = "https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"
        val restTemplate = RestTemplate()
        val headers = HttpHeaders()
        headers.set("Authorization", "Bearer $accessToken")
        val entity: HttpEntity<Any> = HttpEntity(headers)
        val responseEntity: ResponseEntity<JsonNode> =
            restTemplate.exchange(emailUrl, HttpMethod.GET, entity, JsonNode::class.java)
        val jsonNode = responseEntity.body
        val email: String? = jsonNode?.get("elements")?.get(0)?.get("handle~")?.get("emailAddress")?.asText()
        return email
    }
}