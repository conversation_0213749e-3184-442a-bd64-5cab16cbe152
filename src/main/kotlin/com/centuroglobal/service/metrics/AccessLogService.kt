package com.centuroglobal.service.metrics

import com.centuroglobal.shared.data.entity.AccessLogEntity
import com.centuroglobal.shared.data.pojo.metrics.RequestMetadata
import com.centuroglobal.shared.repository.AccessLogRepository
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.concurrent.LinkedBlockingQueue

private val ACCESS_LOG_BLOCKING_QUEUE = LinkedBlockingQueue<AccessLogEntity>()

@Service
class AccessLogService(val accessLogRepository: AccessLogRepository) {

    fun addToQueue(metadata: RequestMetadata) {
        ACCESS_LOG_BLOCKING_QUEUE.offer(RequestMetadata.ModelMapper.toAccessLogEntity(metadata))
    }

    @Scheduled(initialDelayString = "10000", fixedDelayString = "5000")
    @Transactional
    fun persistAccessLogs() {
        val accessLogs: MutableList<AccessLogEntity> = mutableListOf()
        ACCESS_LOG_BLOCKING_QUEUE.drainTo(accessLogs)
        if (accessLogs.isNotEmpty()) {
            accessLogRepository.saveAll(accessLogs)
            accessLogRepository.flush()
        }
    }
}