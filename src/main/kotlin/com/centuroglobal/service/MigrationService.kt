package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.*
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.task.TaskTemplateRepository
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.dao.IncorrectResultSizeDataAccessException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

private val log = KotlinLogging.logger {}

@Service
class MigrationService(

    private val bandsRepository: BandsRepository,
    private val corporateRepository: CorporateRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val accountEntityRepository: AccountEntityRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val caseRepository: CaseRepository,
    private val caseStatusMasterRepository: CaseStatusMasterRepository,
    private val caseStatusHistoryRepository: CaseStatusHistoryRepository,
    private val caseManagerRepository: CaseManagerRepository,
    private val partnerRepository: PartnerRepository,
    private val groupChatRepository: GroupChatRepository,
    private val taskRepository: TaskRepository,

    private val taskTemplateRepository: TaskTemplateRepository,

    private val excelService: ExcelService,
    private val subscriptionService: SubscriptionService,
    private val groupChatService: GroupChatService,
    private val corporateUserService: CorporateUserService,
    private val taskWorkflowService: TaskWorkflowService
) {

    fun getCorporates(): List<CorporateEntity> {
        val corporates = corporateRepository.findAll()
        //Skip dummy user since bands are already present for him
        return corporates.filter { it.id!=-1L }
    }

    fun bandMigration(): Boolean {

        val corporates = getCorporates()
        val defaultBands = bandsRepository.findByCorporateId(-1)

        // Adding default bands and band_details for each corporate.
        for(corporate in corporates) {

            defaultBands!!.forEach {
                val bandEntity = BandsEntity(
                    name = it.name,
                    description = it.description,
                    status = it.status,
                    color = it.color,
                    corporate = corporate
                )
                val accesses = it.bandAccesses?.map { i->
                    BandDetailsEntity(
                        band = bandEntity,
                        access = i.access,
                        visibility = i.visibility
                    )
                }?.toMutableList()
                bandEntity.bandAccesses = accesses
                bandsRepository.save(bandEntity)
            }

            val user = loginAccountRepository.findById(corporate.rootUserId).get()
            val bandName = if(user.subscriptionType==SubscriptionType.PAID) "Super Admin" else "Super Admin (free)"
            try {
                val band = bandsRepository.findByNameAndCorporate(bandName, corporate)
                user as CorporateUserEntity
                user.band = band
                //save root user
                corporateUserRepository.save(user)
            } catch (e: IncorrectResultSizeDataAccessException) {
                // Skip adding band to user since its already present
            }
        }
        return true
    }

    fun corporateUserAccountMigration(): Boolean {

        val corporates = getCorporates()

        corporates.forEach {
            var accountEntity = AccountEntity(
                name = it.name,
                status = AccountStatus.ACTIVE,
                description = null,
                companyName = null,
                corporate = it
            )
            val createdAccount = accountEntityRepository.saveAndFlush(accountEntity)
            var rootUser = corporateUserRepository.findById(it.rootUserId).get()
            rootUser.accounts = setOf(createdAccount)
            corporateUserRepository.saveAndFlush(rootUser)
        }
        return true
    }
    fun expertCompanyAccountMigration(): Boolean {
        val companyProfiles = expertCompanyProfileRepository.findAll()
        companyProfiles
            .filter { it.account == null }
            .ifEmpty { return false }
            .forEach {
                val account = AccountEntity(
                    name = it.name,
                    status = AccountStatus.ACTIVE,
                    corporate = null
                )
                val newAccount = accountEntityRepository.saveAndFlush(account)
                it.account = newAccount
                expertCompanyProfileRepository.save(it)
            }
        return true
    }

    @Transactional
    fun caseAccountMigration(): Boolean {
        val cases = caseRepository.findByAccountIsNull()
        cases
            .forEach {
                log.info("running for case Id ${it.id}")
                val createdByUserId = it.createdBy!!.userId
                val account = when (it.createdBy!!.userType) {
                    UserType.CORPORATE -> {
                        corporateUserRepository.findById(createdByUserId).get().accounts!!.iterator().next()
                    }
                    UserType.EXPERT -> {
                        expertCompanyProfileRepository.findByUsers_Id(createdByUserId)!!.account
                    }
                    else -> {
                        null
                    }
                }
                if(account!=null) {
                    it.account = account
                    caseRepository.save(it)
                }
            }
        return true
    }



    fun caseActionForMigration(): Boolean {
        val cases = caseRepository.findByActionForIsNull()
        cases.map {
            it.actionFor = caseStatusMasterRepository.findBySubCategoryAndStatus(it.category!!.subCategoryId, it.status).actionFor
        }
        caseRepository.saveAllAndFlush(cases)
        return true
    }

    fun caseHistoryActionForMigration(uploadedExcel: MultipartFile): Boolean? {

        val caseHistories = caseStatusHistoryRepository.findAll()

        val oldStatusMap = getOldStatusMap(uploadedExcel)
        caseHistories.map {
            it.actionFor = try {
                caseStatusMasterRepository.findBySubCategoryAndStatus(it.case.category!!.subCategoryId, it.status).actionFor
            } catch (ex: EmptyResultDataAccessException){
                val updatedStatus = oldStatusMap[it.case.category!!.subCategoryId]?.get(it.status)
                if (updatedStatus != null) {
                    caseStatusMasterRepository.findBySubCategoryAndStatus(it.case.category!!.subCategoryId, updatedStatus).actionFor
                }else{
                    it.actionFor
                }
            }
        }
        caseStatusHistoryRepository.saveAllAndFlush(caseHistories)

        return true
    }

    private fun getOldStatusMap(uploadedExcel: MultipartFile): Map<String, Map<String, String>> {
        val file = File.createTempFile("templateType", ExtentionType.XLSX.key)
        FileOutputStream(file).use { fileOutputStream -> fileOutputStream.write(uploadedExcel.bytes) }
        val oldStatusMap = mutableMapOf<String, Map<String, String>>()

        try {
            FileInputStream(file).use { excelFile ->
                try {
                    XSSFWorkbook(excelFile).use { workbook ->
                        for (i in 0 until workbook.numberOfSheets) {
                            val statusMap = mutableMapOf<String, String>()
                            val sheet = workbook.getSheetAt(i)
                            log.info("sheet processing " + sheet.sheetName)
                            val subCategory = sheet.sheetName
                            for (rowNo in 1..sheet.lastRowNum) {
                                val oldStatus = excelService.setValue(sheet, rowNo, 2).toString()
                                val newStatus = excelService.setValue(sheet, rowNo, 3).toString()
                                statusMap[oldStatus] = newStatus
                            }
                            oldStatusMap[subCategory] = statusMap
                        }
                    }
                } catch (e: IOException) {
                    throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
                }
                file.delete()
            }
        } catch (exp: Exception) {
            throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
        }
        return oldStatusMap
    }

    @Transactional
    fun subscriptionPlansMigration(planId: Long, from: Long, to: Long): Int {
        val corporates = corporateRepository.findAll()
        val filteredCorporates = corporates.filter { it.id!! > 0 }
        var count = 0
        filteredCorporates.forEach {
            val plans = subscriptionService.getPlans(it.id!!)
            if (plans.isEmpty()) {
                log.info("processing Corporate " + it.id)
                subscriptionService.createDefaultSubscription(planId, it.id!!, from, to)
                it.subscriptionStartDate= TimeUtil.fromInstantMillis(from)
                it.subscriptionEndDate=TimeUtil.fromInstantMillis(to)
                corporateRepository.save(it)
                count += 1
                log.info("processing Corporate " + it.id+" Completed")
            }
        }
        return count
    }

    @Async
    @Transactional
    fun migrateCaseGroupChat() {

        var count = 0L
        val cases = caseRepository.findAll()
        log.info("Total cases to migrate: ${cases.size}")
        cases.forEach {
            val chat = groupChatRepository.findByChatTypeAndReferenceId(ChatType.APPLICANT_CASE, it.id!!)
            if (chat == null) {
                createGroupChat(it)
                count += 1
            }
        }
        log.info("Migration complete. Processed total {} cases", count)
    }


    @Async
    @Transactional
    fun migarateCaseEmailsByCategory() {

        var listCaseCategories = listOf(
            "BANK_ACCOUNT",
            "OFFICE_SPACE",
            "COMPLEX_REQUIREMENT",
            "MANAGE_ASSIGNESS",
            "MANAGE_PAYROLL",
            "HR_EMP_SUPPORT",
            "INTELLECTUAL_PROPERTY",
            "DATA_PROTECTION",
            "COM_LAW_CONTRACTS",
            "EMP_BENEFITS_INSURANCE",
            "RISK_MANAGEMENT",
            "TAX_SUPPORT",
            "COMP_TAX_ASS"
        )

        var listCategoriesWithEmailAddress = listOf(
            "SINGLE_VISA",
            "INDVL_TAX_ASS",
            "RULING_30_PERCENT",
            "LEGALISATION"
        )

        var listCategoriesWithApplicantEmailAddress = listOf(
            "ENTRY_VISA",
            "POSTED_WORKER_NOTIFICATION",
            "REMOTE_WORK_ASSESSMENT",
            "TAX_RETURN_PREPARATION",
            "EXPATRIATE_TAX_SERVICES",
            "BUSINESS_VISA",
        )


        var count = 0L
        val cases = caseRepository.findAll()


        cases.filter {
            listCaseCategories.contains(it.category!!.subCategoryId) ||
            listCategoriesWithEmailAddress.contains(it.category!!.subCategoryId) ||
            listCategoriesWithApplicantEmailAddress.contains(it.category!!.subCategoryId)
        }.
        forEach {

            if (listCaseCategories.contains(it.category!!.subCategoryId)) {

                it.email = it.personalDetails?.email

            } else if (listCategoriesWithEmailAddress.contains(it.category!!.subCategoryId)) {

                when (it.category!!.subCategoryId) {

                    "SINGLE_VISA" -> {
                        val caseEntity: SingleVisaEntity = it as SingleVisaEntity
                        it.email = caseEntity.emailAddress
                    }

                    "INDVL_TAX_ASS" -> {
                        val caseEntity: IndividualTaxAssessmentEntity = it as IndividualTaxAssessmentEntity
                        it.email = caseEntity.emailAddress
                    }

                    "RULING_30_PERCENT" -> {
                        val caseEntity: Ruling30PercentEntity = it as Ruling30PercentEntity
                        it.email = caseEntity.emailAddress
                    }

                    "LEGALISATION" -> {
                        val caseEntity: LegalisationEntity = it as LegalisationEntity
                        it.email = caseEntity.emailAddress
                    }

                }

            } else if (listCategoriesWithApplicantEmailAddress.contains(it.category!!.subCategoryId)) {

                when (it.category!!.subCategoryId) {

                    "BUSINESS_VISA" -> {
                        val caseEntity: BusinessVisaEntity = it as BusinessVisaEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }

                    "EXPATRIATE_TAX_SERVICES" -> {
                        val caseEntity: ExpatriateTaxServicesEntity = it as ExpatriateTaxServicesEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }

                    "TAX_RETURN_PREPARATION" -> {
                        val caseEntity: TaxReturnPreparationEntity = it as TaxReturnPreparationEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }

                    "REMOTE_WORK_ASSESSMENT" -> {
                        val caseEntity: RemoteWorkAssessmentEntity = it as RemoteWorkAssessmentEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }

                    "POSTED_WORKER_NOTIFICATION" -> {
                        val caseEntity: PostedWorkerNotificationEntity = it as PostedWorkerNotificationEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }

                    "ENTRY_VISA" -> {
                        val caseEntity: EntryVisaEntity = it as EntryVisaEntity
                        it.email = caseEntity.applicant?.emailAddress
                    }
                }
            }

            caseRepository.save(it)
            count++

        }

        log.info("Migration complete. Processed total {} cases", count)
    }


    @Async
    @Transactional
    fun migrateTaskCompanyAndCreatedDetails() {

        val taskTemplates = taskTemplateRepository.findAll()
        var count = 0

        taskTemplates.filter { it.task != null }.forEach {
            count++
            updateTaskCompanyAndCreatedDetails(it)
        }

        log.info("Migration ran completed. Updated details of {} tasks", count)

    }

    private fun updateTaskCompanyAndCreatedDetails(taskTemplateEntity: TaskTemplateEntity) {

        val taskEntity = taskTemplateEntity.task

        if(taskEntity!!.assignee !=null && taskEntity.assignee!!.isNotEmpty()){

            val userId = taskEntity.assignee!!.first().assigneeId!!.id!!
            val idAndType = taskWorkflowService.getCompanyIdAndType(userId)

            taskEntity.companyId = idAndType.first
            taskEntity.companyType = idAndType.second
            taskEntity.createdBy = userId
            taskEntity.updatedBy = userId
            taskRepository.save(taskEntity)
        }

    }

    @Transactional
    private fun createGroupChat(case: CaseEntity) {

        val caseOwner = case.createdBy!!.userId
        val caseId = case.id!!
        // create applicant case group chat

        log.info("caseId: $caseId, caseOwner: $caseOwner")

        val applicantCaseParticipants = mutableListOf(caseOwner)
        applicantCaseParticipants.add(caseOwner)
        val caseManagersList = caseManagerRepository.findAllByCase(case)
        val caseManagers = caseManagersList.map { it.user.id!! }
        applicantCaseParticipants.addAll(caseManagers)
        val partnerEntity = case.partnerId?.let { partnerRepository.findByIdOrNull(it) }
        val virtualParticipants = groupChatService.getVirtualParticipants(partnerEntity)

        groupChatService.createGroupChatWithOwner(ChatType.APPLICANT_CASE, caseId, applicantCaseParticipants, virtualParticipants, caseOwner)

        // create client case group chat

        val clientCaseParticipants = mutableListOf<Long>()
        clientCaseParticipants.addAll(caseManagers)
        clientCaseParticipants.addAll(corporateUserService.getUserManagers(caseOwner))
        clientCaseParticipants.addAll(corporateUserService.getCorporateSuperAdmins(caseOwner))

        groupChatService.createGroupChatWithOwner(ChatType.CLIENT_CASE, caseId, clientCaseParticipants, virtualParticipants, caseOwner)
    }
}