package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.ConciergeNotificationEntity

import com.centuroglobal.shared.data.enums.ConciergeNotificationState

import com.centuroglobal.shared.data.payload.ConciergeRequest

import com.centuroglobal.shared.repository.ConciergeNotificationRepository

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class ConciergeService(


    private val conciergeNotificationRepository: ConciergeNotificationRepository,

) {
    @Transactional
    fun createNotification(request: ConciergeRequest, userId: Long): String {
        conciergeNotificationRepository.save(
            ConciergeNotificationEntity(null, userId, request.query, ConciergeNotificationState.CREATED)
        )

        return AppConstant.SUCCESS_RESPONSE_STRING
    }
}