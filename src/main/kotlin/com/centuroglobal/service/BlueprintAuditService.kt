package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.BlueprintAuditEntity
import com.centuroglobal.shared.data.enums.BlueprintActionStatus
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.BlueprintAuditRepository
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}

@Service
class BlueprintAuditService(
    private val blueprintAuditRepository: BlueprintAuditRepository
) {

    @Transactional
    fun log(countryCode: String, userId: Long, action: BlueprintActionStatus) {
        try {
            blueprintAuditRepository.save(
                BlueprintAuditEntity(
                    null,
                    countryCode,
                    action,
                    lastUpdatedBy = userId
                )
            )
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.BLUEPRINT_AUDIT_SAVE_FAIL)
                }
            }
        }
    }
}
