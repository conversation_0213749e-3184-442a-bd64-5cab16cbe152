package com.centuroglobal.service

import com.centuroglobal.data.payload.case.CaseFormContent
import com.centuroglobal.data.payload.case.CaseFormRequest
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CaseFormCountryEntity
import com.centuroglobal.shared.data.entity.CaseFormEntity
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseFormDetails
import com.centuroglobal.shared.data.pojo.case.CaseFormSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.PartnerRepository
import com.centuroglobal.shared.repository.case.CaseFormRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CaseFormService(

    private val caseFormRepository: CaseFormRepository,
    private val partnerRepository: PartnerRepository,
    private val userProfileUtil: UserProfileUtil,
    private val mapper: ObjectMapper,

): BaseService<CaseFormEntity, Long>() {

    @Transactional
    fun create(request: CaseFormRequest, authenticatedUser: AuthenticatedUser): Long {

        val caseForm = CaseFormEntity(
            name = request.name,
            description = request.description,
            category = request.category,
            fields = request.fields?.let { mapper.writeValueAsString(it) },
            visibility = request.visibility,
            status = request.status
        )

        caseForm.countries = request.countries.map { CaseFormCountryEntity(code = it, form = caseForm) }.toMutableList()

        authenticatedUser.partnerId?.let {
            caseForm.partner = partnerRepository.getReferenceById(it)
        }

        createDocuments(request.defaultDocumentList, caseForm)

        request.fieldCount?.let { caseForm.fieldCount = it }

        val savedEntity = caseFormRepository.save(caseForm)

        return savedEntity.id!!
    }

    fun get(id: Long, authenticatedUser: AuthenticatedUser): CaseFormDetails {
        val entity = getCaseFormEntityForView(id, authenticatedUser)
        return CaseFormDetails.ModelMapper.from(entity, entity.updatedBy?.let { userProfileUtil.retrieveProfile(it) })
    }

    fun list(
        filter: CaseFormSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<CaseFormDetails> {

        if (filter.isPartner == false && AdminAccessUtil.isPartner(authenticatedUser)) {
            filter.visibility = TaskVisibility.PUBLIC
        }

        val page = if (AdminAccessUtil.isExpert(authenticatedUser.role)) {
            filter.visibility = TaskVisibility.PUBLIC
            caseFormRepository.searchByCriteria(filter, emptyList(), pageRequest)
        }
        else {
            super.list(filter, authenticatedUser, pageRequest, caseFormRepository, "CASE_FORM")
        }
        val pageDetails = page.map {
            CaseFormDetails.ModelMapper.formListing(it, it.updatedBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) })
        }
        return PagedResult.ModelMapper.from(page, pageDetails.toList())
    }

    @Transactional
    fun update(id: Long, request: CaseFormRequest, authenticatedUser: AuthenticatedUser): Boolean {

        val entity = getCaseFormEntity(id, authenticatedUser)

        entity.name = request.name
        entity.description = request.description
        entity.category = request.category
        entity.visibility = request.visibility
        entity.status = request.status

        val current = entity.countries
        val updated = request.countries

        val toDelete = mutableListOf<CaseFormCountryEntity>()

        //prepare remove list
        val itr = current.iterator()
        while (itr.hasNext()) {
            val country = itr.next()
            if (country.code !in updated) {
                itr.remove()
                toDelete.add(country)
            }
        }
        // add new countries
        for(country in updated) {
            val existing = current.find { it.code == country }
            if (existing == null) {
                current.add(CaseFormCountryEntity(code = country, form = entity))
            }
        }
        if (toDelete.isNotEmpty()) {
            current.removeAll(toDelete)
        }

        caseFormRepository.save(entity)

        return true
    }

    @Transactional
    fun copy(id: Long, authenticatedUser: AuthenticatedUser): Long {

        val entity = getCaseFormEntityForView(id, authenticatedUser)

        val request = CaseFormRequest(
            "Copy-${entity.name}",
            entity.description,
            entity.countries.map { it.code },
            entity.category,
            entity.fields?.let { mapper.readTree(it) },
            entity.visibility,
            entity.status,
            entity.fieldCount,
            entity.defaultDocuments?.split(",")
        )
        return create(request, authenticatedUser)
    }

    fun delete(id: Long, authenticatedUser: AuthenticatedUser): Boolean {
        val entity = getCaseFormEntity(id, authenticatedUser)
        caseFormRepository.delete(entity)
        return true
    }

    fun updateStatus(id: Long, status: CaseFormStatus, authenticatedUser: AuthenticatedUser): Boolean? {
        val entity = getCaseFormEntity(id, authenticatedUser)
        entity.status = status
        caseFormRepository.save(entity)
        return true
    }

    fun updateContent(id: Long, request: CaseFormContent, authenticatedUser: AuthenticatedUser): Boolean? {
        val entity = getCaseFormEntity(id, authenticatedUser)
        entity.fields = mapper.writeValueAsString(request.content)

        entity.mappingFields = request.mapping
        entity.fieldCount = request.fieldCount

        createDocuments(request.defaultDocumentList, entity)

        caseFormRepository.save(entity)
        return true
    }

    private fun getCaseFormEntity(
        id: Long,
        authenticatedUser: AuthenticatedUser
    ): CaseFormEntity {
        val entity = super.get(id, authenticatedUser, caseFormRepository, "CASE_FORM")
        return entity
    }
    private fun getCaseFormEntityForView(
        id: Long,
        authenticatedUser: AuthenticatedUser
    ): CaseFormEntity {
        return caseFormRepository.getFormForView(id, authenticatedUser.partnerId) ?: throw ApplicationException(
            ErrorCode.NOT_FOUND
        )
    }
    private fun createDocuments(
        requiredDocumentList: List<String>?,
        entity: CaseFormEntity
    ) {
        requiredDocumentList?.let {
            entity.defaultDocuments = it.joinToString(",")
        }
    }
}
