package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.BlueprintStepExpertEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.BlueprintActionStatus
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.StepExpertRequest
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.BlueprintStepExpertRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.service.ExpertUserService
import mu.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}

@Service
class BlueprintExpertService(
    private val blueprintStepExpertRepository: BlueprintStepExpertRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val countryService: CountryService,
    private val expertUserService: ExpertUserService,
    private val blueprintAuditService: BlueprintAuditService
) {
    @Transactional(readOnly = true)
    fun retrieveStepExpertList(countryCode: String): MutableMap<StepName, List<ExpertProfileSummary>> {
        val country = countryService.retrieveByCountryCode(countryCode)

        val stepExpertIdList: MutableMap<StepName, List<ExpertProfileSummary>> = mutableMapOf()
        blueprintStepExpertRepository.findAllByCountryCode(country.code).groupBy { it.stepName }
            .forEach { (k, v) -> stepExpertIdList[k] = v.map { expertUserService.retrieveProfileSummary(it.expertId) } }

        return stepExpertIdList
    }

    @Transactional
    fun updateExperts(
        countryCode: String,
        stepName: StepName,
        userId: Long,
        request: StepExpertRequest
    ): List<ExpertProfileSummary> {
        try {
            val country = countryService.retrieveByCountryCode(countryCode)

            // Delete all existing experts for this blueprint step
            val existingExpertList =
                blueprintStepExpertRepository.findAllByCountryCodeAndStepName(country.code, stepName).map { it.id!! }
            if (existingExpertList.isNotEmpty()) {
                blueprintStepExpertRepository.deleteByIdIn(existingExpertList)
            }

            request.expertIds.distinct().forEach {
                // Verify userId exists and is expert
                val existingExpertUser = expertUserRepository.findByIdOrNull(it)
                    ?: throw ApplicationException(ErrorCode.BLUEPRINT_EXPERT_TYPE_FAIL)

                // Verify expert user is active
                if (existingExpertUser.status != AccountStatus.ACTIVE) throw ApplicationException(ErrorCode.BLUEPRINT_EXPERT_STATUS_FAIL)

                blueprintStepExpertRepository.findByCountryCodeAndStepNameAndExpertId(
                    country.code,
                    stepName,
                    it
                ) ?: blueprintStepExpertRepository.save(
                    BlueprintStepExpertEntity(null, countryCode, stepName, it, userId)
                )
            }

            blueprintAuditService.log(country.code, userId, BlueprintActionStatus.valueOf("UPDATE_EXPERT_$stepName"))
            return blueprintStepExpertRepository.findAllByCountryCodeAndStepName(country.code, stepName)
                .map { expertUserService.retrieveProfileSummary(it.expertId) }
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.BLUEPRINT_EXPERT_UPDATE_FAIL)
                }
            }
        }
    }
}
