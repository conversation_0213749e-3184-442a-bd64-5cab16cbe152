package com.centuroglobal.service

import com.aallam.openai.api.chat.ChatCompletion
import com.aallam.openai.api.chat.ChatCompletionRequest
import com.aallam.openai.api.chat.ChatMessage
import com.aallam.openai.api.chat.ChatRole
import com.aallam.openai.api.http.Timeout
import com.aallam.openai.api.model.ModelId
import com.aallam.openai.client.OpenAI
import com.aallam.openai.client.OpenAIConfig
import com.centuroglobal.shared.client.OpenAIApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.AIChatEntity
import com.centuroglobal.shared.data.payload.openai.*
import com.centuroglobal.shared.exception.ApplicationException
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.apache.commons.codec.binary.Base64
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.concurrent.TimeUnit
import kotlin.time.Duration.Companion.seconds

private val log = KotlinLogging.logger {}

@Service
class OpenAIService(
    @Value("\${app.openapi.api-key}")
    private val apiKey: String,

    @Value("\${app.openapi.prompt-message}")
    private val promptMessageText: String,

    @Value("\${app.openapi.assistant-id}")
    private val assistantId: String,

    @Value("\${app.openapi.file-assistant-id}")
    private val fileAssistantId: String,

    @Value("\${app.openapi.message-read-timeout}")
    private val messageReadTimeout: Long,

    private val adminMasterDataService: AdminMasterDataService,

    private val openAIApiClient: OpenAIApiClient

) {
    private final val config = OpenAIConfig(
        token = apiKey,
        timeout = Timeout(50000.seconds, 50000.seconds, 50000.seconds)
    )
    var openAI: OpenAI = OpenAI(config)

    fun getAnswer(prevQuestions: List<AIChatEntity>, question: String): String=runBlocking {

        //Add chat prompt in beginning
        //val promptMessage = createChatMessage(promptMessageText, ChatRole.System)

        val promptMessage = try {
            val promptContent = adminMasterDataService.getMasterContent("AI_SYSTEM_PROMPT").content
            createChatMessage(
                String(Base64.decodeBase64(promptContent), Charsets.UTF_8),
                ChatRole.System)
        }
        catch (emptyResultEx: EmptyResultDataAccessException) {
            createChatMessage(promptMessageText, ChatRole.System)
        }

        val prevMessages = mutableListOf<ChatMessage>()
        prevQuestions.forEach {
            //question
            prevMessages.add(createChatMessage(it.question, ChatRole.User))
            //answer
            it.answer?.let { it1 -> prevMessages.add(createChatMessage(it1, ChatRole.Assistant)) }
        }

        val currentMessage = createChatMessage(question, ChatRole.User)
        val messages = mutableListOf<ChatMessage>()
        messages.add(promptMessage)
        messages.addAll(prevMessages)
        messages.add(currentMessage)

        val chatCompletionRequest = ChatCompletionRequest(
            model = ModelId("gpt-4-turbo-2024-04-09"),
            messages = messages,
            temperature = 0.0
        )
        val completion: ChatCompletion = openAI.chatCompletion(chatCompletionRequest)
        log.info("tokens used for this request  "+ completion.usage?.totalTokens)
        val answer = completion.choices[0].message.content

        return@runBlocking answer ?: ""
    }

    private fun createChatMessage(message: String, role: ChatRole): ChatMessage {
        return ChatMessage(
            role = role,
            content = message
        )
    }

    fun createThread(threadId: String?=null): ThreadResponse {
        val token = getBearerToken()
        return if (threadId != null) {
            openAIApiClient.getThread(token, threadId)
        } else {
            openAIApiClient.createThread(token)
        }
    }

    private fun getBearerToken(): String {
        return "Bearer $apiKey"
    }

    fun getAnswerFromThread(threadId: String, message: String): MessageData{
        val messageRequest = CreateMessageRequest("user", message)
        return getAnswerFromThread(threadId, messageRequest, assistantId)
    }

    fun getAnswerFromThreadWithAttachments(threadId: String, message: String, fileIds: List<String>): MessageData{
        val attachments = fileIds.map { Attachment(it, listOf(Tool("code_interpreter"))) }
        val messageRequest = CreateMessageRequest("user", message, attachments)
        return getAnswerFromThread(threadId, messageRequest, fileAssistantId, "gpt-4-turbo")
    }


    private fun getAnswerFromThread(threadId: String, message: CreateMessageRequest, assistantId: String, model: String="gpt-4o"): MessageData {

        val token = getBearerToken()

        openAIApiClient.createMessage(token, threadId, message)

        val run = openAIApiClient.createRun(token, threadId, CreateRunRequest(assistantId, model))

        val delayInMillis: Long = 1500
        var readAttempts = TimeUnit.SECONDS.toMillis(messageReadTimeout) / delayInMillis
        do {
            Thread.sleep(delayInMillis)
            val retrievedRun = openAIApiClient.getRun(token, run.threadId, run.id)
            readAttempts -= 1
            if (readAttempts < 0) {
                throw ApplicationException(ErrorCode.MESSAGE_READ_TIMEOUT)
            }
        } while (retrievedRun.status != "completed")

        return openAIApiClient.getMessages(token, run.threadId, 1).data.first()
    }

    fun readMessageText(messages: MessageData): String {
        val answer = messages.content.first().text
        var answerText = answer.value

        //replace file annotations from answer
        answer.annotations.forEach {
            answerText = answerText.replace(it.text, "")
        }
        return answerText
    }

    fun uploadFile(file: MultipartFile, purpose: String): ThreadResponse {
        return openAIApiClient.uploadFile(getBearerToken(), file, purpose)
    }

    fun createThreadWithContext(context: String): ThreadResponse {

        val thread = createThread()
        val token = getBearerToken()

        val message = CreateMessageRequest("assistant", context)

        openAIApiClient.createMessage(token, thread.id, message)
        return thread

    }

}