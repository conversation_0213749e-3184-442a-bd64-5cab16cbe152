package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.payload.account.CreateCorporateUserRequest
import com.centuroglobal.shared.data.payload.account.NotificationSettingRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateInfoRequest
import com.centuroglobal.shared.data.payload.partner.UpdatePartnerCorporateUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.subscription.dto.ISubscriptionUsageDetailsDto
import com.centuroglobal.shared.data.pojo.subscription.response.SubscriptionUsageDetails
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.subscription.SubscriptionDetailsRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.AdminAccessUtil
import mu.KotlinLogging
import org.apache.http.util.TextUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.stream.Collectors
import kotlin.jvm.optionals.getOrNull

private val log = KotlinLogging.logger {}

@Service
class CorporateUserService(
    @Value("\${app.aws.s3.user-profile-folder}")
    private val userProfileFolder: String,
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateRepository: CorporateRepository,
    private val tokenVerificationService: TokenVerificationService,
    private val referralCodeService: ReferralCodeService,
    private val accountEntityRepository: AccountEntityRepository,
    private val bandsRepository: BandsRepository,
    private val caseRepository: CaseRepository,
    private val clientViewRepository: ClientViewRepository,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository,
    private val subscriptionDetailsRepository: SubscriptionDetailsRepository) {

    @Transactional
    fun createCorporateRootUser(request: CreateCorporateUserRequest, sendVerifyMail: Boolean = true): CorporateUser {

        val corporate = corporateRepository.findByIdOrNull(request.corporateId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        try {
            var referredBy: Long? = null
            if (!TextUtils.isEmpty(request.referralCode)) {
                referredBy =
                    referralCodeService.getUserIdByReferralCode(request.referralCode) ?: throw ApplicationException(
                        ErrorCode.REFERRAL_CODE_INVALID
                    )
            }

            val band = bandsRepository.findByNameAndCorporate("Super Admin (free)", corporate)

            val corporateUserEntity = CorporateUserEntity()

            val status = getStatus(false, corporate)

            corporateUserEntity.email = request.email
            corporateUserEntity.firstName = request.firstName?.trim() ?: ""
            corporateUserEntity.lastName = request.lastName?.trim() ?: ""
            corporateUserEntity.jobTitle = request.jobTitle?.trim() ?: ""
            corporateUserEntity.password = ""
            corporateUserEntity.corporate = corporate
            corporateUserEntity.status = status
            corporateUserEntity.role = Role.ROLE_CORPORATE
            corporateUserEntity.lastUpdatedBy = 0L
            corporateUserEntity.referredBy = referredBy
            corporateUserEntity.countryCode = request.countryCode
            corporateUserEntity.tour = false
            corporateUserEntity.subscriptionType = if(corporate.partner!=null) SubscriptionType.PAID else SubscriptionType.FREE
            corporateUserEntity.keepMeInformed = request.keepMeInformed
            corporateUserEntity.band = band
            corporateUserEntity.userType = "PRIMARY"
            corporateUserEntity.lastTermsViewDate = LocalDateTime.now()
            corporateUserEntity.tncView = true
            corporateUserEntity.notificationPreferences = getDefaultNotificationPreferences(corporateUserEntity)
             val createdUser=corporateUserRepository.save(corporateUserEntity)
            createdUser.userRoles.add(
                UserRoleEntity(
                    user = createdUser,
                    role= Role.ROLE_CORPORATE
                ))
            createdUser.lastUpdatedBy = createdUser.id!!
            corporateUserRepository.save(createdUser)

            if (sendVerifyMail && corporateUserEntity.status!=AccountStatus.DRAFT) {
                val validationType = getValidationType(corporate)
                tokenVerificationService.createToken(createdUser, validationType)
            }

            return CorporateUser.ModelMapper.from(createdUser)

        } catch (ex: Exception) {
            when (ex) {
                is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.EMAIL_ALREADY_EXISTS)
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.CORPORATE_USER_CREATE_FAIL)
                }
            }
        }
    }

    private fun getStatus(isDraft: Boolean, corporate: CorporateEntity): AccountStatus {

        return if (isDraft || corporate.status == CorporateStatus.DRAFT) AccountStatus.DRAFT else AccountStatus.PENDING_VERIFICATION
    }

    private fun getDefaultNotificationPreferences(user: CorporateUserEntity): MutableList<NotificationPreferencesEntity> {
        return NotificationType.values().map {
            NotificationPreferencesEntity(
                notificationKey = it,
                value = true,
                corporateUser = user
            )
        }.toMutableList()
    }

    fun createCorporateUser(request: CorporateUserRequest, authenticatedUser: AuthenticatedUser, corporateId: Long,
                            sendVerifyMail: Boolean = true): CorporateUser {

        val corporate = corporateRepository.findByIdOrNull(corporateId)
            ?: throw ApplicationException(ErrorCode.BAD_REQUEST)

        // validate accountId belongs to same corporate.
        val accounts = request.accounts.map { accountEntityRepository.findByIdAndCorporate(it, corporate)
            ?: throw ApplicationException(ErrorCode.BAD_REQUEST) }.toSet()

        // validate managerUserId belongs to same corporate.
        request.managerUserIds!!.forEach { managerUserId ->
            corporateUserRepository.findByIdAndCorporate(managerUserId, corporate)
                ?: throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        // validate bandId belongs to same corporate.
        val band = bandsRepository.findByIdAndCorporate(request.bandId, corporate)
            ?: throw ApplicationException(ErrorCode.BAD_REQUEST)

        try {
            val subscriptionType = corporateUserRepository.findById(corporate.rootUserId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }.subscriptionType
            val corporateUserEntity = CorporateUserEntity()

            corporateUserEntity.email = request.email
            corporateUserEntity.firstName = request.firstName?.trim() ?: ""
            corporateUserEntity.lastName = request.lastName?.trim() ?: ""
            corporateUserEntity.jobTitle = request.jobTitle?.trim() ?: ""
            corporateUserEntity.password = ""
            corporateUserEntity.corporate = corporate
            corporateUserEntity.status = getStatus(request.isDraft, corporate)
            corporateUserEntity.role = Role.ROLE_CORPORATE
            corporateUserEntity.lastUpdatedBy = authenticatedUser.userId
            corporateUserEntity.countryCode = request.countryCode
            corporateUserEntity.tour = false
            corporateUserEntity.subscriptionType = subscriptionType
            corporateUserEntity.keepMeInformed = request.keepMeInformed
            corporateUserEntity.accounts = accounts
            corporateUserEntity.band = band
            corporateUserEntity.dialCode = request.dialCode
            corporateUserEntity.contactNo = request.contactNo
            corporateUserEntity.notificationPreferences = getDefaultNotificationPreferences(corporateUserEntity)
            corporateUserEntity.questionsQuota = corporate.questionsQuota?:0
            corporateUserEntity.lastTermsViewDate = LocalDateTime.now()
            corporateUserEntity.tncView = true

            val createdUser = corporateUserRepository.save(corporateUserEntity)

            val managers = request.managerUserIds!!.stream().map {
                    id->CorporateUserManagerEntity(
                managerId = id,
                corporateUser = createdUser
            )
            }.collect(Collectors.toList())

            createdUser.managers = managers
            createdUser.lastUpdatedBy = createdUser.id!!
            corporateUserRepository.save(createdUser)

            if (sendVerifyMail && corporateUserEntity.status!=AccountStatus.DRAFT) {
                tokenVerificationService.createToken(createdUser, getValidationType(corporate))
            }

            return CorporateUser.ModelMapper.from(createdUser)

        } catch (ex: Exception) {
            when (ex) {
                is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.EMAIL_ALREADY_EXISTS)
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.CORPORATE_USER_CREATE_FAIL)
                }
            }
        }
    }

    fun listUsers(searchFilter: CorporateUserSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser): PagedResult<UserCardDetails> {

        val accesses = authenticatedUser.visibilities.filter { it.feature == "USER" }
        if(accesses.isEmpty()){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val access = accesses[0]

        val corporateUsers = when {
            access.accesses.contains("FULL") -> {

                corporateUserRepository.searchByCriteriaForFullAccess(searchFilter, authenticatedUser.companyId!!, pageRequest)
            }
            access.accesses.contains("REPORTEES") -> {
                val reporteesIds = corporateUserRepository.findAllByManagersManagerId(authenticatedUser.userId).map {
                    it.id!!
                }
                corporateUserRepository.searchByCriteriaForReporteesAccess(searchFilter, authenticatedUser.companyId!!,
                    reporteesIds.plus(authenticatedUser.userId), pageRequest)
            }
            access.accesses.contains("OWN") -> {
                corporateUserRepository.searchByCriteriaForOwnAccess(searchFilter, authenticatedUser.companyId!!,
                    authenticatedUser.userId, pageRequest)
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }

        val userDetails = corporateUsers.map { UserCardDetails.ModelMapper.from(
            it, caseRepository.countByCreatedBy(clientViewRepository.findById(it.id!!).get()),
            corporateUserRepository.countByManagersManagerId(it.id!!),
            it.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}
        ) }
        return PagedResult.ModelMapper.from(corporateUsers, userDetails.toList())

    }

    fun updateCorporateUser(userId: Long, corporateUserRequest: CorporateUserRequest, authenticatedUser: AuthenticatedUser) {

        val existingUser = corporateUserRepository.findById(userId).orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}

        existingUser.firstName = corporateUserRequest.firstName!!
        existingUser.lastName = corporateUserRequest.lastName!!
        existingUser.jobTitle = corporateUserRequest.jobTitle!!
        existingUser.keepMeInformed = corporateUserRequest.keepMeInformed
        existingUser.email = corporateUserRequest.email

        if(corporateUserRequest.bandId != null) {
            existingUser.band = bandsRepository.findById(corporateUserRequest.bandId!!).
            orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}
        }


        val accounts = corporateUserRequest.accounts.map {
            accountEntityRepository.findById(it).orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}
        }.toSet()
        existingUser.accounts = accounts

        if(corporateUserRequest.managerUserIds != null) {
            existingUser.managers = corporateUserRequest.managerUserIds!!.stream().map {
                    id->CorporateUserManagerEntity(
                managerId = id,
                corporateUser = existingUser
            )
            }.collect(Collectors.toList())
        }

        existingUser.countryCode = corporateUserRequest.countryCode
        existingUser.dialCode = corporateUserRequest.dialCode
        existingUser.contactNo = corporateUserRequest.contactNo
        corporateUserRepository.save(existingUser)
    }

    @Transactional(readOnly = true)
    fun fetchUser(userId: Long, corporateId: Long): CorporateUserResponse {

        val corporate = corporateRepository.getReferenceById(corporateId)
        val corporateUser = corporateUserRepository.findByIdAndCorporate(userId, corporate)
            ?: throw ApplicationException(ErrorCode.BAD_REQUEST)

        return toCorporateUserResponse(corporateUser)

    }

    @Transactional(readOnly = true)
    private fun toCorporateUserResponse(corporateUser: CorporateUserEntity): CorporateUserResponse {
        return CorporateUserResponse.ModelMapper.from(
            corporateUser,
            corporateUser.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url) })
    }

    fun updateCorporateUserForAdmins(userId: Long, profileRequest: UpdateCorporateInfoRequest): CorporateUserResponse {

        val rootUser = updateCorporateUser(userId, profileRequest)

        val band = bandsRepository.findById(profileRequest.bandId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        rootUser.band = band

        if(profileRequest.managerUserIds != null && profileRequest.managerUserIds!!.isNotEmpty()) {
            rootUser.managers = profileRequest.managerUserIds!!.stream().map {
                    id->CorporateUserManagerEntity(
                managerId = id,
                corporateUser = rootUser
            )
            }.collect(Collectors.toList())
        }

        val accounts = profileRequest.accounts?.map {
            accountEntityRepository.findById(it).orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}
        }?.toSet()
        rootUser.accounts = accounts

        val corporateUser = corporateUserRepository.save(rootUser)
        corporateRepository.save(rootUser.corporate)

        return toCorporateUserResponse(corporateUser)
    }


    //@Transactional
    fun update(userId: Long, profileRequest: UpdateCorporateInfoRequest): CorporateUserResponse {

        val rootUser = updateCorporateUser(userId, profileRequest)

        val corporateUser = corporateUserRepository.save(rootUser)
        corporateRepository.save(rootUser.corporate)

        return toCorporateUserResponse(corporateUser)
    }

    private fun updateCorporateUser(userId: Long, profileRequest: UpdateCorporateInfoRequest): CorporateUserEntity {

        val rootUser = corporateUserRepository.findById(userId).get()
        rootUser.firstName = profileRequest.firstName.trim()
        rootUser.lastName = profileRequest.lastName.trim()
        rootUser.jobTitle = profileRequest.jobTitle.trim()
        rootUser.keepMeInformed = profileRequest.keepMeInformed ?: false
        rootUser.corporate.name = profileRequest.corporateName.trim()
        rootUser.countryCode = profileRequest.countryCode
        rootUser.dialCode = profileRequest.dialCode
        rootUser.contactNo = profileRequest.contactNo
        rootUser.educationQualification = profileRequest.educationQualification
        rootUser.salary = profileRequest.salary
        rootUser.relevantExperience = profileRequest.relevantExperience
        profileRequest.email?.let {
            rootUser.email = it
        }

        updateNotificationSettings(rootUser, profileRequest.notificationSettings)

        rootUser.profilePhotoUrl = profileRequest.profilePicS3Key.let {
            awsS3Service.updateProfilePicture(
                it,
                if (it.isNullOrBlank()) null else "$userProfileFolder/${it}",
                rootUser.profilePhotoUrl
            )
        }

        val corporate = profileRequest.corporateId?.let {
            rootUser.corporate = corporateRepository.findById(it).orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}
        }

        return rootUser
    }


    private fun updateNotificationSettings(
        rootUser: CorporateUserEntity,
        request: List<NotificationSettingRequest>?
    ) {
        val existing = rootUser.notificationPreferences
        val updatedSettings = mutableListOf <NotificationPreferencesEntity>()
        request?.let { notificationSettings ->

            notificationSettings.forEach { notificationSetting ->
                val exists = existing.any { notificationSetting.key == it.notificationKey }
                if (!exists) {
                    updatedSettings.add(
                        NotificationPreferencesEntity(
                            notificationKey = notificationSetting.key,
                            value = notificationSetting.value,
                            corporateUser = rootUser
                        )
                    )
                }
            }
        }
        val requestKeys = request?.map { it.key } ?: emptyList()
        rootUser.notificationPreferences.removeAll { !requestKeys.contains(it.notificationKey) }
        rootUser.notificationPreferences.addAll(updatedSettings)
    }


    /**
     * Returns user's manager ids if present else returns empty list
     */
    fun getUserManagers(userId: Long): List<Long> {
        val managerUserIds = mutableListOf<Long>()
        val corporateUserEntity = corporateUserRepository.findById(userId)
        corporateUserEntity.ifPresent { managerUserIds.addAll(it.managers.map { managers -> managers.managerId })}
        return managerUserIds
    }

    @Transactional
    fun updatePartnerCorporateUser(updateRequest: UpdatePartnerCorporateUserRequest, partnerId: Long, corporateUserId: Long): Long? {

        val existingUser = corporateUserRepository.findById(corporateUserId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND)}

        existingUser.corporate.name = updateRequest.corporateName
        existingUser.corporate.primaryColor = updateRequest.primaryColor
        existingUser.corporate.secondaryColor = updateRequest.secondaryColor
        existingUser.corporate.companyLogoId = updateRequest.companyLogoId?.let {
            awsS3Service.updateProfilePicture(
                updateRequest.companyLogoId,
                if (updateRequest.companyLogoId.isNullOrBlank()) null else "$userProfileFolder/${updateRequest.companyLogoId}",
                existingUser.corporate.companyLogoId)
        }
        existingUser.firstName = updateRequest.firstName
        existingUser.lastName = updateRequest.lastName
        existingUser.jobTitle = updateRequest.jobTitle
        existingUser.countryCode = updateRequest.countryCode
        existingUser.keepMeInformed = updateRequest.keepMeInformed

        corporateRepository.save(existingUser.corporate)
        corporateUserRepository.save(existingUser)
        return existingUser.id
    }

    @Transactional(readOnly = true)
    fun listCorporateUser(
        filter: CorporateUserDetailsFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): ListingWithStatsDetails<CorporateUserDetails>? {

        val corporateUserList = corporateUserRepository.searchByCriteria(filter, pageRequest)
        val corporateUserListResponse = toCorporateUserDetails(corporateUserList)

        val stats = getCorporateUserStats(filter)

        return ListingWithStatsDetails(corporateUserListResponse, stats)
    }

    private fun getCorporateUserStats(searchFilter: CorporateUserDetailsFilter):  ListingStats {

        val statsByCriteria = corporateUserRepository.findStatsByCriteria(searchFilter)

        val corporateStatsMap = statsByCriteria.map { mapOf(it[0] to it[1]) }.flatMap { it1->it1.entries }.associate { it2->it2.key to it2.value }

        val activeUsers =corporateStatsMap.getOrDefault(AccountStatus.ACTIVE, 0L) as Long
        val suspendedUsers = corporateStatsMap.getOrDefault(AccountStatus.SUSPENDED, 0L) as Long
        val pendingUsers = corporateStatsMap.getOrDefault(AccountStatus.PENDING_VERIFICATION, 0L) as Long

        val totalUsers = corporateStatsMap.values.sumOf { it as Long }

        return ListingStats.ModelMapper.from(
            totalUsers,
            activeUsers,
            suspendedUsers,
            pendingUsers
        )
    }

    private fun toCorporateUserDetails(corporateUserList: Page<CorporateUserEntity>): PagedResult<CorporateUserDetails?> {
        val corporateUserDetailsList = corporateUserList.map {
            CorporateUserDetails.ModelMapper.from(
                it, loginAccountRepository
            )
        }
        return PagedResult.ModelMapper.from(corporateUserList, corporateUserDetailsList.toList())
    }

    fun getCorporateUsageDetails(token: String?, authenticatedUser: AuthenticatedUser, corporateId: Long?): SubscriptionUsageDetails{
        val companyId =
            if (AdminAccessUtil.isAdminOrPartner(authenticatedUser)) corporateId else authenticatedUser.companyId

        if(token == null){

            throw ApplicationException(ErrorCode.CORPORATE_USAGE_CODE_EMPTY)

        } else if(companyId == null){

            throw ApplicationException(ErrorCode.CORPORATE_ID_EMPTY)

        } else {

            val currentDate = LocalDate.now()

            val usageDetails: List<ISubscriptionUsageDetailsDto> =
                corporateUserRepository.findSubscriptionUsageDetailsByCode(companyId, token,
                currentDate.monthValue, currentDate.year)

            val subUsage = if(usageDetails.isEmpty()) null
            else usageDetails[0]

            if(usageDetails.isEmpty()){
                val threshold =
                    subscriptionDetailsRepository.findThresholdByCorporateIdAndCode(companyId, token)
                        ?: throw ApplicationException(ErrorCode.CORPORATE_MISSING_USAGE_DATA)
                return SubscriptionUsageDetails(threshold,0f,0f,0)
            }

            return SubscriptionUsageDetails(subUsage!!.getThreshold(),
                subUsage.getOverageRate(),
                subUsage.getOverageCharge(),
                subUsage.getTotalUsage())
        }
    }

    @Transactional
    fun sendVerificationEmail(userId: Long): Boolean? {

        val corporateUserEntity = corporateUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val corporate = corporateUserEntity.corporate

        val validationType = getValidationType(corporate)

        var corporateStatus = corporate.status
        if (corporate.status == CorporateStatus.DRAFT && corporate.rootUserId == userId) {
            corporateStatus = CorporateStatus.PENDING_VERIFICATION
        }

        //only update user's status if corporate is in not in draft
        if (corporateStatus != CorporateStatus.DRAFT && corporateUserEntity.status == AccountStatus.DRAFT) {
            corporateUserEntity.status = AccountStatus.PENDING_VERIFICATION
            corporate.status = corporateStatus
            tokenVerificationService.invalidateUnusedToken(corporateUserEntity.id!!, validationType)
            tokenVerificationService.createToken(corporateUserEntity, validationType)

            corporateRepository.save(corporate)
            corporateUserRepository.save(corporateUserEntity)
            return true
        }
        throw ApplicationException(ErrorCode.CORPORATE_IS_DRAFT)
    }

    private fun getValidationType(corporate: CorporateEntity): ValidationType {
        return  if(corporate.partner!=null){
            ValidationType.INVITE_PARTNER_CORPORATE
        }
        else {
            ValidationType.CORPORATE_SIGNUP
        }
    }


    @Transactional(readOnly = true)
    fun getCorporateSuperAdmins(caseOwner: Long): List<Long> {
        val corporateUser = corporateUserRepository.findById(caseOwner).getOrNull()
        return corporateUser?.corporate?.users?.filter {
            listOf("Super Admin", "Super Admin (free").contains(it.band?.name) }?.map { it.id!! }?: emptyList()
    }

}