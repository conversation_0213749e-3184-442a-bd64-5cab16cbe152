package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.*
import com.centuroglobal.shared.data.entity.view.UserView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.case.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.case.*
import com.centuroglobal.shared.data.pojo.task.dto.TaskWorkflowSearchFilter
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowReport
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.case.CaseFormRepository
import com.centuroglobal.shared.repository.view.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.*
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.AdminAccessUtil.Companion.isAdmin
import com.centuroglobal.shared.util.AdminAccessUtil.Companion.isAdminOrPartner
import com.centuroglobal.shared.util.AdminAccessUtil.Companion.isExpert
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.shared.util.TimeUtil.Companion.fromInstant
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.stream.Collectors

private val log = KotlinLogging.logger {}

@Service
class CaseService(

    private val milestonesRepository: MilestonesRepository,
    private val caseMilestonesRepository: CaseMilestonesRepository,
    private val caseStatusHistoryRepository: CaseStatusHistoryRepository,
    private val caseRepository: CaseRepository,
    private val directorRepository: DirectorRepository,
    private val travellerRepository: TravellerRepository,
    private val applicantInfoRepository: ApplicantInfoRepository,
    private val shareholderRepository: ShareholderRepository,
    private val caseCategoryRepository: CaseCategoryRepository,
    private val clientViewRepository: ClientViewRepository,
    private val caseViewRepository: CaseViewRepository,
    private val sicCodeRepository: SICCodeRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val caseDocumentsRepository: CaseDocumentsRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val validationTokenRepository: ValidationTokenRepository,
    private val adminUserService: AdminUserService,
    private val corporateService: CorporateService,
    private val userViewRepository: UserViewRepository,
    private val caseFeesApprovalHistoryRepository: CaseFeesApprovalHistoryRepository,
    private val countryRepository: CountryRepository,
    private val caseStatusMasterRepository: CaseStatusMasterRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val accountEntityRepository: AccountEntityRepository,
    private val caseEmailService: CaseEmailService,
    private val caseDoucmentsStatusLogsRepository: CaseDocumentsStatusLogsRepository,
    private val caseDocumentsAuditRepository: CaseDocumentsAuditRepository,
    private val docRepoService: DocRepoService,
    private val groupChatService: GroupChatService,
    private val corporateUserService: CorporateUserService,
    private val expertUserService: ExpertUserService,
    private val groupChatParticipantRepository: GroupChatParticipantRepository,
    private val partnerRepository: PartnerRepository,
    private val caseAssigneeRepository: CaseAssigneeRepository,
    private val caseManagerRepository: CaseManagerRepository,
    private val reminderRepository: ReminderRepository,
    private val caseNotesRepository: CaseNotesRepository,
    private val employeeOfRecordApplicantRepository: EmployeeOfRecordApplicantRepository,
    private val travelAssessmentService: TravelAssessmentService,
    private val taskWorkflowService: TaskWorkflowService,
    private val userProfileUtil: UserProfileUtil,
    private val caseFormRepository: CaseFormRepository

) : BaseService<CaseEntity, Long>() {

    @Transactional(readOnly = true)
    fun listCases(
        filter: CaseSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser,
        caseListType: String,
        archive: Boolean
    ): PagedResult<CaseViewDetails> {
        val case = if (isAdmin(authenticatedUser) || authenticatedUser.userType == UserType.PARTNER.name) {
            val clientList: List<Long>? =
                filter.corporateId?.let { it -> corporateUserRepository.findIdByCorporateId(it).map { it.id } }
            caseViewRepository.searchByCriteria(filter, pageRequest, clientList)
        } else if (caseListType == "CASES_INITIATED" && authenticatedUser.role == Role.ROLE_CORPORATE.name) {
            val pair = corporateService.getEligibleClientList(authenticatedUser, null, null)
            log.info("Client List: ${pair.second.map { it.id }}")
            caseViewRepository.searchByCriteriaAndCreatedUser(filter, pageRequest, pair.second.map { it.userId })
        } else if (caseListType == "CASES_INITIATED" && isExpert(authenticatedUser.role)) {
            caseViewRepository.searchByCriteriaAndCreatedUser(filter, pageRequest, listOf(authenticatedUser.userId))
        } else {
            caseViewRepository.searchByCriteriaAndAssigneeUser(filter, pageRequest, authenticatedUser)
        }
        return PagedResult.ModelMapper.from(
            case,
            case.map { CaseViewDetails.ModelMapper.from(it, getUserProfile(it.accountManager)) }.toList()
        )
    }


    @Transactional(readOnly = true)
    fun downloadCases(
        filter: CaseSearchFilter,
        authenticatedUser: AuthenticatedUser,
        caseListType: String,
        archive: Boolean
    ): List<CaseViewForDownload> {
        val cases = if (isAdmin(authenticatedUser) || authenticatedUser.userType == UserType.PARTNER.name) {
            authenticatedUser.partnerId?.let { filter.partnerId = it }
            val clientList: List<Long>? =
                filter.corporateId?.let { corporateUserRepository.findIdByCorporateId(it).map { it -> it.id } }
            caseViewRepository.searchByCriteriaForResidenceCountry(filter, clientList)
        } else if (caseListType == "CASES_INITIATED" && authenticatedUser.role == Role.ROLE_CORPORATE.name) {
            val pair = corporateService.getEligibleClientList(authenticatedUser, null, null)
            caseViewRepository.searchByCriteriaForDownloadAndCreatedUser(filter, pair.second.map { it.userId })
        } else if (caseListType == "CASES_INITIATED" && authenticatedUser.role == Role.ROLE_EXPERT.name) {
            caseViewRepository.searchByCriteriaForDownloadAndCreatedUser(filter, listOf(authenticatedUser.userId))
        } else {
            caseViewRepository.searchByCriteriaForDownloadAndAssigneeUser(filter, authenticatedUser)
        }
        return cases.map { CaseViewForDownload.ModelMapper.from(it, getUserProfile(it.accountManager)) }.toList()
    }

    @Transactional
    fun getDetails(caseId: Long, authenticatedUser: AuthenticatedUser): CaseEntity {
        return super.get(caseId, authenticatedUser, caseRepository, "USER")
    }

    fun canAccessCaseFees(case: CaseEntity, requestedByUser: AuthenticatedUser): Boolean {

        if (requestedByUser.role == "ROLE_CORPORATE") {
            val caseCreatedByUser = corporateUserRepository.findById(case.createdBy!!.userId).get()
            val accesses = requestedByUser.accesses.filter { it.feature == "CASE" }
            if (accesses.isNotEmpty()) {
                val access = accesses[0]
                when {
                    access.accesses.contains("VIEW_FEE") -> {
                        //caseCreated and current user belongs to same corporate
                        val corporateUser = corporateUserRepository.findById(requestedByUser.userId)
                            .orElseThrow { ApplicationException(ErrorCode.BAD_REQUEST) }
                        return caseCreatedByUser.corporate.id == corporateUser.corporate.id
                    }

                    access.accesses.contains("VIEW_FEE_APPROVED") -> {
                        // Case createdBy is reportees of current user
                        return case.caseFees?.isApproved ?: false
                    }
                }
            }
            return false
        }
        return true
    }

    fun getDetails(uuid: String, feeToken: String): CaseDetailsForPublic {
        val case = caseRepository.findByUuid(uuid) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (case.feeToken != feeToken) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        return if (case.caseFees?.isApproved == true && case.caseFees?.approverDetails?.feesAgreedDate?.isBefore(
                LocalDateTime.now().minusDays(7)
            ) == true
        ) {
            CaseDetailsForPublic(isTokenExpired = true)
        } else {
            CaseDetailsForPublic.ModelMapper.from(case)
        }
    }

    fun listSICCodes(): List<SICCode> {
        return sicCodeRepository.findAll().map { SICCode.ModelMapper.from(it) }.toList()
    }

    @Transactional
    fun update(caseId: Long, authenticatedUser: AuthenticatedUser, request: UpdateCaseRequest, role: String): Boolean {

        var case = super.get(caseId, authenticatedUser, caseRepository, "USER")
        val userId = authenticatedUser.userId
        case.notifyPrimaryExpert = request.notifyPrimaryExpert
        case.isPriorityCase = request.isPriorityCase
        case.startDate = null
        case.assignCompany = null
        var cgAccepted = false

        if (isAdminOrPartner(authenticatedUser) && !isExpert(role)) {
            // case.notes =  request.notes
            val notes = case.notes?.toMutableList()

            request.notes?.let {
                notes?.add(
                    CaseNotesEntity(
                        case = case,
                        note = it,
                        userId = userId
                    )
                )
            }

            case.notes = notes

            case.accountManagerUserType = PartnerCaseType.CG
            if (case.cgRequestedStatus == CGRequestStatus.CG_REQUESTED) {
                case.cgRequestedStatus = CGRequestStatus.CG_ACCEPTED
                cgAccepted = true
            }
        }
        if (authenticatedUser.userType == UserType.PARTNER.name) {
            case.accountManagerUserType = PartnerCaseType.PARTNER
        }

        //send email on status change to all stakeholders of the case
        val recipientListApplicant = mutableListOf<String>()

        if (request.caseOwner != null) case.createdBy = clientViewRepository.getReferenceById(request.caseOwner!!)
        case.startDate = request.startDate?.let { fromInstant(it / 1000) }
        case.assignCompany = request.assignCompany?.let { expertCompanyProfileRepository.getReferenceById(it) }
        case.estimatedTimeline = request.estimatedTimeline
        case.actionFor = request.actionFor
        case.notifyCaseOwner = request.notifyCaseOwner
        case.notifyApplicant = request.notifyApplicant
        case.sendUpdateReminder = request.sendUpdateReminder ?: case.sendUpdateReminder

        var visaDatesAdded = false

        if (case.category?.subCategoryId == "SINGLE_VISA" || case.category?.subCategoryId == "MULTIPLE_VISA"
            || case.category?.subCategoryId == "RIGHT_TO_WORK_CHECK" || case.category?.subCategoryId == "DEPENDENT_VISA"
            || case.category?.subCategoryId == "BUSINESS_VISA" || case.category?.subCategoryId == "ENTRY_VISA"
        ) {

            when (case) {
                is SingleVisaEntity -> {
                    case.visaExpiryDate = request.visaExpiryDate
                    case.visaIssueDate = request.visaIssueDate
                    case.visaType = request.visaType
                    visaDatesAdded = true
                    // send email to applicant
                }

                is MultipleVisaEntity -> {
                    case.visaType = request.visaType
                    case.visaIssueDate = request.visaIssueDate
                    case.visaExpiryDate = request.visaExpiryDate
                    visaDatesAdded = true
                }

                is RightToWorkCheckEntity -> {
                    case.visaIssueDate = request.visaIssueDate
                    case.visaType = request.visaType
                    case.visaExpiryDate = request.visaExpiryDate
                    visaDatesAdded = true
                }

                is DependentVisaEntity -> {
                    case.visaIssueDate = request.visaIssueDate
                    case.visaType = request.visaType
                    case.visaExpiryDate = request.visaExpiryDate
                    visaDatesAdded = true
                }

                is BusinessVisaEntity -> {
                    case.visaIssueDate = request.visaIssueDate
                    case.visaType = request.visaType
                    case.visaExpiryDate = request.visaExpiryDate
                    visaDatesAdded = true
                }

                is EntryVisaEntity -> {
                    case.visaIssueDate = request.visaIssueDate
                    case.visaType = request.visaType
                    case.visaExpiryDate = request.visaExpiryDate
                    visaDatesAdded = true
                }
            }
        }

        if (request.isStatusChange ) {
            updateCaseStatusAndMilestone(case, request.status, request.statusUpdate, userId, null,
                case.isLegacy, authenticatedUser.email)
        }

        if (!isExpert(role)) {

            case.account = accountEntityRepository.getReferenceById(request.accountId)
            case.accountManager = null
            case.accountManager = request.accountManager?.let { loginAccountRepository.getReferenceById(it) }

            var requestExpertList: MutableList<UserView> = getExperts(request.notifyPrimaryExpert, request.experts)

            if (requestExpertList.isNotEmpty())
                requestExpertList = requestExpertList.distinct() as MutableList<UserView>

            val newExpertList = updateAssignee(case, requestExpertList)

            val requestManagerList: MutableList<UserView> = getUsers(request.managers)
            val newManagers = updateManagers(case, requestManagerList)

            //case manager emails
            newManagers.forEach {
                caseEmailService.caseAssignEmail(case, it.user.firstName, it.user.email)
            }

            newExpertList.forEach { user ->
                caseEmailService.caseAssignEmail(case, user.expert.firstName, user.expert.email)
            }
            if (cgAccepted && newExpertList.isNotEmpty()) {
                caseEmailService.caseCgRequestAcceptedMail(case, newExpertList[0])
            }
        }
        case = caseRepository.save(case)

        case.notes?.forEach { caseNotesRepository.save(it) }

        //update chat participants
        val caseManagers = caseManagerRepository.findAllByCase(case)
        val caseAssignee = caseAssigneeRepository.findAllByCase(case)
        updateChatParticipants(
            caseId, caseManagers.map { it.user.id!! }, caseAssignee.map { it.expert.id!! },
            case.accountManager?.id, case.createdBy!!.userId, ChatType.CASE
        )
        updateChatParticipants(
            caseId, caseManagers.map { it.user.id!! }, caseAssignee.map { it.expert.id!! },
            case.accountManager?.id, case.createdBy!!.userId, ChatType.APPLICANT_CASE
        )
        updateChatParticipants(
            caseId, caseManagers.map { it.user.id!! }, caseAssignee.map { it.expert.id!! },
            case.accountManager?.id, case.createdBy!!.userId, ChatType.CLIENT_CASE
        )

        if (case.isLegacy) {
            val caseStatusMasterEntity =
                caseStatusMasterRepository.findBySubCategoryAndStatus(case.category?.subCategoryId!!, request.status)
            case.percentCompletion = caseStatusMasterEntity.percentage.toInt()
        }
        caseRepository.save(case)
        if (case.trackingType != TrackingType.NOTRACK && (request.isVisaDateChanged || visaDatesAdded)) {
            recalculateReminders(case)
        }
        return true
    }

    fun updateCaseStatusAndMilestone(
        case: CaseEntity,
        status: String,
        statusUpdate: String?,
        userId: Long,
        milestone: String? = null,
        isLegacy: Boolean,
        updatedByEmail:String
    ) {
        val recipientListApplicant: MutableList<String> = mutableListOf()

        case.status = status
        case.statusUpdate = statusUpdate
        val caseStatusHistory = CaseStatusHistory(
            status = case.status,
            statusUpdate = case.statusUpdate,
            case = case,
            lastUpdatedBy = userId,
            actionFor = case.actionFor
        )
        case.archive = (case.status == CaseStatus.CANCELLED.name || case.status == CaseStatus.CASE_DECLINED.name)
        caseStatusHistoryRepository.save(caseStatusHistory)
        val countryEntity = countryRepository.findAllByCountryCode(case.country!!)

        caseEmailService.caseEmailStatusUpdate(countryEntity.country, case, updatedByEmail)
        //send email for applicant
        if (case is SingleVisaEntity && case.notifyApplicant) {
            case.emailAddress?.let { i -> recipientListApplicant.add(i) }
            caseEmailService.caseEmailStatusUpdateForApplicant(
                countryEntity.country, case.initiatedFor!!, case.category?.subCategoryId!!,
                case.category?.subCategoryName!!, case.id, case.statusUpdate!!, recipientListApplicant,
            )
        }
        if (case.isLegacy) {
            // legacy cases
            processMilestoneData(case, userId)
        }
        caseRepository.save(case)
    }

    private fun recalculateReminders(case: CaseEntity) {
        var reminders = reminderRepository.findAllByReferenceId(case.id!!)

        reminders = addReminderIfNotExists(reminders, CaseDeadlineType.VISA_EXPIRY, case.id!!)
        reminders = addReminderIfNotExists(reminders, CaseDeadlineType.VISA_RENEWAL, case.id!!)

        // if documents exists for case but no reminders present
        if (!reminders.any { it.deadlineAgainst == CaseDeadlineType.DOCUMENT_EXPIRY }) {
            val caseDocuments = caseDocumentsRepository.findAllByCaseAndExpiryDateNotNull(case)

            if (caseDocuments.isNotEmpty()) {
                caseDocuments.forEach {
                    reminders =
                        addReminder(reminders, CaseDeadlineType.DOCUMENT_EXPIRY, case.id!!, it.expiryDate, it.id)
                }
            }
        }
        reminders = reminders.map { calculateReminderDate(case, it) }.filter { it.date != null }

        reminderRepository.saveAll(reminders)
    }

    private fun addReminderIfNotExists(
        reminders: List<ReminderEntity>,
        reminderType: CaseDeadlineType,
        caseId: Long
    ): List<ReminderEntity> {
        if (!reminders.any { it.deadlineAgainst == reminderType }) {
            return addReminder(reminders, reminderType, caseId)
        }
        return reminders
    }

    private fun addReminder(
        reminders: List<ReminderEntity>,
        reminderType: CaseDeadlineType,
        caseId: Long,
        date: LocalDateTime? = null,
        docId: Long? = null
    ): List<ReminderEntity> {
        return reminders.plus(
            ReminderEntity(
                referenceId = caseId,
                trackType = CaseTrackType.EXACT,
                date = date,
                deadlineAgainst = reminderType,
                documentId = docId
            )
        )
    }

    @Transactional
    private fun updateChatParticipants(
        caseId: Long,
        managers: List<Long>,
        assignee: List<Long>,
        accountManager: Long?,
        caseOwner: Long,
        chatType: ChatType
    ) {

        val newParticipants = mutableSetOf<Long>()
        newParticipants.addAll(managers)
        when(chatType) {
            ChatType.CASE -> {
                newParticipants.add(caseOwner)
                // Add case owners managers as participants by default
                newParticipants.addAll(corporateUserService.getUserManagers(caseOwner))
                newParticipants.addAll(assignee)
                accountManager?.let { newParticipants.add(it) }
            }
            ChatType.APPLICANT_CASE -> {
                newParticipants.add(caseOwner)
                // These participants are already updated in default initialization
            }
            ChatType.CLIENT_CASE -> {
                newParticipants.addAll(corporateUserService.getUserManagers(caseOwner))
                newParticipants.addAll(corporateUserService.getCorporateSuperAdmins(caseOwner))
            }
            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }

        groupChatService.updateChatParticipants(caseId, chatType, newParticipants)
    }

    private fun processMilestoneData(case: CaseEntity, userId: Long) {
        val requestMilestone =
            milestonesRepository.findByCaseSubCategoryAndStatus(case.category?.subCategoryId!!, case.status) ?: return
        val lastCaseMilestone = caseMilestonesRepository.findFirstByCaseOrderByLastUpdatedDateDesc(case)
        val requestSequence = requestMilestone.sequence
        val caseMilestonesList: MutableList<CaseMilestonesEntity> = mutableListOf()

        if (lastCaseMilestone != null) {
            caseMilestonesList.addAll(
                processInitiatedCaseMilestones(
                    lastCaseMilestone,
                    case,
                    requestSequence,
                    requestMilestone,
                    userId
                )
            )
        } else {
            caseMilestonesList.addAll(processNewCaseMilestones(requestSequence, case, requestMilestone, userId))
        }
        caseMilestonesRepository.saveAll(caseMilestonesList)
    }

    private fun processNewCaseMilestones(
        requestSequence: Long, case: CaseEntity, requestMilestone: MilestonesEntity,
        userId: Long
    ): MutableList<CaseMilestonesEntity> {
        var requestSequence1 = requestSequence
        val caseMilestonesList: MutableList<CaseMilestonesEntity> = mutableListOf()
        if (requestSequence1 == 1L) {
            val caseMilestonesEntity = CaseMilestonesEntity(
                case = case,
                milestoneKey = requestMilestone.milestoneKey,
                lastUpdatedBy = userId
            )
            caseMilestonesList.add(caseMilestonesEntity)
        } else {
            ++requestSequence1
            for (i in 1 until requestSequence1) {
                val miles = milestonesRepository.findBySequenceAndCaseSubCategory(i, case.category?.subCategoryId!!)
                val caseMilestonesEntity = CaseMilestonesEntity(
                    case = case,
                    milestoneKey = miles[0].milestoneKey,
                    lastUpdatedBy = userId
                )
                caseMilestonesList.add(caseMilestonesEntity)
            }
        }
        return caseMilestonesList
    }

    private fun processInitiatedCaseMilestones(
        lastCaseMilestone: CaseMilestonesEntity,
        case: CaseEntity,
        requestSequence: Long,
        requestMilestone: MilestonesEntity,
        userId: Long
    ): MutableList<CaseMilestonesEntity> {
        val caseMilestonesList: MutableList<CaseMilestonesEntity> = mutableListOf()
        var requestSequence1 = requestSequence
        val lastMilestone = milestonesRepository.findFirstByMilestoneKeyAndCaseSubCategory(
            lastCaseMilestone.milestoneKey, case.category?.subCategoryId!!
        )
        var lastSequence = lastMilestone.sequence
        val currentDate = LocalDateTime.now()

        if (requestSequence1 < lastSequence) {
            val caseMilestonesEntity =
                caseMilestonesRepository.findByCaseAndMilestoneKey(case, requestMilestone.milestoneKey)
            if (caseMilestonesEntity != null) {
                caseMilestonesEntity.lastUpdatedDate = currentDate
                caseMilestonesList.add(caseMilestonesEntity)
            }
        } else {
            ++lastSequence
            ++requestSequence1
            for (i in lastSequence until requestSequence1) {
                val miles =
                    milestonesRepository.findBySequenceAndCaseSubCategory(i, case.category?.subCategoryId!!)
                var caseMilestonesEntity =
                    caseMilestonesRepository.findByCaseAndMilestoneKey(case, miles[0].milestoneKey)
                if (caseMilestonesEntity == null) {
                    caseMilestonesEntity = CaseMilestonesEntity(
                        case = case,
                        milestoneKey = miles[0].milestoneKey,
                        lastUpdatedBy = userId
                    )
                } else {
                    caseMilestonesEntity.lastUpdatedDate = currentDate
                }
                caseMilestonesList.add(caseMilestonesEntity)
            }
        }
        return caseMilestonesList
    }

    private fun updateAssignee(
        case: CaseEntity,
        requestExpertList: MutableList<UserView>
    ): MutableList<CaseAssigneeEntity> {
        val dbExperts = caseAssigneeRepository.findAllByCase(case)
        val existing: MutableList<CaseAssigneeEntity> = mutableListOf()
        val dbExpertIterator = dbExperts.iterator()
        while (dbExpertIterator.hasNext()) {
            val it = dbExpertIterator.next()
            var contains = false
            if (requestExpertList.isNotEmpty()) {
                for (expert in requestExpertList) {
                    if (it.expert.id == expert.id) {
                        contains = true
                        break
                    }
                }
            }
            if (contains)
                existing.add(it)
            else {
                caseAssigneeRepository.delete(it)
                dbExpertIterator.remove()  //not in request so we have to remove it
            }
        }
        val newExpertList: MutableList<CaseAssigneeEntity> = requestExpertList.map {
            CaseAssigneeEntity(
                case = case,
                expert = expertUserRepository.getReferenceById(it.id!!),
                userType = case.accountManagerUserType!!
            )
        }.toMutableList()
        if (newExpertList.isNotEmpty() && existing.isNotEmpty()) {
            log.info("new List size " + newExpertList.size + "  existing list size" + existing.size)
            newExpertList.removeAll(existing)
        }
        caseAssigneeRepository.saveAll(newExpertList)
        return newExpertList
    }

    private fun updateManagers(
        case: CaseEntity,
        requestExpertList: MutableList<UserView>
    ): MutableList<CaseManagerEntity> {
        val dbExperts = caseManagerRepository.findAllByCase(case)
        val existing: MutableList<CaseManagerEntity> = mutableListOf()
        val dbExpertIterator = dbExperts.iterator()
        while (dbExpertIterator.hasNext()) {
            val it = dbExpertIterator.next()
            var contains = false
            if (requestExpertList.isNotEmpty()) {
                for (expert in requestExpertList) {
                    if (it.user.id == expert.id) {
                        contains = true
                        break
                    }
                }
            }
            if (contains)
                existing.add(it)
            else {
                caseManagerRepository.deleteById(it.id!!)
                dbExpertIterator.remove()  //not in request so we have to remove it
            }
        }
        var newExpertList = requestExpertList.map {
            CaseManagerEntity(
                case = case,
                user = loginAccountRepository.getReferenceById(it.id!!),
                userType = case.accountManagerUserType!!
            )
        }.toMutableList()
        if (newExpertList.isNotEmpty() && existing.isNotEmpty()) {
            log.info("new List size " + newExpertList.size + "  existing list size" + existing.size)
            //newExpertList.removeAll(existing)
            val existingIds = existing.map { it.user.id }
            newExpertList = newExpertList.filter { it.user.id !in existingIds }.toMutableList()
        }
        caseManagerRepository.saveAll(newExpertList)
        return newExpertList
    }

    private fun getExperts(notifyPrimaryExpert: Boolean, experts: MutableList<Long>?): MutableList<UserView> {
        var requestExpertList1: MutableList<ExpertUserEntity>
        var responseList: MutableList<UserView> = mutableListOf()

        if (!experts.isNullOrEmpty()) {
            responseList = userViewRepository.findAllById(experts)
            requestExpertList1 = expertUserRepository.findAllById(experts)
            if (notifyPrimaryExpert) {
                val primaryExpertSet: MutableSet<ExpertUserEntity> = mutableSetOf()
                for (expert in requestExpertList1) {
                    if (expert.expertType == ExpertType.SECONDARY.name) {
                        val primaryExpert = expertUserRepository.findByCompanyProfileAndExpertType(
                            expert.companyProfile!!, ExpertType.PRIMARY.name
                        )
                        primaryExpertSet.add(primaryExpert)
                    }
                }
                responseList.addAll(userViewRepository.findAllById(primaryExpertSet.map { it.id }.toMutableList()))
            }
        }
        return responseList
    }

    private fun getUsers(experts: MutableList<Long>?): MutableList<UserView> {
        var requestExpertList: MutableList<UserView> = mutableListOf()
        if (!experts.isNullOrEmpty()) {
            requestExpertList = userViewRepository.findAllById(experts)
        }
        return requestExpertList
    }


    fun getPageRequest(
        download: Boolean,
        pageIndex: Int,
        pageSize: Int,
        sortBy: String,
        sort: String,
        role: String
    ): PageRequest {
        if (download) {
            return PageRequest.of(0, Integer.MAX_VALUE)
        }
        return PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
    }


    @Transactional
    fun deleteCase(caseId: Long, user: AuthenticatedUser): String {
        if (AdminAccessUtil.isPartner(user)) {
            //only root partner admin can delete case
            if (partnerRepository.findById(user.partnerId!!).get().rootUserId != user.userId) {
                throw ApplicationException(ErrorCode.CASE_DELETE_NOT_ALLOWED)
            }
        }
        try {
            val case = getDetails(caseId, user)
            caseRepository.delete(case)
            return AppConstant.SUCCESS_RESPONSE_STRING
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> {
                    if (ex.error == ErrorCode.INTERNAL_SERVER_ERROR) {
                        throw ApplicationException(ErrorCode.DELETE_CASE_FAIL)
                    } else {
                        throw ex
                    }
                }

                else -> {
                    ex.printStackTrace()
                    throw throw ApplicationException(ErrorCode.DELETE_CASE_FAIL)
                }
            }
        }
    }

    fun getCaseDocuments(case: CaseEntity): List<CaseDocumentsEntity> {
        val pageRequest = PageRequest.of(0, Integer.MAX_VALUE, SearchConstant.SORT_ORDER("id", "ASC"))
        return caseDocumentsRepository.findAllByCase(case, pageRequest)
    }

    fun getCaseStatusHistory(case: CaseEntity): MutableList<CaseStatusHistoryResponse> {
        val statusHistory = caseStatusHistoryRepository.findAllByCase(case)
        val historyList: MutableList<CaseStatusHistoryResponse> = mutableListOf()
        for (status in statusHistory) {
            val user = userProfileUtil.retrieveProfile(status.lastUpdatedBy)
            val caseStatus = CaseStatusHistoryResponse.ModelMapper.from(status, user)
            historyList.add(caseStatus)
        }
        return historyList
    }

    fun getCaseMilestones(case: CaseEntity): MutableList<CaseMilestoneResponse> {
        val caseMilestonesList = caseMilestonesRepository.findAllByCase(case)


        if (!case.isLegacy) {
            return taskWorkflowService.getWorkflowMilestones(case.id!!, "CASE")
        }

        val allMilestonesForSubcategory = milestonesRepository.findByCaseSubCategoryOrderBySequence(
            case.category?.subCategoryId!!
        )
        val milestonesList: MutableList<CaseMilestoneResponse> = mutableListOf()

        var completedSequence: Long? = null
        val milestonesMap = caseMilestonesList.groupBy { it.milestoneKey }
        for (milestone in allMilestonesForSubcategory) {
            if (milestone.sequence < 1) {
                continue
            }
            val milestoneKey = milestone.milestoneKey
            var user: UserProfile? = null
            var newMilestone: CaseMilestonesEntity
            if (milestonesMap.containsKey(milestoneKey)) {
                newMilestone = milestonesMap[milestoneKey]!![0]
                user = userProfileUtil.retrieveProfile(newMilestone.lastUpdatedBy)
            } else {
                newMilestone = CaseMilestonesEntity(
                    milestoneKey = milestone.milestoneKey,
                    case = case,
                    lastUpdatedBy = -1
                )
            }
            //Current case status
            if (milestone.status == case.status) {
                completedSequence = milestone.sequence
            }
            val milestoneResponse = CaseMilestoneResponse.ModelMapper.from(newMilestone, user, milestone.sequence)
            if (!milestonesList.any { it.milestoneKey == milestoneResponse.milestoneKey })
                milestonesList.add(milestoneResponse)
        }

        milestonesList.map { it.isCompleted = (completedSequence != null && it.sequence <= completedSequence) }
        return milestonesList

    }

    private fun getCaseAssigneeProfileList(caseEntity: CaseEntity): MutableList<UserProfile> {
        val assignees = caseAssigneeRepository.findAllByCase(caseEntity)
        return assignees.map { userProfileUtil.retrieveProfileWithUserType(it.expert, it.userType) }
            .toMutableList()
    }

    private fun getCaseManagerProfileList(caseEntity: CaseEntity): MutableList<UserProfile> {
        val assignees = caseManagerRepository.findAllByCase(caseEntity)
        return assignees.map { userProfileUtil.retrieveProfileWithUserType(it.user, it.userType) }.toMutableList()
    }

    fun getUserProfile(user: LoginAccountEntity?): UserProfile? {
        if (user?.id != null) {
            return userProfileUtil.retrieveProfile(user.id!!)
        }
        return null
    }

    fun documentExists(caseId: Long): Boolean {

        val case = caseRepository.getReferenceById(caseId)
        return caseDocumentsRepository.existsByCase(case)

    }

    @Transactional
    fun createCase(
        caseCategory: String,
        jsonObj: GenericCaseRequest,
        authenticatedUser: AuthenticatedUser
    ): Pair<List<Long?>, Boolean> {
        val (userId, partnerId) = getUserId(authenticatedUser, jsonObj)
        val accountId = jsonObj.additionalData?.get("accountId")?.longValue()
        val cases = createCase(
            caseCategory,
            jsonObj,
            userId,
            accountId,
            false,
            if (isExpert(authenticatedUser.role)) null else partnerId
        )
        val docExists = documentExists(cases[0].id!!)
        //create a group chat for case type
        cases.forEach {
            createGroupChat(it, userId, false)

            //case submit email
            val emails = super.getCorporateTeamEmails(it.createdBy?.userId)
            caseEmailService.caseSubmitEmail(it, false, emails)
        }
        return Pair(cases.map { it.id!! }, docExists)
    }

    private fun createGroupChat(case: CaseEntity, caseOwner: Long, isPublicCase: Boolean) {
        val participants = mutableListOf(caseOwner)
        participants.addAll(corporateUserService.getUserManagers(caseOwner))
        val caseId = case.id!!
        if (isPublicCase) {
            groupChatService.createGroupChatWithOwner(ChatType.CASE, caseId, participants, emptyList(), caseOwner)
        } else {
            groupChatService.createGroupChat(ChatType.CASE, caseId, participants, emptyList())

            // create applicant case group chat

            val applicantCaseParticipants = mutableListOf(caseOwner)
            applicantCaseParticipants.add(caseOwner)
            val caseManagersList = caseManagerRepository.findAllByCase(case)
            val caseManagers = caseManagersList.map { it.user.id!! }
            applicantCaseParticipants.addAll(caseManagers)
            val partnerEntity = case.partnerId?.let { partnerRepository.findByIdOrNull(it) }
            val virtualParticipants = groupChatService.getVirtualParticipants(partnerEntity)

            groupChatService.createGroupChat(ChatType.APPLICANT_CASE, caseId, applicantCaseParticipants, virtualParticipants)

            // create client case group chat

            val clientCaseParticipants = mutableListOf<Long>()
            clientCaseParticipants.addAll(caseManagers)
            clientCaseParticipants.addAll(corporateUserService.getUserManagers(caseOwner))
            clientCaseParticipants.addAll(corporateUserService.getCorporateSuperAdmins(caseOwner))

            groupChatService.createGroupChat(ChatType.CLIENT_CASE, caseId, clientCaseParticipants, virtualParticipants)
        }
    }

    @Transactional
    fun createCase(
        subCaseCategoryId: String, jsonObj: GenericCaseRequest, userId: Long, accountId: Long?,
        isPublicCase: Boolean = false, partnerId: Long?
    ): List<CaseEntity> {
        val mapper = jacksonObjectMapper()
        mapper.findAndRegisterModules()
        val parentCategoryId = jsonObj.additionalData?.get("parentCategoryId")!!.textValue()
        val additionalData = jsonObj.additionalData

        when (subCaseCategoryId) {
            "BUSINESS_INCORPORATION" -> {
                val caseEntity: BusinessIncorporationEntity =
                    mapper.convertValue(jsonObj.caseEntityData, BusinessIncorporationEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.newBranchCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )
            }

            "TAX_AND_ACCOUNTING" -> {
                val caseEntity: TaxAndAccountingEntity =
                    mapper.convertValue(jsonObj.caseEntityData, TaxAndAccountingEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.taxAccountingCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )

            }

            "GLOBAL_MOBILITY" -> {
                val caseEntity: GlobalMobilityEntity =
                    mapper.convertValue(jsonObj.caseEntityData, GlobalMobilityEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.globalMobilityCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )

            }

            "HR_AND_PAYROLL" -> {
                val caseEntity: HrAndPayrollEntity =
                    mapper.convertValue(jsonObj.caseEntityData, HrAndPayrollEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.hrAndPayrollCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )

            }

            "IMMIGRATION" -> {
                val caseEntity: ImmigrationEntity =
                    mapper.convertValue(jsonObj.caseEntityData, ImmigrationEntity::class.java)
                return saveCommonData(
                    userId,
                    caseEntity,
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.travellingToCountry,
                    caseEntity.fullNameOfTraveller,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData
                )
            }

            "INSURANCE" -> {
                val caseEntity: InsuranceEntity =
                    mapper.convertValue(jsonObj.caseEntityData, InsuranceEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.insuranceCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )
            }

            "LEGAL" -> {
                val caseEntity: LegalEntity = mapper.convertValue(jsonObj.caseEntityData, LegalEntity::class.java)
                return saveCommonData(
                    userId, caseEntity, subCaseCategoryId, parentCategoryId,
                    caseEntity.legalCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData
                )
            }
            /**---------------new case Category----------**/
            "ENTITY_SETUP" -> {
                val request: EntitySetup = mapper.convertValue(jsonObj.caseEntityData, EntitySetup::class.java)
                return saveEntitySetup(
                    request,
                    subCaseCategoryId,
                    parentCategoryId,
                    userId,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData
                )
            }

            "BANK_ACCOUNT" -> {
                val caseEntity: BankAccountEntity =
                    mapper.convertValue(jsonObj.caseEntityData, BankAccountEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "OFFICE_SPACE" -> {
                val caseEntity: OfficeAddressEntity =
                    mapper.convertValue(jsonObj.caseEntityData, OfficeAddressEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "SINGLE_VISA" -> {
                val singleVisa = mapper.convertValue(jsonObj.caseEntityData, SingleVisa::class.java)
                val caseEntity: SingleVisaEntity = mapper.convertValue(singleVisa, SingleVisaEntity::class.java)
                val genericCaseEntity: CaseEntity = setPersonalDetails(mapper, jsonObj, caseEntity)
                val caseList = mutableListOf<CaseEntity>()
                caseList.addAll(
                    saveCommonData(
                        userId,
                        genericCaseEntity,
                        subCaseCategoryId,
                        parentCategoryId,
                        caseEntity.caseCountry,
                        caseEntity.foreName + " " + caseEntity.surname,
                        isPublicCase,
                        accountId,
                        partnerId,
                        additionalData,
                        caseEntity.emailAddress
                    )
                )
                docRepoService.createCaseDocument(
                    caseList[0].id!!,
                    arrayOf("PASSPORT", "RESUME", "RESIDENCY", "SCOPE", "DEGREE"),
                    userId
                )
                //create dependents if any
                val dependentCases = mutableListOf<CaseEntity>()
                singleVisa.dependents.forEach {
                    dependentCases.add(
                        saveDependentVisaEntity(
                            it,
                            jsonObj,
                            parentCategoryId,
                            userId,
                            mapper,
                            isPublicCase,
                            accountId,
                            it.travelCountry!!,
                            singleVisa.applicantStayType,
                            singleVisa.applicantStay,
                            it.travelPurpose,
                            it.firstName + " " + it.lastName,
                            singleVisa.moreInformation,
                            null,
                            partnerId,
                            additionalData,
                            it.emailAddress
                        )
                    )
                }
                linkCases(caseList[0].id!!, dependentCases)
                linkDependents(dependentCases)
                caseList.addAll(dependentCases)
                return caseList
            }

            "COMPLEX_REQUIREMENT" -> {
                val caseEntity: ComplexRequirementEntity =
                    mapper.convertValue(jsonObj.caseEntityData, ComplexRequirementEntity::class.java)
                val caseList = saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.requirementCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase, accountId, partnerId, additionalData, caseEntity.personalDetails?.email
                )

                docRepoService.createCaseDocument(caseList[0].id!!, arrayOf("INITIAL"), userId)
                return caseList
            }

            "MULTIPLE_VISA" -> {
                val request: MultipleVisa = mapper.convertValue(jsonObj.caseEntityData, MultipleVisa::class.java)
                return saveMultipleVisa(
                    request,
                    jsonObj,
                    parentCategoryId,
                    userId,
                    mapper,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData
                )
            }

            "MANAGE_ASSIGNESS" -> {
                val caseEntity: ManageAssigneesEntity =
                    mapper.convertValue(jsonObj.caseEntityData, ManageAssigneesEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "MANAGE_PAYROLL" -> {
                val caseEntity: ManagePayrollEntity =
                    mapper.convertValue(jsonObj.caseEntityData, ManagePayrollEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "HR_EMP_SUPPORT" -> {
                val caseEntity: HrAndEmploymentSupportEntity =
                    mapper.convertValue(jsonObj.caseEntityData, HrAndEmploymentSupportEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )

            }

            "INTELLECTUAL_PROPERTY" -> {
                val caseEntity: IntellectualPropertyEntity =
                    mapper.convertValue(jsonObj.caseEntityData, IntellectualPropertyEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )

            }

            "DATA_PROTECTION" -> {
                val caseEntity: DataProtectionEntity =
                    mapper.convertValue(jsonObj.caseEntityData, DataProtectionEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )

            }

            "COM_LAW_CONTRACTS" -> {
                val caseEntity: CommercialSupportEntity =
                    mapper.convertValue(jsonObj.caseEntityData, CommercialSupportEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "RISK_MANAGEMENT" -> {
                val caseEntity: RiskManagementEntity =
                    mapper.convertValue(jsonObj.caseEntityData, RiskManagementEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email

                )
            }

            "EMP_BENEFITS_INSURANCE" -> {
                val caseEntity: EmployeeBenefitsAndInsuranceEntity =
                    mapper.convertValue(jsonObj.caseEntityData, EmployeeBenefitsAndInsuranceEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "TAX_SUPPORT" -> {
                val caseEntity: TaxSupportEntity =
                    mapper.convertValue(jsonObj.caseEntityData, TaxSupportEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )
            }

            "RIGHT_TO_WORK_CHECK" -> {
                val request: RightToWorkCheck =
                    mapper.convertValue(jsonObj.caseEntityData, RightToWorkCheck::class.java)
                val caseList = saveRightToWorkCheck(
                    request, jsonObj, subCaseCategoryId, parentCategoryId, userId, mapper,
                    isPublicCase, accountId, partnerId, additionalData
                )
                createCaseDocument(mapper, jsonObj, caseList[0].id!!, userId)
                return caseList
            }

            "LEGALISATION" -> {
                val caseEntity: LegalisationEntity =
                    mapper.convertValue(jsonObj.caseEntityData, LegalisationEntity::class.java)
                val caseList = saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.legalisedCountry, caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase, accountId, partnerId, additionalData,
                    caseEntity.emailAddress
                )

                createCaseDocument(mapper, jsonObj, caseList[0].id!!, userId)
                return caseList
            }

            "COMP_TAX_ASS" -> {
                val caseEntity: CompanyTaxAssessmentEntity =
                    mapper.convertValue(jsonObj.caseEntityData, CompanyTaxAssessmentEntity::class.java)
                return saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.personalDetails?.firstName + " " + caseEntity.personalDetails?.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.personalDetails?.email
                )

            }

            "INDVL_TAX_ASS" -> {
                val caseEntity: IndividualTaxAssessmentEntity =
                    mapper.convertValue(jsonObj.caseEntityData, IndividualTaxAssessmentEntity::class.java)
                val personalDetails = caseEntity.personalDetails ?: CaseContactInformation()
                personalDetails.country = caseEntity.country ?: personalDetails.country
                personalDetails.companyName = caseEntity.accountName
                caseEntity.personalDetails = personalDetails

                val caseList = saveCommonData(
                    userId,
                    caseEntity,
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.caseCountry,
                    caseEntity.foreName + " " + caseEntity.surname,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.emailAddress
                )
                createCaseDocument(mapper, jsonObj, caseList[0].id!!, userId)
                return caseList
            }

            "DEPENDENT_VISA" -> {
                val request: DependentVisa = mapper.convertValue(jsonObj.caseEntityData, DependentVisa::class.java)
                return saveDependentVisa(
                    request,
                    jsonObj,
                    parentCategoryId,
                    userId,
                    mapper,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData
                )
            }

            "BUSINESS_VISA" -> {
                val caseEntity: BusinessVisaEntity =
                    mapper.convertValue(jsonObj.caseEntityData, BusinessVisaEntity::class.java)
                val caseList = saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.destinationCountry,
                    caseEntity.applicant!!.firstName + " " + caseEntity.applicant!!.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.applicant!!.emailAddress
                )
                docRepoService.createCaseDocument(caseList[0].id!!, arrayOf("PASSPORT"), userId)
                return caseList
            }

            "EMPLOYEE_OF_RECORD" -> {
                val request: EmployeeOfRecord =
                    mapper.convertValue(jsonObj.caseEntityData, EmployeeOfRecord::class.java)
                val caseList = saveEmployeeOfRecord(
                    request, jsonObj, subCaseCategoryId, parentCategoryId, userId, mapper,
                    isPublicCase, accountId, partnerId
                )
                createCaseDocument(mapper, jsonObj, caseList[0].id!!, userId)
                return caseList
            }

            "EXPATRIATE_TAX_SERVICES" -> {
                val caseEntity: ExpatriateTaxServicesEntity =
                    mapper.convertValue(jsonObj.caseEntityData, ExpatriateTaxServicesEntity::class.java)

                return saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.hostCountry, caseEntity.applicant.firstName + " " + caseEntity.applicant.lastName,
                    isPublicCase, accountId, partnerId, additionalData,caseEntity.applicant.emailAddress
                )
            }

            "TAX_RETURN_PREPARATION" -> {
                val caseEntity: TaxReturnPreparationEntity =
                    mapper.convertValue(jsonObj.caseEntityData, TaxReturnPreparationEntity::class.java)

                return saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.hostCountry, caseEntity.applicant.firstName + " " + caseEntity.applicant.lastName,
                    isPublicCase, accountId, partnerId, additionalData, caseEntity.applicant.emailAddress
                )
            }

            "REMOTE_WORK_ASSESSMENT" -> {
                val caseEntity: RemoteWorkAssessmentEntity =
                    mapper.convertValue(jsonObj.caseEntityData, RemoteWorkAssessmentEntity::class.java)

                return saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.hostCountry, caseEntity.applicant.firstName + " " + caseEntity.applicant.lastName,
                    isPublicCase, accountId, partnerId, additionalData,caseEntity.applicant.emailAddress
                )
            }

            "POSTED_WORKER_NOTIFICATION" -> {
                val caseEntity: PostedWorkerNotificationEntity =
                    mapper.convertValue(jsonObj.caseEntityData, PostedWorkerNotificationEntity::class.java)

                return saveCommonData(
                    userId, setPersonalDetails(mapper, jsonObj, caseEntity), subCaseCategoryId, parentCategoryId,
                    caseEntity.toCountry, caseEntity.applicant.firstName + " " + caseEntity.applicant.lastName,
                    isPublicCase, accountId, partnerId, additionalData,caseEntity.applicant.emailAddress
                )
            }

            "ENTRY_VISA" -> {
                val caseEntity: EntryVisaEntity =
                    mapper.convertValue(jsonObj.caseEntityData, EntryVisaEntity::class.java)
                val caseList = saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.destinationCountry,
                    caseEntity.applicant!!.firstName + " " + caseEntity.applicant!!.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.applicant!!.emailAddress
                )
                docRepoService.createCaseDocument(caseList[0].id!!, arrayOf("PASSPORT"), userId)
                return caseList
            }

            "RULING_30_PERCENT" -> {
                val caseEntity: Ruling30PercentEntity =
                    mapper.convertValue(jsonObj.caseEntityData, Ruling30PercentEntity::class.java)
                val caseList = saveCommonData(
                    userId,
                    setPersonalDetails(mapper, jsonObj, caseEntity),
                    subCaseCategoryId,
                    parentCategoryId,
                    caseEntity.destinationCountry,
                    caseEntity.firstName + " " + caseEntity.lastName,
                    isPublicCase,
                    accountId,
                    partnerId,
                    additionalData,
                    caseEntity.emailAddress
                )
                docRepoService.createCaseDocument(caseList[0].id!!,
                    arrayOf("PASSPORT", "SIGNED_EMPLOYMENT_CONTRACT", "COMPLETED_ADDENDUM"), userId)
                return caseList
            }

            else -> throw ApplicationException(ErrorCode.NOT_FOUND)
        }
    }

    private fun linkDependents(dependentCases: List<CaseEntity>) {
        // Get all possible unique pairs from dependents
        val pairs = mutableListOf<Pair<CaseEntity, CaseEntity>>()
        for (i in 0 until dependentCases.size - 1) {
            for (j in i + 1 until dependentCases.size) {
                pairs.add(dependentCases[i] to dependentCases[j])
            }
        }
        //group by Pair.first
        val groupMap = pairs.groupBy { it.first.id!! }
        groupMap.forEach {
            val linkFrom = it.key
            val linkCases = it.value.map { it1 -> it1.second }
            linkCases(linkFrom, linkCases)
        }
    }

    @Transactional
    fun linkCase(linkFrom: Long, linkTo: List<Long>, authenticatedUser: AuthenticatedUser): Boolean {
        val updatedLinkTo = mutableListOf<CaseEntity>()
        linkTo.forEach {
            //check if case is already linked?
            if (caseRepository.countLinks(linkFrom, it) > 0) {
                log.warn("The case id: $linkFrom and $it are already linked.")
                //already linked so skip this case
                return@forEach
            }
            val linkedCase = getDetails(it, authenticatedUser)
            linkedCase?.let { it1 -> updatedLinkTo.add(it1) }
        }

        if (updatedLinkTo.isEmpty()) {
            return false
        }
        return linkCases(linkFrom, updatedLinkTo, authenticatedUser)
    }

    @Transactional
    private fun linkCases(
        linkFrom: Long,
        linkCases: List<CaseEntity>,
        authenticatedUser: AuthenticatedUser? = null
    ): Boolean {
        if (linkCases.isEmpty()) {
            return false
        }
        val case = if (authenticatedUser != null) {
            getDetails(linkFrom, authenticatedUser)
        } else {
            caseRepository.findById(linkFrom).orElseThrow { ApplicationException(ErrorCode.CASE_NOT_FOUND) }
        }
        case.linkedCases.addAll(linkCases)
        caseRepository.save(case)
        return true
    }

    @Transactional
    fun unlinkCase(linkFrom: Long, linkCase: List<Long>): Boolean {
        var removedLinksCount = 0
        linkCase.forEach {
            removedLinksCount += caseRepository.unlinkCases(linkFrom, it) ?: 0
        }
        log.info("Removed $removedLinksCount link(s) from case id: $linkFrom")

        return removedLinksCount > 0
    }

    private fun saveDependentVisaEntity(
        dependent: Dependent,
        jsonObj: GenericCaseRequest,
        parentCategoryId: String,
        userId: Long,
        mapper: ObjectMapper,
        isPublicCase: Boolean,
        accountId: Long?,
        travelCountry: String,
        applicantStayType: String?,
        applicantStay: Long?,
        travelPurpose: String?,
        initiatedFor: String?,
        moreInformation: String?,
        aliasName: String? = null,
        partnerId: Long?,
        additionalData: JsonNode?,
        email: String? = null
    ): DependentVisaEntity {

        val caseEntity = DependentVisaEntity(
            dependent.firstName, dependent.middleName, dependent.lastName,
            dependent.relationWithPrimary!!, dependent.nationality!!, dependent.residenceCountry,
            dependent.contactNo, dependent.emailAddress, dependent.shareApplicantInfo,
            applicantStayType, applicantStay, travelPurpose,
            dependent.childAge, travelCountry
        )

        setPersonalDetails(mapper, jsonObj, caseEntity)

        caseEntity.caseFees = CaseFees()
        caseEntity.uuid = UUID.randomUUID().toString()
        caseEntity.category =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId("DEPENDENT_VISA", parentCategoryId)
        caseEntity.createdBy = clientViewRepository.getReferenceById(userId)
        caseEntity.initiatedFor = initiatedFor
        caseEntity.email = email
        caseEntity.moreInformation = moreInformation
        caseEntity.parentCategoryId = parentCategoryId
        caseEntity.actionFor = caseStatusMasterRepository.findBySubCategoryAndStatus(
            caseEntity.category?.subCategoryId!!,
            caseEntity.status
        ).actionFor
        caseEntity.account = accountEntityRepository.getReferenceById(accountId!!)
        caseEntity.aliasName = aliasName
        caseEntity.country = travelCountry
        caseEntity.partnerId = partnerId
        caseEntity.notifyCaseOwner = additionalData?.get("notifyCaseOwner")!!.booleanValue()
        partnerId?.let { caseEntity.managedBy = partnerRepository.findById(it).get().casesManaged }

        val savedCase = caseRepository.save(caseEntity)

        if (dependent.relationWithPrimary == "CHILD")
            docRepoService.createCaseDocument(savedCase.id, arrayOf("BIRTH", "PASSPORT"), userId)
        if (dependent.relationWithPrimary == "MARRIED_PARTNER")
            docRepoService.createCaseDocument(savedCase.id, arrayOf("MARRIAGE", "PASSPORT"), userId)
        if (dependent.relationWithPrimary == "UNMARRIED_PARTNER")
            docRepoService.createCaseDocument(savedCase.id, arrayOf("PASSPORT"), userId)
        if (dependent.relationWithPrimary == "OTHER")
            docRepoService.createCaseDocument(savedCase.id, arrayOf("PASSPORT"), userId)

        return savedCase
    }

    private fun createCaseDocument(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        caseId: Long,
        userId: Long
    ) {
        val docTypes: Array<String>? =
            mapper.convertValue(jsonObj.additionalData?.get("docTypes"), Array<String>::class.java)
        if (!docTypes.isNullOrEmpty()) {
            val documentsRequest = docTypes.map {
                var otherDocName = jsonObj.additionalData?.get("otherDocTypeName")?.textValue()
                DocumentRequest(
                    documentCode = it,
                    otherDocumentName = otherDocName,
                    status = "REQUESTED",
                    requestedDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                    receivedDate = null,
                    verifiedDate = null,
                    apostilledDate = null,
                    displayOrder = null,
                    docSize = null,
                    expiryDate = null
                )
            }
            docRepoService.caseDocuments(caseId, documentsRequest, userId)
        }
    }


    private fun saveRightToWorkCheck(
        request: RightToWorkCheck,
        jsonObj: GenericCaseRequest,
        subCaseCategoryId: String,
        parentCategoryId: String,
        userId: Long,
        mapper: ObjectMapper,
        isPublicCase: Boolean = false,
        accountId: Long?,
        partnerId: Long?,
        additionalData: JsonNode?
    ): List<CaseEntity> {

        val caseEntity = RightToWorkCheckEntity(
            request.nationality,
            request.caseCountry,
            request.documentType,
            request.timeToWorkLeft
        )
        setPersonalDetails(mapper, jsonObj, caseEntity)

        caseEntity.category =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(subCaseCategoryId, parentCategoryId)
        caseEntity.actionFor =
            caseStatusMasterRepository.findBySubCategoryAndStatus(subCaseCategoryId, caseEntity.status).actionFor
        caseEntity.createdBy = clientViewRepository.getReferenceById(userId)
        caseEntity.country = request.caseCountry
        caseEntity.initiatedFor = request.applicantInfo[0].firstName + " " + request.applicantInfo[0].lastName
        caseEntity.moreInformation = request.moreInformation
        caseEntity.parentCategoryId = parentCategoryId
        caseEntity.uuid = UUID.randomUUID().toString()
        caseEntity.account = accountId.let { accountEntityRepository.getReferenceById(accountId!!) }
        val caseFees = CaseFees()
        caseEntity.caseFees = caseFees
        caseEntity.partnerId = partnerId
        caseEntity.notifyCaseOwner = additionalData?.get("notifyCaseOwner")!!.booleanValue()
        caseEntity.assessmentId = additionalData.get("assessmentId")?.asLong()
        partnerId?.let { caseEntity.managedBy = partnerRepository.findById(it).get().casesManaged }
        val savedCase = caseRepository.save(caseEntity)

        val applicantInfoList = ArrayList<ApplicantInfoEntity>()
        for (applicantInfo in request.applicantInfo) {
            val applicantInfoEntity = ApplicantInfoEntity(
                applicantInfo.firstName, applicantInfo.middleName,
                applicantInfo.lastName, applicantInfo.companyName,
                applicantInfo.shareApplicantInfo, applicantInfo.contactNo,
                applicantInfo.emailAddress, savedCase
            )
            applicantInfoList.add(applicantInfoEntity)
        }

        applicantInfoRepository.saveAll(applicantInfoList)
        return mutableListOf(savedCase)
    }

    private fun saveEmployeeOfRecord(
        request: EmployeeOfRecord,
        jsonObj: GenericCaseRequest,
        subCaseCategoryId: String,
        parentCategoryId: String,
        userId: Long,
        mapper: ObjectMapper,
        isPublicCase: Boolean = false,
        accountId: Long?,
        partnerId: Long?
    ): List<CaseEntity> {

        val caseEntity = EmployeeOfRecordEntity(
            request.destinationCountry,
            request.entityInDestinationCountry,
            TimeUtil.fromInstantMillis(request.hiringDate),
            request.rightToWork,
            request.signContract,
            request.employmentTypes,
            request.internationalTravel,
            request.labourSafety,
            request.certificationRequired,
            request.governmentContract
        )
        setPersonalDetails(mapper, jsonObj, caseEntity)

        caseEntity.category =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(subCaseCategoryId, parentCategoryId)
        caseEntity.actionFor =
            caseStatusMasterRepository.findBySubCategoryAndStatus(subCaseCategoryId, caseEntity.status).actionFor
        caseEntity.createdBy = clientViewRepository.getReferenceById(userId)
        caseEntity.country = request.destinationCountry
        caseEntity.initiatedFor = request.applicants[0].firstName + " " + request.applicants[0].lastName
//        caseEntity.email = request.applicants[0].emailAddress

        caseEntity.moreInformation = request.moreInformation
        caseEntity.parentCategoryId = parentCategoryId
        caseEntity.uuid = UUID.randomUUID().toString()
        caseEntity.account = accountId.let { accountEntityRepository.getReferenceById(accountId!!) }
        val caseFees = CaseFees()
        caseEntity.caseFees = caseFees
        caseEntity.partnerId = partnerId
        partnerId?.let { caseEntity.managedBy = partnerRepository.findById(it).get().casesManaged }
        caseEntity.notifyCaseOwner = jsonObj.additionalData?.get("notifyCaseOwner")!!.booleanValue()
        val savedCase = caseRepository.save(caseEntity)

        val applicantInfoList = ArrayList<EmployeeRecordApplicantEntity>()
        for (applicantInfo in request.applicants) {
            val applicantInfoEntity = EmployeeRecordApplicantEntity(
                applicantInfo.firstName,
                applicantInfo.lastName, applicantInfo.residenceCountry,
                applicantInfo.jobTitle, applicantInfo.salaryCurrency,
                applicantInfo.salary, savedCase
            )
            applicantInfoList.add(applicantInfoEntity)
        }

        employeeOfRecordApplicantRepository.saveAll(applicantInfoList)
        return mutableListOf(savedCase)
    }

    private fun saveMultipleVisa(
        request: MultipleVisa, jsonObj: GenericCaseRequest,
        parentCategoryId: String, userId: Long, mapper: ObjectMapper, isPublicCase: Boolean = false,
        accountId: Long?, partnerId: Long?, additionalData: JsonNode?
    ): List<CaseEntity> {
        val category =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId("SINGLE_VISA", parentCategoryId)
        val caseList: MutableList<CaseEntity> = mutableListOf()
        for (traveller in request.traveller) {

            val caseOwner = traveller.applicantUserId ?: userId
            val accountId = traveller.applicantAccountId ?: accountId!!

            val caseEntity = SingleVisaEntity(
                request.visaType, traveller.nationality, request.caseCountry,
                traveller.travelCountry, null, traveller.firstName!!,
                null, traveller.lastName!!, null,
                traveller.dependantTravelling, traveller.durationOfStay,
                traveller.estimatedSalary, null, null, false,
                traveller.duties, traveller.qualification, traveller.durationOfStayType,
                traveller.currencySymbol, traveller.accountName, traveller.shareApplicantInfo,
                traveller.contactNo, traveller.emailAddress, null, null
            )

            setPersonalDetails(mapper, jsonObj, caseEntity)
            caseEntity.personalDetails?.companyName = traveller.accountName
            caseEntity.category = category
            val createdBy = clientViewRepository.getReferenceById(caseOwner)
            caseEntity.createdBy = createdBy
            caseEntity.country = request.caseCountry
            caseEntity.initiatedFor = traveller.firstName!! + " " + traveller.lastName!!
            caseEntity.moreInformation = request.moreInformation
            caseEntity.parentCategoryId = parentCategoryId

            caseEntity.actionFor =
                caseStatusMasterRepository.findBySubCategoryAndStatus("SINGLE_VISA", caseEntity.status).actionFor
            caseEntity.account = accountEntityRepository.getReferenceById(accountId)
            caseEntity.partnerId = partnerId
            caseEntity.notifyCaseOwner = additionalData?.get("notifyCaseOwner")!!.booleanValue()
            val savedCase = caseRepository.save(caseEntity)
            docRepoService.createCaseDocument(
                savedCase.id,
                arrayOf("PASSPORT", "RESUME", "RESIDENCY", "SCOPE", "DEGREE"),
                userId
            )
            caseList.add(savedCase)
        }
        return caseList
    }

    private fun saveDependentVisa(
        request: DependentVisa, jsonObj: GenericCaseRequest,
        parentCategoryId: String, userId: Long, mapper: ObjectMapper, isPublicCase: Boolean = false,
        accountId: Long?, partnerId: Long?,
        additionalData: JsonNode?
    ): MutableList<CaseEntity> {
        val caseList = mutableListOf<CaseEntity>()
        request.dependents.forEach {
            val initiatedFor = it.firstName + " " + it.lastName
            val savedCase = saveDependentVisaEntity(
                it,
                jsonObj,
                parentCategoryId,
                userId,
                mapper,
                isPublicCase,
                accountId,
                request.travelCountry,
                request.applicantStayType,
                request.applicantStay,
                request.travelPurpose,
                initiatedFor,
                request.moreInformation,
                null,
                partnerId,
                additionalData,
                it.emailAddress
            )
            caseList.add(savedCase)
        }
        // add links among dependents
        linkDependents(caseList)
        return caseList
    }

    private fun saveEntitySetup(
        request: EntitySetup, subCaseCategoryId: String, parentCategoryId: String, userId: Long,
        isPublicCase: Boolean = false,
        accountId: Long?,
        partnerId: Long?,
        additionalData: JsonNode?
    ): List<CaseEntity> {
        val case = toEntitySetupEntity(request)

        case.category =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(subCaseCategoryId, parentCategoryId)
        case.actionFor = caseStatusMasterRepository.findBySubCategoryAndStatus(subCaseCategoryId, case.status).actionFor
        case.createdBy = clientViewRepository.getReferenceById(userId)
        case.personalDetails?.companyName = case.createdBy?.company
        case.country = request.caseCountry
        case.initiatedFor = request.directors[0].fullName
        case.moreInformation = request.moreInformation
        case.parentCategoryId = parentCategoryId
        case.uuid = UUID.randomUUID().toString()
        case.account = accountId.let { accountEntityRepository.getReferenceById(accountId!!) }
        case.notifyCaseOwner = additionalData?.get("notifyCaseOwner")!!.booleanValue()

        val caseFees = CaseFees()
        case.caseFees = caseFees
        case.partnerId = partnerId
        partnerId?.let { case.managedBy = partnerRepository.findById(it).get().casesManaged }

        val savedCase = caseRepository.save(case)

        val directorList = ArrayList<DirectorEntity>()
        val shareholderList = ArrayList<ShareholderEntity>()

        for (director in request.directors) {
            val directorEntity = DirectorEntity(
                director.fullName, director.dob, director.occupation,
                director.nationality, director.residentialAddress, director.serviceAddress, savedCase
            )
            directorList.add(directorEntity)
        }
        for (shareholder in request.shareholders) {
            val shareholderEntity = ShareholderEntity(
                shareholder.fullName, shareholder.percentageShare, shareholder.serviceAddress, savedCase
            )
            shareholderList.add(shareholderEntity)
        }

        directorRepository.saveAll(directorList)
        shareholderRepository.saveAll(shareholderList)
        return mutableListOf(savedCase)
    }

    private fun toEntitySetupEntity(request: EntitySetup): EntitySetupEntity {
        val companySecretary = CompanySecretary(
            request.companySecretary.fullName,
            request.companySecretary.dob,
            request.companySecretary.occupation,
            request.companySecretary.nationality,
            request.companySecretary.residentialAddress,
            request.companySecretary.serviceAddress
        )

        return EntitySetupEntity(
            request.caseCountry,
            request.entityType,
            request.setupCompanyName,
            request.businessActivities,
            request.hqCountry,
            request.hqState,
            request.hqCity,
            request.hqZipcode,
            request.hqAddress,
            request.foreignOwned,
            request.requireAddress,
            request.supportTaxRegistration,
            request.otherInformation,
            companySecretary
        )
    }

    private fun setPersonalDetails(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        caseEntity: CaseEntity,
        country: String?,
        initiatedFor: String?
    ): CaseEntity {
        val personalDetails: PersonalDetails =
            mapper.convertValue(jsonObj.additionalData?.get("personalDetails"), PersonalDetails::class.java)
        if (personalDetails.firstName == null || personalDetails.lastName == null || personalDetails.email == null ||
            personalDetails.country == null
        ) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
        caseEntity.initiatedFor = initiatedFor ?: (personalDetails.firstName + " " + personalDetails.lastName)
        caseEntity.country = country

        caseEntity.personalDetails = CaseContactInformation(
            personalDetails.firstName,
            personalDetails.lastName,
            personalDetails.title,
            personalDetails.email,
            personalDetails.contactNumber,
            personalDetails.country,
            personalDetails.occupation,
            personalDetails.companyName
        )
        return caseEntity
    }

    private fun setPersonalDetails(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        caseEntity: CaseEntity
    ): CaseEntity {
        val personalDetails: PersonalDetails =
            mapper.convertValue(jsonObj.additionalData?.get("personalDetails"), PersonalDetails::class.java)
        if (personalDetails.firstName == null || personalDetails.lastName == null || personalDetails.email == null ||
            personalDetails.country == null
        ) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        caseEntity.personalDetails = CaseContactInformation(
            personalDetails.firstName,
            personalDetails.lastName,
            personalDetails.title,
            personalDetails.email,
            personalDetails.contactNumber,
            personalDetails.country,
            personalDetails.occupation,
            personalDetails.companyName
        )
        return caseEntity
    }

    private fun getUserId(authenticatedUser: AuthenticatedUser, jsonObj: GenericCaseRequest): Pair<Long, Long?> {
        if (isAdmin(authenticatedUser) || canCreateCase(
                jsonObj.additionalData?.get("caseOwner")?.toString()?.toLong(),
                authenticatedUser
            )
        ) {
            val caseOwner = jsonObj.additionalData?.get("caseOwner")
            return caseOwner.toString().toLong() to authenticatedUser.partnerId
        }
        return authenticatedUser.userId to authenticatedUser.partnerId
    }

    fun canCreateCase(userId: Long?, requestedByUser: AuthenticatedUser): Boolean {
        if (userId == null)
            return false
        val corporateUser =
            corporateUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.BAD_REQUEST) }
        if (corporateUser.corporate.id == requestedByUser.companyId) {
            val accesses = requestedByUser.visibilities.filter { it.feature == "USER" }

            if (accesses.isNotEmpty()) {
                val access = accesses[0]
                when {
                    access.accesses.contains("FULL") -> {
                        //caseCreated and current user belongs to same corporate
                        return true
                    }

                    access.accesses.contains("REPORTEES") -> {
                        // Case createdBy is reportees of current user
                        return corporateUserRepository.findAllByManagersManagerIdAndManagersCorporateUser(
                            requestedByUser.userId, corporateUser
                        ).isPresent
                    }

                    access.accesses.contains("OWN") -> {
                        //OWN cases
                        return requestedByUser.userId == userId
                    }
                }
            }
        }
        if (AdminAccessUtil.isPartner(requestedByUser)) {
            return partnerRepository.findById(requestedByUser.partnerId!!).get().corporates.map { it.id }
                .contains(corporateUser.corporate.id)
        }
        return false
    }

    private fun saveCommonData(
        userId: Long, case: CaseEntity, subCaseCategoryId: String?, parentCategoryId: String,
        country: String?, initiatedFor: String?, isPublicCase: Boolean = false,
        accountId: Long?, partnerId: Long?, additionalData: JsonNode?, email: String?=null
    ): MutableList<CaseEntity> {

        if (subCaseCategoryId!=null) {
            case.category =
                caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId(
                    subCaseCategoryId,
                    parentCategoryId
                )
            case.actionFor = caseStatusMasterRepository.findBySubCategoryAndStatus(subCaseCategoryId, case.status).actionFor
        }
        case.parentCategoryId = parentCategoryId
        case.createdBy = clientViewRepository.getReferenceById(userId)
        case.country = country
        case.initiatedFor = initiatedFor
        case.email=email

        case.uuid = UUID.randomUUID().toString()
        
        val caseFees = CaseFees()
        case.caseFees = caseFees

        case.account = accountId.let { accountEntityRepository.getReferenceById(it!!) }
        case.partnerId = partnerId
        case.notifyCaseOwner = additionalData?.get("notifyCaseOwner")!!.booleanValue()
        case.assessmentId = additionalData.get("assessmentId")?.asLong()
        partnerId?.let { case.managedBy = partnerRepository.findById(it).get().casesManaged }
        val savedCase = caseRepository.save(case)
        return mutableListOf(savedCase)
    }

    fun updateCase(caseId: Long, jsonObj: GenericCaseRequest, authenticatedUser: AuthenticatedUser): CaseEntity {
        val case = getDetails(caseId, authenticatedUser)
        return updateCaseEntity(case.category?.subCategoryId!!, jsonObj, case)
    }

    private fun updateCaseEntity(subCategoryId: String, jsonObj: GenericCaseRequest, case: CaseEntity): CaseEntity {
        val mapper = jacksonObjectMapper()
        mapper.findAndRegisterModules()
        when (subCategoryId) {
            "BUSINESS_INCORPORATION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, BusinessIncorporationEntity::class.java)
                val caseEntity = case as BusinessIncorporationEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.companyHqCountry = request.companyHqCountry
                caseEntity.companyHqAddress = request.companyHqAddress
                caseEntity.newBranchCountry = request.newBranchCountry
                caseEntity.newBranchAddress = request.newBranchAddress
                caseEntity.typeOfBusiness = request.typeOfBusiness
                caseEntity.otherDetails = request.otherDetails
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.newBranchCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "TAX_AND_ACCOUNTING" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, TaxAndAccountingEntity::class.java)
                val caseEntity = case as TaxAndAccountingEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.taxAccountingCountry = request.taxAccountingCountry
                caseEntity.queryDetails = request.queryDetails
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.taxAccountingCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "GLOBAL_MOBILITY" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, GlobalMobilityEntity::class.java)
                val caseEntity = case as GlobalMobilityEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.globalMobilityCountry = request.globalMobilityCountry
                caseEntity.relocatingCountry = request.relocatingCountry
                caseEntity.agreeTermOfUse = request.agreeTermOfUse
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.globalMobilityCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "HR_AND_PAYROLL" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, HrAndPayrollEntity::class.java)
                val caseEntity = case as HrAndPayrollEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.hrAndPayrollCountry = request.hrAndPayrollCountry
                caseEntity.queryDetails = request.queryDetails
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.hrAndPayrollCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "IMMIGRATION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, ImmigrationEntity::class.java)
                val caseEntity = case as ImmigrationEntity
                caseEntity.applicantNationality = request.applicantNationality
                caseEntity.applyingFromCountry = request.applyingFromCountry
                caseEntity.travellingToCountry = request.travellingToCountry
                caseEntity.purposeOfTrip = request.purposeOfTrip
                caseEntity.dateOfEntry = request.dateOfEntry
                caseEntity.noOfTimesEnterIntoTheCountry = request.noOfTimesEnterIntoTheCountry
                caseEntity.expectedVisaPeriod = request.expectedVisaPeriod
                caseEntity.fullNameOfTraveller = request.fullNameOfTraveller
                caseEntity.companyName = request.companyName
                caseEntity.companyAddress = request.companyAddress
                caseEntity.contactName = request.contactName
                caseEntity.contactEmail = request.contactEmail
                caseEntity.anyDependants = request.anyDependants
                caseEntity.noOfDependants = request.noOfDependants
                caseEntity.otherDetails = request.otherDetails
                caseEntity.covidVaccinated = request.covidVaccinated
                caseEntity.vaccineName = request.vaccineName
                caseEntity.otherPurpose = request.otherPurpose
                caseEntity.otherVaccine = request.otherVaccine
                caseEntity.detailAbout = request.detailAbout
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.travellingToCountry,
                    request.fullNameOfTraveller
                )
                return caseRepository.save(caseEntity)
            }

            "INSURANCE" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, InsuranceEntity::class.java)
                val caseEntity = case as InsuranceEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.insuranceCountry = request.insuranceCountry
                caseEntity.queryDetails = request.queryDetails
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.insuranceCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "LEGAL" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, LegalEntity::class.java)
                val caseEntity = case as LegalEntity
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.companyName = request.companyName
                caseEntity.areaOfSupport = request.areaOfSupport
                caseEntity.legalCountry = request.legalCountry
                caseEntity.queryDetails = request.queryDetails
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.legalCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }
            /**---------------new case Category----------**/
            "ENTITY_SETUP" -> {
                return updateEntitySetup(mapper, jsonObj, case)
            }

            "BANK_ACCOUNT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, BankAccountEntity::class.java)
                val caseEntity = case as BankAccountEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.entityType = request.entityType
                caseEntity.trading = request.trading
                caseEntity.businessActivity = request.businessActivity
                caseEntity.fullTime = request.fullTime
                caseEntity.partTime = request.partTime
                caseEntity.nonContractWorkers = request.nonContractWorkers
                caseEntity.expectedTurnover = request.expectedTurnover
                caseEntity.taxStatus = request.taxStatus
                caseEntity.incomeCome = request.incomeCome
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "OFFICE_SPACE" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, OfficeAddressEntity::class.java)
                val caseEntity = case as OfficeAddressEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.assistanceFindingOffice = request.assistanceFindingOffice
                caseEntity.contract = request.contract
                caseEntity.spaceType = request.spaceType
                caseEntity.seatingSpace = request.seatingSpace
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "SINGLE_VISA" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, SingleVisaEntity::class.java)
                val caseEntity = case as SingleVisaEntity
                caseEntity.visaType = request.visaType
                caseEntity.nationality = request.nationality
                caseEntity.caseCountry = request.caseCountry
                caseEntity.fromCountry = request.fromCountry
                caseEntity.tripPurpose = request.tripPurpose
                caseEntity.foreName = request.foreName
                caseEntity.middleName = request.middleName
                caseEntity.surname = request.surname
                caseEntity.covidVaccinated = request.covidVaccinated
                caseEntity.dependantTravelling = request.dependantTravelling
                caseEntity.applicantStay = request.applicantStay
                caseEntity.estimatedSalary = request.estimatedSalary
                caseEntity.estimatedSalaryRange = request.estimatedSalaryRange
                caseEntity.paidCountry = request.paidCountry
                caseEntity.entityInHostCountry = request.entityInHostCountry
                caseEntity.duties = request.duties
                caseEntity.qualification = request.qualification
                caseEntity.moreInformation = request.moreInformation
                caseEntity.currencySymbol = request.currencySymbol
                caseEntity.shareApplicantInfo = request.shareApplicantInfo
                caseEntity.contactNo = request.contactNo
                caseEntity.emailAddress = request.emailAddress
                caseEntity.applicantStayType = request.applicantStayType
                caseEntity.accountName = request.accountName
                caseEntity.jobTitle = request.jobTitle
                caseEntity.jobStartDate = request.jobStartDate
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.caseCountry,
                    caseEntity.foreName + " " + caseEntity.surname
                )
                caseEntity.initiatedFor = request.foreName + " " + request.surname
                caseEntity.country = request.caseCountry
                if (request.shareApplicantInfo == true) {
                    caseEntity.email = request.emailAddress
                }
                return caseRepository.save(caseEntity)
            }

            "MULTIPLE_VISA" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, MultipleVisa::class.java)
                val caseEntity = case as MultipleVisaEntity
                caseEntity.visaType = request.visaType
                caseEntity.caseCountry = request.caseCountry
                caseEntity.numberVisaPermit = request.numberVisaPermit
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity)

                val travellerList: MutableList<TravellerEntity> = mutableListOf()
                val removeList = caseEntity.traveller.map { it.id }.toMutableList()
                log.info("Current DB Travellers $removeList")

                request.traveller.forEach { requestTraveller ->

                    if (requestTraveller.id != null) {
                        log.info("process for traveller id ${requestTraveller.id}")
                        val travellerEntityOpt = travellerRepository.findById(requestTraveller.id!!)
                        if (travellerEntityOpt.isPresent) {
                            log.info("record found for traveller id ${requestTraveller.id}")
                            val travellerEntity: TravellerEntity = travellerEntityOpt.get()
                            removeList.remove(requestTraveller.id)
                            travellerEntity.fullName = requestTraveller.fullName!!
                            travellerEntity.nationality = requestTraveller.nationality
                            travellerEntity.travelCountry = requestTraveller.travelCountry
                            travellerEntity.estimatedSalary = requestTraveller.estimatedSalary
                            travellerEntity.durationOfStay = requestTraveller.durationOfStay
                            travellerEntity.durationOfStayType = requestTraveller.durationOfStayType
                            travellerEntity.duties = requestTraveller.duties
                            travellerEntity.qualification = requestTraveller.qualification
                            travellerEntity.dependantTravelling = requestTraveller.dependantTravelling
                            travellerEntity.dependantDurationOfStay = requestTraveller.dependantDurationOfStay
                            travellerEntity.currencySymbol = requestTraveller.currencySymbol
                            travellerEntity.accountName = requestTraveller.accountName
                            travellerEntity.shareApplicantInfo = requestTraveller.shareApplicantInfo
                            travellerEntity.contactNo = requestTraveller.contactNo
                            travellerEntity.emailAddress = requestTraveller.emailAddress
                            travellerList.add(travellerEntity)
                        } else {
                            val createTravellerEntity = toTravellerEntity(requestTraveller, caseEntity)
                            travellerList.add(createTravellerEntity)
                        }
                    } else {
                        log.info("create new traveller ${requestTraveller.fullName}")
                        val createTravellerEntity = toTravellerEntity(requestTraveller, caseEntity)
                        travellerList.add(createTravellerEntity)
                    }
                }
                caseEntity.traveller.removeIf { c -> c.id in removeList }
                caseEntity.initiatedFor = if (travellerList.isNotEmpty()) travellerList[0].fullName else null
                val savedCase = caseRepository.save(caseEntity)
                travellerRepository.saveAll(travellerList)
                return savedCase
            }

            "MANAGE_ASSIGNESS" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, ManageAssigneesEntity::class.java)
                val caseEntity = case as ManageAssigneesEntity
                caseEntity.accommodationSupport = request.accommodationSupport
                caseEntity.caseCountry = request.caseCountry
                caseEntity.shippingSupport = request.shippingSupport
                caseEntity.educationSupport = request.educationSupport
                caseEntity.assigneesPayType = request.assigneesPayType
                caseEntity.taxSupport = request.taxSupport
                caseEntity.expertTaxServices = request.expertTaxServices
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "MANAGE_PAYROLL" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, ManagePayrollEntity::class.java)
                val caseEntity = case as ManagePayrollEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.entityInJurisdiction = request.entityInJurisdiction
                caseEntity.staffCount = request.staffCount
                caseEntity.staffType = request.staffType
                caseEntity.salaryRange = request.salaryRange
                caseEntity.benefitsSupport = request.benefitsSupport
                caseEntity.moreInformation = request.moreInformation
                caseEntity.employmentSupport = request.employmentSupport
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "HR_EMP_SUPPORT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, HrAndEmploymentSupportEntity::class.java)
                val caseEntity = case as HrAndEmploymentSupportEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.entityInJurisdiction = request.entityInJurisdiction
                caseEntity.contractsSupport = request.contractsSupport
                caseEntity.policySupport = request.policySupport
                caseEntity.liabilityInsurance = request.liabilityInsurance
                caseEntity.employmentSupport = request.employmentSupport
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "INTELLECTUAL_PROPERTY" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, IntellectualPropertyEntity::class.java)
                val caseEntity = case as IntellectualPropertyEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.ipSupport = request.ipSupport
                caseEntity.interestedIp = request.interestedIp
                caseEntity.protectWordLogo = request.protectWordLogo
                caseEntity.trademarkName = request.trademarkName
                caseEntity.copyrightNature = request.copyrightNature
                caseEntity.copyrightDetails = request.copyrightDetails
                caseEntity.patentDescription = request.patentDescription
                caseEntity.designDescription = request.designDescription
                caseEntity.ipAssignSupport = request.ipAssignSupport
                caseEntity.ipDisputeSupport = request.ipDisputeSupport
                caseEntity.trademarkLogoLink = request.trademarkLogoLink
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "DATA_PROTECTION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, DataProtectionEntity::class.java)
                val caseEntity = case as DataProtectionEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.dataProtection = request.dataProtection
                caseEntity.dataOfficer = request.dataOfficer
                caseEntity.dataController = request.dataController
                caseEntity.dataCompliance = request.dataCompliance
                caseEntity.dataPolicies = request.dataPolicies
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "COM_LAW_CONTRACTS" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, CommercialSupportEntity::class.java)
                val caseEntity = case as CommercialSupportEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.legalAdvice = request.legalAdvice
                caseEntity.currentContracts = request.currentContracts
                caseEntity.purchaseContract = request.purchaseContract
                caseEntity.commercialNeeds = request.commercialNeeds
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "RISK_MANAGEMENT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, RiskManagementEntity::class.java)
                val caseEntity = case as RiskManagementEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.riskPolicy = request.riskPolicy
                caseEntity.lastPolicyUpdated = request.lastPolicyUpdated
                caseEntity.identifiedPotentialRisks = request.identifiedPotentialRisks
                caseEntity.potentialRisks = request.potentialRisks
                caseEntity.strategyAdvice = request.strategyAdvice
                caseEntity.insuranceForJurisdiction = request.insuranceForJurisdiction
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "EMP_BENEFITS_INSURANCE" -> {
                val request =
                    mapper.convertValue(jsonObj.caseEntityData, EmployeeBenefitsAndInsuranceEntity::class.java)
                val caseEntity = case as EmployeeBenefitsAndInsuranceEntity
                caseEntity.caseCountry = request.caseCountry
                caseEntity.employeesHealthCare = request.employeesHealthCare
                caseEntity.existingInsurance = request.existingInsurance
                caseEntity.insurancePartner = request.insurancePartner
                caseEntity.localInsurance = request.localInsurance
                caseEntity.physicalProducts = request.physicalProducts
                caseEntity.physicalOffice = request.physicalOffice
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "TAX_SUPPORT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, TaxSupportEntity::class.java)
                val caseEntity = case as TaxSupportEntity
                caseEntity.taxSupport = request.taxSupport
                caseEntity.caseCountry = request.caseCountry
                caseEntity.estimatedRevenue = request.estimatedRevenue
                caseEntity.payroll = request.payroll
                caseEntity.employeeBenefits = request.employeeBenefits
                caseEntity.benefitsTaxable = request.benefitsTaxable
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "COMPLEX_REQUIREMENT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, ComplexRequirementEntity::class.java)
                val caseEntity = case as ComplexRequirementEntity
                caseEntity.requirementCountry = request.requirementCountry
                caseEntity.caseCategoryType = request.caseCategoryType
                caseEntity.moreInformation = request.moreInformation
                caseEntity.aliasName = request.aliasName
                setPersonalDetails(mapper, jsonObj, caseEntity, request.requirementCountry, null)
                return caseRepository.save(caseEntity)
            }

            "RIGHT_TO_WORK_CHECK" -> {
                return updateRightToWorkCheck(mapper, jsonObj, case)
            }

            "LEGALISATION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, LegalisationEntity::class.java)
                val caseEntity = case as LegalisationEntity
                caseEntity.nationality = request.nationality
                caseEntity.legalisedCountry = request.legalisedCountry
                caseEntity.legalisedDocument = request.legalisedDocument
                caseEntity.otherDocuments = request.otherDocuments
                caseEntity.firstName = request.firstName
                caseEntity.middleName = request.middleName
                caseEntity.lastName = request.lastName
                caseEntity.accountName = request.accountName
                caseEntity.contactNo = request.contactNo
                caseEntity.emailAddress = request.emailAddress
                caseEntity.applicantCountry = request.applicantCountry
                caseEntity.shareApplicantInfo = request.shareApplicantInfo
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.legalisedCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "COMP_TAX_ASS" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, CompanyTaxAssessmentEntity::class.java)
                val caseEntity = case as CompanyTaxAssessmentEntity
                caseEntity.entityType = request.entityType
                caseEntity.companyHq = request.companyHq
                caseEntity.employeeType = request.employeeType
                caseEntity.caseCountry = request.caseCountry
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(mapper, jsonObj, caseEntity, request.caseCountry, null)
                return caseRepository.save(caseEntity)
            }

            "INDVL_TAX_ASS" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, IndividualTaxAssessmentEntity::class.java)
                val caseEntity = case as IndividualTaxAssessmentEntity
                caseEntity.companyEntity = request.companyEntity
                caseEntity.citizenCountry = request.citizenCountry
                caseEntity.employeeType = request.employeeType
                caseEntity.companyType = request.companyType
                caseEntity.contractWithOrganization = request.contractWithOrganization
                caseEntity.employeeEarnIncome = request.employeeEarnIncome
                caseEntity.employeeResidentType = request.employeeResidentType
                caseEntity.permissionToWork = request.permissionToWork
                caseEntity.foreName = request.foreName
                caseEntity.middleName = request.middleName
                caseEntity.surname = request.surname
                caseEntity.shareApplicantInfo = request.shareApplicantInfo
                caseEntity.accountName = request.accountName
                caseEntity.contactNo = request.contactNo
                caseEntity.emailAddress = request.emailAddress
                caseEntity.caseCountry = request.caseCountry
                caseEntity.moreInformation = request.moreInformation
                caseEntity.initiatedFor = request.initiatedFor ?: (request.foreName + " " + request.surname)
                caseEntity.country = request.caseCountry

                val personalDetails = caseEntity.personalDetails ?: CaseContactInformation()
                personalDetails.companyName = caseEntity.accountName
                personalDetails.country = caseEntity.country ?: personalDetails.country
                caseEntity.personalDetails = personalDetails

                return caseRepository.save(caseEntity)
            }

            "DEPENDENT_VISA" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, DependentVisaEntity::class.java)
                val caseEntity = case as DependentVisaEntity
                caseEntity.firstName = request.firstName
                caseEntity.middleName = request.middleName
                caseEntity.lastName = request.lastName
                caseEntity.relationWithPrimary = request.relationWithPrimary
                caseEntity.nationality = request.nationality
                caseEntity.residenceCountry = request.residenceCountry
                caseEntity.contactNo = request.contactNo
                caseEntity.emailAddress = request.emailAddress
                caseEntity.shareApplicantInfo = request.shareApplicantInfo
                caseEntity.applicantStayType = request.applicantStayType
                caseEntity.applicantStay = request.applicantStay
                caseEntity.travelPurpose = request.travelPurpose
                caseEntity.childAge = request.childAge
                caseEntity.travelCountry = request.travelCountry
                setPersonalDetails(
                    mapper,
                    jsonObj,
                    caseEntity,
                    request.travelCountry,
                    request.firstName + " " + request.lastName
                )
                if (request.shareApplicantInfo == true) {
                    caseEntity.email = request.emailAddress
                }
                return caseRepository.save(caseEntity)
            }

            "BUSINESS_VISA" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, BusinessVisaEntity::class.java)
                val caseEntity = case as BusinessVisaEntity
                caseEntity.destinationCountry = request.destinationCountry
                caseEntity.tripPurpose = request.tripPurpose
                caseEntity.travelDate = request.travelDate
                caseEntity.tripLength = request.tripLength
                caseEntity.tripLengthDetails = request.tripLengthDetails
                caseEntity.destinationCountryDuration = request.destinationCountryDuration
                caseEntity.destinationCountryDurationDetails = request.destinationCountryDurationDetails
                caseEntity.visaEntryType = request.visaEntryType
                caseEntity.visaServiceType = request.visaServiceType
                caseEntity.applicant!!.firstName = request.applicant!!.firstName
                caseEntity.applicant!!.middleName = request.applicant!!.middleName
                caseEntity.applicant!!.lastName = request.applicant!!.lastName
                caseEntity.applicant!!.citizenCountry = request.applicant!!.citizenCountry
                caseEntity.applicant!!.residenceCountry = request.applicant!!.residenceCountry
                caseEntity.applicant!!.jobTitle = request.applicant!!.jobTitle
                caseEntity.applicant!!.shareApplicantInfo = request.applicant!!.shareApplicantInfo
                caseEntity.applicant!!.contactNo = request.applicant!!.contactNo
                caseEntity.applicant!!.emailAddress = request.applicant!!.emailAddress
                caseEntity.applicant!!.companyName = request.applicant!!.companyName
                caseEntity.duties = request.duties
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.destinationCountry,
                    request.applicant!!.firstName + " " + request.applicant!!.lastName
                )
                if (request.applicant!!.shareApplicantInfo == true) {
                    caseEntity.email = request.applicant!!.emailAddress
                }
                return caseRepository.save(caseEntity)
            }

            "EMPLOYEE_OF_RECORD" -> {
                return updateEmployeeOfRecord(mapper, jsonObj, case)
            }

            "EXPATRIATE_TAX_SERVICES" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, ExpatriateTaxServicesEntity::class.java)
                val caseEntity = case as ExpatriateTaxServicesEntity
                caseEntity.hostCountry = request.hostCountry
                caseEntity.state = request.state
                caseEntity.services = request.services
                caseEntity.dateOfHire = request.dateOfHire
                caseEntity.assignmentStartDate = request.assignmentStartDate
                caseEntity.endDate = request.endDate
                caseEntity.serviceYears = request.serviceYears
                caseEntity.permanentTransfer = request.permanentTransfer
                caseEntity.applicant.firstName = request.applicant.firstName
                caseEntity.applicant.lastName = request.applicant.lastName
                caseEntity.applicant.citizenCountry = request.applicant.citizenCountry
                caseEntity.applicant.residenceCountry = request.applicant.residenceCountry
                caseEntity.applicant.jobTitle = request.applicant.jobTitle
                caseEntity.applicant.applicantState = request.applicant.applicantState
                caseEntity.applicant.contactNo = request.applicant.contactNo
                caseEntity.applicant.emailAddress = request.applicant.emailAddress
                caseEntity.applicant.shareApplicantInfo = request.applicant.shareApplicantInfo
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.hostCountry,
                    request.applicant.firstName + " " + request.applicant.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "TAX_RETURN_PREPARATION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, TaxReturnPreparationEntity::class.java)
                val caseEntity = case as TaxReturnPreparationEntity
                caseEntity.hostCountry = request.hostCountry
                caseEntity.state = request.state
                caseEntity.dateOfHire = request.dateOfHire
                caseEntity.assignmentStartDate = request.assignmentStartDate
                caseEntity.endDate = request.endDate
                caseEntity.serviceYears = request.serviceYears
                caseEntity.duties = request.duties
                caseEntity.permanentTransfer = request.permanentTransfer
                caseEntity.applicant.firstName = request.applicant.firstName
                caseEntity.applicant.lastName = request.applicant.lastName
                caseEntity.applicant.citizenCountry = request.applicant.citizenCountry
                caseEntity.applicant.residenceCountry = request.applicant.residenceCountry
                caseEntity.applicant.jobTitle = request.applicant.jobTitle
                caseEntity.applicant.applicantState = request.applicant.applicantState
                caseEntity.applicant.contactNo = request.applicant.contactNo
                caseEntity.applicant.emailAddress = request.applicant.emailAddress
                caseEntity.applicant.shareApplicantInfo = request.applicant.shareApplicantInfo
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.hostCountry,
                    request.applicant.firstName + " " + request.applicant.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "REMOTE_WORK_ASSESSMENT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, RemoteWorkAssessmentEntity::class.java)
                val caseEntity = case as RemoteWorkAssessmentEntity
                caseEntity.hostCountry = request.hostCountry
                caseEntity.anticipatedStartDate = request.anticipatedStartDate
                caseEntity.workDuration = request.workDuration
                caseEntity.spentTimePastYear = request.spentTimePastYear
                caseEntity.workSpentDuration = request.workSpentDuration
                caseEntity.entityInHostCountry = request.entityInHostCountry
                caseEntity.payrollInDestinationCountry = request.payrollInDestinationCountry
                caseEntity.duties = request.duties
                caseEntity.applicant.firstName = request.applicant.firstName
                caseEntity.applicant.lastName = request.applicant.lastName
                caseEntity.applicant.citizenCountry = request.applicant.citizenCountry
                caseEntity.applicant.residenceCountry = request.applicant.residenceCountry
                caseEntity.applicant.jobTitle = request.applicant.jobTitle
                caseEntity.applicant.applicantState = request.applicant.applicantState
                caseEntity.applicant.contactNo = request.applicant.contactNo
                caseEntity.applicant.emailAddress = request.applicant.emailAddress
                caseEntity.applicant.shareApplicantInfo = request.applicant.shareApplicantInfo
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.hostCountry,
                    request.applicant.firstName + " " + request.applicant.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "POSTED_WORKER_NOTIFICATION" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, PostedWorkerNotificationEntity::class.java)
                val caseEntity = case as PostedWorkerNotificationEntity

                caseEntity.toCountry = request.toCountry
                caseEntity.travelPurpose = request.travelPurpose
                caseEntity.grossPayCurrency = request.grossPayCurrency
                caseEntity.grossPay = request.grossPay
                caseEntity.weeklyHours = request.weeklyHours
                caseEntity.paidCountry = request.paidCountry
                caseEntity.sendPersonFirstName = request.sendPersonFirstName
                caseEntity.sendPersonLastName = request.sendPersonLastName
                caseEntity.sendPersonEmail = request.sendPersonEmail
                caseEntity.sendPersonShareInfo = request.sendPersonShareInfo
                caseEntity.postingStartDate = request.postingStartDate
                caseEntity.postingEndDate = request.postingEndDate
                caseEntity.postingWorkAddress = request.postingWorkAddress
                caseEntity.receivingCompany = request.receivingCompany
                caseEntity.receivingCompanyCity = request.receivingCompanyCity
                caseEntity.receivingCompanyAddress = request.receivingCompanyAddress
                caseEntity.receivingCompanyEmail = request.receivingCompanyEmail
                caseEntity.legalRepr = request.legalRepr
                caseEntity.leageReprEmail = request.leageReprEmail
                caseEntity.receivePersonFirstName = request.receivePersonFirstName
                caseEntity.receivePersonLastName = request.receivePersonLastName
                caseEntity.receivePersonEmail = request.receivePersonEmail
                caseEntity.receivePersonShareInfo = request.receivePersonShareInfo
                caseEntity.moreInformation = request.moreInformation

                caseEntity.applicant.firstName = request.applicant.firstName
                caseEntity.applicant.lastName = request.applicant.lastName
                caseEntity.applicant.nationality = request.applicant.nationality
                caseEntity.applicant.residenceCountry = request.applicant.residenceCountry
                caseEntity.applicant.jobTitle = request.applicant.jobTitle
                caseEntity.applicant.highestQualification = request.applicant.highestQualification
                caseEntity.applicant.contactNo = request.applicant.contactNo
                caseEntity.applicant.emailAddress = request.applicant.emailAddress
                caseEntity.applicant.shareApplicantInfo = request.applicant.shareApplicantInfo

                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.toCountry,
                    request.applicant.firstName + " " + request.applicant.lastName
                )
                return caseRepository.save(caseEntity)
            }

            "ENTRY_VISA" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, EntryVisaEntity::class.java)
                val caseEntity = case as EntryVisaEntity
                caseEntity.destinationCountry = request.destinationCountry
                caseEntity.tripPurpose = request.tripPurpose
                caseEntity.travelDate = request.travelDate
                caseEntity.tripLength = request.tripLength
                caseEntity.tripLengthDetails = request.tripLengthDetails
                caseEntity.destinationCountryDuration = request.destinationCountryDuration
                caseEntity.destinationCountryDurationDetails = request.destinationCountryDurationDetails
                caseEntity.visaEntryType = request.visaEntryType
                caseEntity.visaServiceType = request.visaServiceType
                caseEntity.applicant!!.firstName = request.applicant!!.firstName
                caseEntity.applicant!!.middleName = request.applicant!!.middleName
                caseEntity.applicant!!.lastName = request.applicant!!.lastName
                caseEntity.applicant!!.citizenCountry = request.applicant!!.citizenCountry
                caseEntity.applicant!!.residenceCountry = request.applicant!!.residenceCountry
                caseEntity.applicant!!.jobTitle = request.applicant!!.jobTitle
                caseEntity.applicant!!.shareApplicantInfo = request.applicant!!.shareApplicantInfo
                caseEntity.applicant!!.contactNo = request.applicant!!.contactNo
                caseEntity.applicant!!.emailAddress = request.applicant!!.emailAddress
                caseEntity.applicant!!.companyName = request.applicant!!.companyName
                caseEntity.duties = request.duties
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.destinationCountry,
                    request.applicant!!.firstName + " " + request.applicant!!.lastName
                )
                if (request.applicant!!.shareApplicantInfo == true) {
                    caseEntity.email = request.applicant!!.emailAddress
                }
                return caseRepository.save(caseEntity)
            }

            "RULING_30_PERCENT" -> {
                val request = mapper.convertValue(jsonObj.caseEntityData, Ruling30PercentEntity::class.java)
                val caseEntity = case as Ruling30PercentEntity
                caseEntity.destinationCountry = request.destinationCountry
                caseEntity.citizenCountry = request.citizenCountry
                caseEntity.residenceCountry = request.residenceCountry
                caseEntity.emailAddress = request.emailAddress
                caseEntity.bsn = request.bsn
                caseEntity.jobTitle = request.jobTitle
                caseEntity.jobStartDate = request.jobStartDate
                caseEntity.firstName = request.firstName
                caseEntity.lastName = request.lastName
                caseEntity.moreInformation = request.moreInformation
                setPersonalDetails(
                    mapper, jsonObj, caseEntity, request.destinationCountry,
                    request.firstName + " " + request.lastName
                )
                return caseRepository.save(caseEntity)
            }

            else -> throw ApplicationException(ErrorCode.NOT_FOUND)
        }
    }

    private fun updateRightToWorkCheck(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        case: CaseEntity
    ): RightToWorkCheckEntity {
        val request = mapper.convertValue(jsonObj.caseEntityData, RightToWorkCheck::class.java)
        val caseEntity = case as RightToWorkCheckEntity
        caseEntity.nationality = request.nationality
        caseEntity.caseCountry = request.caseCountry
        caseEntity.documentType = request.documentType
        caseEntity.timeToWorkLeft = request.timeToWorkLeft
        caseEntity.moreInformation = request.moreInformation
        caseEntity.country = request.caseCountry
        caseEntity.initiatedFor = request.applicantInfo[0].firstName + " " + request.applicantInfo[0].lastName
        setPersonalDetails(mapper, jsonObj, caseEntity)

        val applicantInfoList: MutableList<ApplicantInfoEntity> = mutableListOf()
        val removeList = caseEntity.applicantInfo.map { it.id }.toMutableList()
        log.info("Current DB Applicant Info $removeList")

        request.applicantInfo.forEach { requestApplicantInfo ->
            if (requestApplicantInfo.id != null) {
                log.info("process for Applicant Info id ${requestApplicantInfo.id}")
                val applicantInfoEntityOpt = applicantInfoRepository.findById(requestApplicantInfo.id!!)
                if (applicantInfoEntityOpt.isPresent) {
                    log.info("record found for Applicant Info id ${requestApplicantInfo.id}")
                    val applicantInfoEntity: ApplicantInfoEntity = applicantInfoEntityOpt.get()
                    removeList.remove(requestApplicantInfo.id)
                    applicantInfoEntity.firstName = requestApplicantInfo.firstName
                    applicantInfoEntity.middleName = requestApplicantInfo.middleName
                    applicantInfoEntity.lastName = requestApplicantInfo.lastName
                    applicantInfoEntity.companyName = requestApplicantInfo.companyName
                    applicantInfoEntity.shareApplicantInfo = requestApplicantInfo.shareApplicantInfo
                    applicantInfoEntity.contactNo = requestApplicantInfo.contactNo
                    applicantInfoEntity.emailAddress = requestApplicantInfo.emailAddress
                    applicantInfoList.add(applicantInfoEntity)
                } else {
                    val createApplicantInfoEntity = toApplicantInfoEntity(requestApplicantInfo, caseEntity)
                    applicantInfoList.add(createApplicantInfoEntity)
                }
            } else {
                log.info("create new Applicant Info ${requestApplicantInfo.firstName} ${requestApplicantInfo.lastName}")
                val createApplicantInfoEntity = toApplicantInfoEntity(requestApplicantInfo, caseEntity)
                applicantInfoList.add(createApplicantInfoEntity)
            }
        }
        caseEntity.applicantInfo.removeIf { c -> c.id in removeList }
        val savedCase = caseRepository.save(caseEntity)
        applicantInfoRepository.saveAll(applicantInfoList)
        return savedCase
    }

    private fun updateEmployeeOfRecord(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        case: CaseEntity
    ): EmployeeOfRecordEntity {
        val request = mapper.convertValue(jsonObj.caseEntityData, EmployeeOfRecord::class.java)
        val caseEntity = case as EmployeeOfRecordEntity
        caseEntity.destinationCountry = request.destinationCountry
        caseEntity.entityInDestinationCountry = request.entityInDestinationCountry
        caseEntity.signContract = request.signContract
        caseEntity.labourSafety = request.labourSafety
        caseEntity.certificationRequired = request.certificationRequired
        caseEntity.hiringDate = TimeUtil.fromInstantMillis(request.hiringDate)
        caseEntity.governmentContract = request.governmentContract
        caseEntity.internationalTravel = request.internationalTravel
        caseEntity.employmentTypes = request.employmentTypes
        caseEntity.rightToWork = request.rightToWork
        caseEntity.moreInformation = request.moreInformation
        caseEntity.country = request.destinationCountry
        caseEntity.initiatedFor = request.applicants[0].firstName + " " + request.applicants[0].lastName
        setPersonalDetails(mapper, jsonObj, caseEntity)

        val applicantInfoList: MutableList<EmployeeRecordApplicantEntity> = mutableListOf()
        val removeList = caseEntity.applicants.map { it.id }.toMutableList()
        log.info("Current DB Applicant Info $removeList")

        request.applicants.forEach { requestApplicantInfo ->
            if (requestApplicantInfo.id != null) {
                log.info("process for Applicant Info id ${requestApplicantInfo.id}")
                val applicantInfoEntityOpt = employeeOfRecordApplicantRepository.findById(requestApplicantInfo.id!!)
                if (applicantInfoEntityOpt.isPresent) {
                    log.info("record found for Applicant Info id ${requestApplicantInfo.id}")
                    val applicantInfoEntity: EmployeeRecordApplicantEntity = applicantInfoEntityOpt.get()
                    removeList.remove(requestApplicantInfo.id)
                    applicantInfoEntity.firstName = requestApplicantInfo.firstName
                    applicantInfoEntity.lastName = requestApplicantInfo.lastName
                    applicantInfoEntity.residenceCountry = requestApplicantInfo.residenceCountry
                    applicantInfoEntity.jobTitle = requestApplicantInfo.jobTitle
                    applicantInfoEntity.salaryCurrency = requestApplicantInfo.salaryCurrency
                    applicantInfoEntity.salary = requestApplicantInfo.salary
                    applicantInfoList.add(applicantInfoEntity)
                } else {
                    val createApplicantInfoEntity =
                        toEmployeeRecordApplicantInfoEntity(requestApplicantInfo, caseEntity)
                    applicantInfoList.add(createApplicantInfoEntity)
                }
            } else {
                log.info("create new Applicant Info ${requestApplicantInfo.firstName} ${requestApplicantInfo.lastName}")
                val createApplicantInfoEntity = toEmployeeRecordApplicantInfoEntity(requestApplicantInfo, caseEntity)
                applicantInfoList.add(createApplicantInfoEntity)
            }
        }
        caseEntity.applicants.removeIf { c -> c.id in removeList }
        val savedCase = caseRepository.save(caseEntity)
        employeeOfRecordApplicantRepository.saveAll(applicantInfoList)
        return savedCase
    }

    private fun updateEntitySetup(
        mapper: ObjectMapper,
        jsonObj: GenericCaseRequest,
        case: CaseEntity
    ): EntitySetupEntity {

        val request = mapper.convertValue(jsonObj.caseEntityData, EntitySetup::class.java)
        val caseEntity = case as EntitySetupEntity
        caseEntity.caseCountry = request.caseCountry
        caseEntity.entityType = request.entityType
        caseEntity.setupCompanyName = request.setupCompanyName
        caseEntity.businessActivities = request.businessActivities
        caseEntity.hqCountry = request.hqCountry
        caseEntity.hqState = request.hqState
        caseEntity.hqCity = request.hqCity
        caseEntity.hqZipcode = request.hqZipcode
        caseEntity.hqAddress = request.hqAddress
        caseEntity.foreignOwned = request.foreignOwned
        caseEntity.requireAddress = request.requireAddress
        caseEntity.moreInformation = request.moreInformation
        caseEntity.otherInformation = request.otherInformation
        caseEntity.companySecretary.fullName = request.companySecretary.fullName
        caseEntity.companySecretary.dob = request.companySecretary.dob
        caseEntity.companySecretary.occupation = request.companySecretary.occupation
        caseEntity.companySecretary.nationality = request.companySecretary.nationality
        caseEntity.companySecretary.residentialAddress = request.companySecretary.residentialAddress
        caseEntity.companySecretary.serviceAddress = request.companySecretary.serviceAddress
        case.country = request.caseCountry
        case.initiatedFor = request.directors[0].fullName
        val directorList: MutableList<DirectorEntity> = mutableListOf()
        val directorRemoveList = caseEntity.directors.map { it.id }.toMutableList()

        request.directors.forEach { requestDirector ->
            val directorEntity: DirectorEntity
            if (requestDirector.id != null) {
                val directorEntityOpt = directorRepository.findById(requestDirector.id!!)
                if (directorEntityOpt.isPresent) {
                    directorEntity = directorEntityOpt.get()
                    directorRemoveList.remove(directorEntity.id)
                    directorEntity.fullName = requestDirector.fullName
                    directorEntity.dob = requestDirector.dob
                    directorEntity.occupation = requestDirector.occupation
                    directorEntity.nationality = requestDirector.nationality
                    directorEntity.residentialAddress = requestDirector.residentialAddress
                    directorEntity.serviceAddress = requestDirector.serviceAddress
                } else {
                    directorEntity = toDirectorEntity(requestDirector, caseEntity)
                }
            } else {
                directorEntity = toDirectorEntity(requestDirector, caseEntity)
            }
            directorList.add(directorEntity)
        }
        caseEntity.directors.removeIf { c -> c.id in directorRemoveList }

        val shareholderList: MutableList<ShareholderEntity> = mutableListOf()
        val shareholderRemoveList = caseEntity.shareholders.map { it.id }.toMutableList()

        request.shareholders.forEach { requestShareholder ->
            val shareholderEntity: ShareholderEntity
            if (requestShareholder.id != null) {
                val shareholderEntityOpt = shareholderRepository.findById(requestShareholder.id!!)
                if (shareholderEntityOpt.isPresent) {
                    shareholderEntity = shareholderEntityOpt.get()
                    shareholderRemoveList.remove(shareholderEntity.id)
                    shareholderEntity.fullName = requestShareholder.fullName
                    shareholderEntity.percentageShare = requestShareholder.percentageShare
                    shareholderEntity.serviceAddress = requestShareholder.serviceAddress
                } else {
                    shareholderEntity = toShareholderEntity(requestShareholder, caseEntity)
                }
            } else {
                shareholderEntity = toShareholderEntity(requestShareholder, caseEntity)
            }
            shareholderList.add(shareholderEntity)
        }
        caseEntity.shareholders.removeIf { c -> c.id in shareholderRemoveList }

        val savedCase = caseRepository.save(caseEntity)
        directorRepository.saveAll(directorList)
        shareholderRepository.saveAll(shareholderList)

        return savedCase
    }

    private fun toApplicantInfoEntity(request: ApplicantInfo, case: RightToWorkCheckEntity): ApplicantInfoEntity {
        return ApplicantInfoEntity(
            firstName = request.firstName,
            middleName = request.middleName,
            lastName = request.lastName,
            companyName = request.companyName,
            shareApplicantInfo = request.shareApplicantInfo,
            contactNo = request.contactNo,
            emailAddress = request.emailAddress,
            rightToWorkCheckEntity = case
        )
    }

    private fun toEmployeeRecordApplicantInfoEntity(
        request: EmployeeOfRecordApplicant,
        case: EmployeeOfRecordEntity
    ): EmployeeRecordApplicantEntity {
        return EmployeeRecordApplicantEntity(
            firstName = request.firstName,
            lastName = request.lastName,
            residenceCountry = request.residenceCountry,
            jobTitle = request.jobTitle,
            salaryCurrency = request.salaryCurrency,
            salary = request.salary,
            employeeOfRecordEntity = case
        )
    }

    private fun toTravellerEntity(request: Traveller, case: MultipleVisaEntity): TravellerEntity {
        return TravellerEntity(
            fullName = request.fullName!!,
            nationality = request.nationality,
            travelCountry = request.travelCountry,
            estimatedSalary = request.estimatedSalary,
            durationOfStay = request.durationOfStay,
            durationOfStayType = request.durationOfStayType,
            duties = request.duties,
            qualification = request.qualification,
            dependantTravelling = request.dependantTravelling,
            dependantDurationOfStay = request.dependantDurationOfStay,
            currencySymbol = request.currencySymbol,
            visaEntity = case
        )
    }

    private fun toDirectorEntity(request: Director, case: EntitySetupEntity): DirectorEntity {
        return DirectorEntity(
            fullName = request.fullName,
            dob = request.dob,
            occupation = request.occupation,
            nationality = request.nationality,
            residentialAddress = request.residentialAddress,
            serviceAddress = request.serviceAddress,
            entitySetup = case
        )
    }

    private fun toShareholderEntity(request: Shareholder, case: EntitySetupEntity): ShareholderEntity {
        return ShareholderEntity(
            fullName = request.fullName,
            percentageShare = request.percentageShare,
            serviceAddress = request.serviceAddress,
            entitySetup = case
        )
    }

    fun createPublicCase(caseCategory: String, jsonObj: GenericCaseRequest): List<Long?> {
        val mapper = jacksonObjectMapper()
        val userData = mapper.convertValue(jsonObj.userData, UserData::class.java)
        val user = loginAccountRepository.findByEmail(userData.email)
        if (user != null) throw ApplicationException(ErrorCode.USER_ALREADY_EXISTS)
        val request = SignUpRequest(
            userData.email, userData.firstName, userData.lastName, null,
            userData.corporateName, userData.country, null, userData.keepMeInformed,
            userData.recaptchaResponse
        )
        val pair = corporateService.createCorporateAndReturnData(request)
        val caseIds = createCase(caseCategory, jsonObj, pair.first, pair.second, true, null)
        caseIds.forEach {
            createGroupChat(it, pair.first, true)
            caseEmailService.caseSubmitEmail(it, true, super.getCorporateTeamEmails(it.createdBy?.userId))
        }
        return caseIds.map { it.id!! }
    }

    @Transactional
    fun deletePublicCase(caseId: Long, validationToken: String): Any {
        val validationUser = validationTokenRepository.findByCode(validationToken)
        val caseOpt = caseRepository.findById(caseId)
        if (caseOpt.isPresent && validationUser != null) {
            val case = caseOpt.get()
            val clientView = case.createdBy
            if (clientView != null) {
                if (clientView.userId == validationUser.userId && clientView.status == AccountStatus.PENDING_VERIFICATION) {
                    try {
                        caseRepository.delete(case)
                        adminUserService.deleteCorporateByRootUserId(clientView.userId)
                        return AppConstant.SUCCESS_RESPONSE_STRING
                    } catch (ex: Exception) {
                        throw ex
                    }
                }
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }
        throw ApplicationException(ErrorCode.NOT_FOUND)
    }


    fun caseFeesInvoiceUpdate(caseId: Long, request: CaseFeesInvoiceRequest, authenticatedUser: AuthenticatedUser) {

        val case = super.get(caseId, authenticatedUser, caseRepository, "USER")
        case.caseFees?.feesDetails?.incomePaid = request.incomePaid
        case.caseFees?.feesDetails?.incomeRaised = request.incomeRaised
        caseRepository.save(case)
    }

    fun createCaseFees(caseId: Long, request: CaseFeesRequest, authenticatedUser: AuthenticatedUser) {

        val case = super.get(caseId, authenticatedUser, caseRepository, "USER")
        var approverDetails: ApproverDetails?
        var approverEmails: String? = null

        val feeDetails = FeeDetails(
            professionalFees = request.feesDetails?.professionalFees,
            governmentFees = request.feesDetails?.governmentFees,
            apostilleFees = request.feesDetails?.apostilleFees,
            certificateFees = request.feesDetails?.certificateFees,
            translationFees = request.feesDetails?.translationFees,
            thirdPartyFees = request.feesDetails?.thirdPartyFees,
            totalFees = request.feesDetails?.totalFees,
            incomeRaised = request.feesDetails?.incomeRaised,
            incomePaid = request.feesDetails?.incomePaid,
            dependentFees = request.feesDetails?.dependentFees,
            feesCurrency = request.feesDetails?.feesCurrency
        )

        if (request.needApproval) {
            approverEmails = request.approverEmails
            approverDetails = null
        } else {
            val approvalDetailsReq = request.approverDetails!!
            approverDetails = ApproverDetails(
                firstName = approvalDetailsReq.firstName,
                lastName = approvalDetailsReq.lastName,
                jobTitle = approvalDetailsReq.jobTitle,
                email = approvalDetailsReq.email,
                feesAgreedDate = if (approvalDetailsReq.feesAgreedDate != null) TimeUtil.fromInstantMillis(
                    approvalDetailsReq.feesAgreedDate!!
                ) else null
            )
        }

        case.caseFees = CaseFees(
            comments = request.comments,
            needApproval = request.needApproval,
            isApproved = !request.needApproval,
            approverEmails = approverEmails,
            feesDetails = feeDetails,
            approverDetails = approverDetails,
            feesCreationDate = LocalDateTime.now()
        )

        case.feeToken = UUID.randomUUID().toString()

        if (request.needApproval) {
            val emails: MutableSet<String> = approverEmails!!.split(",").toMutableSet()

            caseEmailService.feesApprovalRequiredEmail(
                case.accountManager, case.id, case.category?.subCategoryId!!,
                case.uuid!!, case.feeToken!!, emails
            )
        }
        caseRepository.save(case)
    }

    fun caseFeesApprovalForExternal(uuid: String, request: ApproverDetailsRequest): ApproverDetailsRequest? {
        val case = caseRepository.findByUuid(uuid) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (case.feeToken != request.feeToken) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        var approveDetails: ApproverDetails? = null
        if (case.caseFees != null) {
            case.caseFees!!.isApproved = true
            case.caseFees!!.needApproval = false
            approveDetails = ApproverDetails(
                firstName = request.firstName,
                lastName = request.lastName,
                jobTitle = request.jobTitle,
                email = request.email,
                feesAgreedDate = LocalDateTime.now()
            )
            case.caseFees!!.approverDetails = approveDetails
        }

        //save the fee approval history
        val caseFeesHistory = CaseFeesApprovalHistory(
            caseFees = case.caseFees,
            case = case
        )
        var targetEmails: MutableList<String> = mutableListOf()
        //send email to case owner's Reporting managers and cg admin
        if (case.createdBy!!.userType == UserType.CORPORATE) {
            val managerIds =
                corporateUserRepository.findById(case.createdBy!!.userId).get().managers.map { it.managerId }
            targetEmails =
                if (managerIds.isNotEmpty()) loginAccountRepository.findAllByIdIn(managerIds).map { it.email }
                    .toMutableList() else mutableListOf()
        }
        caseEmailService.caseFeesApprovedEmail(
            case.accountManager,
            targetEmails,
            case.id!!,
            case.category?.subCategoryId!!
        )

        caseRepository.save(case)
        caseFeesApprovalHistoryRepository.save(caseFeesHistory)
        return ApproverDetailsRequest.ModelMapper.from(approveDetails)
    }


    fun caseFeesApprovalForCaseOwner(caseId: Long, authenticatedUser: AuthenticatedUser): ApproverDetailsRequest? {
        val case = getDetails(caseId, authenticatedUser)
        var approverDetails: ApproverDetails? = null
        var targetEmails: MutableList<String> = mutableListOf()
        if (case.caseFees != null) {
            var user = loginAccountRepository.findById(authenticatedUser.userId).get()
            if (user is ExpertUserEntity) {
                approverDetails = ApproverDetails(
                    feesAgreedDate = LocalDateTime.now(),
                    firstName = user.firstName,
                    lastName = user.lastName,
                    jobTitle = user.jobTitle,
                    email = user.email
                )
                targetEmails.add(user.email)
            } else if (user is CorporateUserEntity) {
                approverDetails = ApproverDetails(
                    firstName = user.firstName,
                    lastName = user.lastName,
                    jobTitle = user.jobTitle,
                    email = user.email,
                    feesAgreedDate = LocalDateTime.now()
                )
                val managerIds =
                    corporateUserRepository.findById(case.createdBy!!.userId).get().managers.map { it.managerId }
                targetEmails = loginAccountRepository.findAllByIdIn(managerIds).map { it.email }.toMutableList()
            }
            case.caseFees!!.approverDetails = approverDetails
            case.caseFees!!.isApproved = true
            case.caseFees!!.needApproval = false
        }
        //save the fee approval history
        val caseFeesHistory = CaseFeesApprovalHistory(
            caseFees = case.caseFees,
            case = case
        )
        //send email to case owner and cg admin
        caseEmailService.caseFeesApprovedEmail(
            case.accountManager,
            targetEmails,
            case.id!!,
            case.category?.subCategoryId!!
        )

        caseRepository.save(case)
        caseFeesApprovalHistoryRepository.save(caseFeesHistory)
        return ApproverDetailsRequest.ModelMapper.from(approverDetails)
    }

    fun getSubCategories(caseSubCategory: String): List<Map<String, Any>> {
        val subCategories = caseStatusMasterRepository.findBySubCategory(caseSubCategory)
        return subCategories.stream().map { e ->
            mapOf(
                "status" to e.status,
                "displayText" to e.statusDisplayText,
                "actionFor" to e.actionFor,
                "show" to e.showStatus
            )
        }.collect(Collectors.toList())
    }

    fun getStatues(): List<Map<String, String>>? {
        return caseStatusMasterRepository.findAllDistinctStatus()
    }

    fun listCaseIds(userId: Long): MutableList<ApplicantCaseReferenceData> {

        val users = corporateUserRepository.findIdByManagersManagerId(userId)
        return caseRepository.findAllIdByCreatedByUserIdIn(users.map { it.id }.plus(userId))
    }

    fun updateCaseHistory(caseHistoryId: Long, request: Map<String, Any>, authenticatedUser: AuthenticatedUser) {
        val caseStatusHistory = caseStatusHistoryRepository.findById(caseHistoryId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        val isDeleted: Boolean = request.getOrDefault("isDeleted", caseStatusHistory.isDeleted) as Boolean
        caseStatusHistory.isDeleted = isDeleted
        caseStatusHistoryRepository.save(caseStatusHistory)
    }

    fun listCaseNotes(caseId: Long, authenticatedUser: AuthenticatedUser): List<CaseNotesDetails> {

        val pageRequest = PageRequest.of(
            0, Integer.MAX_VALUE,
            SearchConstant.SORT_ORDER("createdDate", "DESC")
        )

        val caseNotes = caseNotesRepository.findAllByCaseId(caseId, pageRequest) ?: return emptyList()

        return caseNotes.map {
            CaseNotesDetails.ModelMapper.from(
                it,
                userProfileUtil.retrieveProfile(it.userId)
            )
        }
    }

    fun listDocumentActivityLogs(caseId: Long, type: String, docType: String?, authenticatedUser: AuthenticatedUser):
            List<CaseDocumentActivityDetails>? {
        getDetails(caseId, authenticatedUser)
        val pageRequest = PageRequest.of(0, Integer.MAX_VALUE, SearchConstant.SORT_ORDER("createdDate", "DESC"))
        when (type) {
            "STATUS" -> {
                val statusViews = if (docType == null)
                    caseDoucmentsStatusLogsRepository.findAllByCaseId(caseId, pageRequest)
                else
                    caseDoucmentsStatusLogsRepository.findAllByCaseIdAndDocType(caseId, docType, pageRequest)

                return statusViews?.map {
                    CaseDocumentActivityDetails.ModelMapper.from(it, userProfileUtil.retrieveProfile(it.createdBy))
                }
            }

            "ACTIVITY" -> {
                val auditViews = if (docType == null)
                    caseDocumentsAuditRepository.findAllByCaseId(caseId, pageRequest)
                else
                    caseDocumentsAuditRepository.findAllByCaseIdAndDocType(caseId, docType, pageRequest)

                return auditViews?.map {
                    CaseDocumentActivityDetails.ModelMapper.from(it, userProfileUtil.retrieveProfile(it.createdBy))
                }
            }
        }
        throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
    }

    fun getLinkedCases(caseId: Long): PagedResult<CaseViewDetails>? {

        val linkedCaseId = caseRepository.getLinkedCaseIds(caseId)
        val cases = caseViewRepository.findAllByIdIn(
            linkedCaseId,
            PageRequest.of(0, Integer.MAX_VALUE, SearchConstant.SORT_ORDER("lastUpdatedDate", "DESC"))
        )

        return PagedResult.ModelMapper.from(
            cases,
            cases.map { CaseViewDetails.ModelMapper.from(it, getUserProfile(it.accountManager)) }.toList()
        )
    }

    @Transactional(readOnly = true)
    fun retrieveCorporateUsersList(caseId: Long, authenticatedUser: AuthenticatedUser): List<Any> {

        val case = getDetails(caseId, authenticatedUser)
        return when (case.createdBy!!.userType) {
            UserType.CORPORATE -> {
                val corporate = corporateUserRepository.findById(case.createdBy!!.userId)
                    .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                corporateService.retrieveCorporateUsers(corporate.corporate.id!!)
            }

            UserType.EXPERT -> {
                val companyProfile = expertCompanyProfileRepository.getReferenceByUsersId(case.createdBy!!.userId)
                    ?: throw ApplicationException(ErrorCode.NOT_FOUND)
                expertUserService.retrieveExpertUsers(companyProfile)
            }

            else -> {
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }
    }

    fun getCaseList(userId: Long): List<CaseReferenceData> {
        val singleVisa =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId("SINGLE_VISA", "IMMIGRATION_GM")
        val dependentVisa =
            caseCategoryRepository.getReferenceBySubCategoryIdAndParentCategoryId("DEPENDENT_VISA", "IMMIGRATION_GM")
        val createdBy =
            clientViewRepository.getReferenceByUserId(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        return caseRepository.findAllByCreatedByAndCategoryIn(createdBy, listOf(singleVisa, dependentVisa))
    }

    @Transactional(readOnly = true)
    fun getCaseDetailsById(caseId: String, authenticatedUser: AuthenticatedUser): Response<CaseViewDetails> {

        val case = fetchCaseEntity(caseId.toLong(), authenticatedUser)
        val documents = getCaseDocuments(case)
        val statusHistory = getCaseStatusHistory(case)
        val milestones = getCaseMilestones(case)
        val assigneeList = getCaseAssigneeProfileList(case)
        val managersList = getCaseManagerProfileList(case)
        val accountManager = getUserProfile(case.accountManager)
        if (!AdminAccessUtil.isAdminAndExpert(authenticatedUser)) case.notes = null


        val participants =
            groupChatParticipantRepository.findByGroupChatReferenceIdAndGroupChatChatTypeAndUserIdAndIsActive(
                caseId.toLong(),
                ChatType.CASE,
                authenticatedUser.userId,
                true
            )
        val isMember = participants.isNotEmpty()
        val caseOutput = CaseViewDetails.ModelMapper.fromCaseEntity(
            case, documents, statusHistory, milestones,
            assigneeList, managersList, accountManager, isMember
        )
        val userList = case.assignee.stream().map(UserView::id).collect(Collectors.toList())

        if (userList.contains(authenticatedUser.userId)) {
            caseOutput.caseDetails?.caseFees = null
            caseOutput.caseFees = null
        }
        case.assignee = mutableListOf()
        case.managers = mutableListOf()
        case.accountManager = null

        if (!canAccessCaseFees(case, authenticatedUser)) {
            caseOutput.caseDetails?.caseFees = null
            caseOutput.caseFees = null
        }
        val searchFilter = TaskWorkflowSearchFilter.Builder.build(
            null, null, null, null, null, case.partnerId != null, case.id!!, "CASE", null
        )
        val workflow = taskWorkflowService.list(
            searchFilter,
            PageRequest.of(0, 1, SearchConstant.SORT_ORDER("createdDate", "DESC")),
            authenticatedUser
        )
        if (workflow.rows.isNotEmpty()) {
            caseOutput.taskWorkflow = workflow.rows[0]
        }
        caseOutput.milestoneChart = taskWorkflowService.getMilestoneChart(caseId.toLong(), "CASE")
        return Response(true, caseOutput)
    }

    fun fetchCaseEntity(caseId: Long, authenticatedUser: AuthenticatedUser): CaseEntity {
        return super.get(caseId, authenticatedUser, caseRepository, "USER")
    }

    fun updatePartnerCg(
        caseId: Long, cgRequested: Boolean,
        authenticatedUser: AuthenticatedUser
    ): Boolean {
        val case = super.get(caseId, authenticatedUser, caseRepository, "USER")
        val partnerAdmin = loginAccountRepository.findById(authenticatedUser.userId).get()

        if (cgRequested) {
            case.cgRequestedStatus = CGRequestStatus.CG_REQUESTED
            caseEmailService.caseCgRequestMail(case, partnerAdmin)
        } else {
            case.cgRequestedStatus = null
        }
        caseRepository.save(case)
        return true
    }

    @Transactional
    fun createReminders(caseId: Long, request: CaseTracking, authenticatedUser: AuthenticatedUser): Boolean {

        val case = super.get(caseId, authenticatedUser, caseRepository, "USER")

        //delete existing reminders
        reminderRepository.deleteAllByReferenceId(caseId)
        setVisaDates(case, request)
        val reminders = request.reminders?.map {

            calculateReminderDate(
                case, ReminderEntity(
                    referenceId = caseId,
                    trackType = it.trackType,
                    date = it.date?.let { it1 -> TimeUtil.fromInstantMillis(it1) },
                    deadline = it.deadline,
                    deadlineAgainst = if (it.deadlineAgainst.isNullOrBlank()) CaseDeadlineType.VISA_EXPIRY_REMINDER else CaseDeadlineType.valueOf(
                        it.deadlineAgainst!!
                    ),
                    deadlineUnit = if (it.deadlineUnit.isNullOrBlank()) null else CaseDeadlineUnit.valueOf(it.deadlineUnit!!),
                    suggestion = it.suggestion
                )
            )
        }
        case.trackingType = request.trackingType

        //Recalculate renewal/expiry dates if dates are changed
        //Create default reminders for document and visa expiry/renewal since these are deleted
        if (case.trackingType != TrackingType.NOTRACK) {
            recalculateReminders(case)
        }

        reminders?.let { reminderRepository.saveAll(it) }
        caseRepository.save(case)
        return true
    }

    private fun setVisaDates(case: CaseEntity, request: CaseTracking) {

        if (request.trackingType == TrackingType.RENEWAL) {
            when (case) {
                is SingleVisaEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                is MultipleVisaEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                is RightToWorkCheckEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                is DependentVisaEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                is BusinessVisaEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                is EntryVisaEntity -> {
                    request.visaExpiry?.let { case.visaExpiryDate = it }
                    request.visaRenewalDeadline?.let { case.visaRenewalDate = it }
                }

                else -> {
                    log.error("Case: ${case.id} is not of type visa")
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                }
            }
        }
    }

    private fun calculateReminderDate(case: CaseEntity, caseTrackingEntity: ReminderEntity): ReminderEntity {

        if ((caseTrackingEntity.trackType == CaseTrackType.EXACT && listOf(
                CaseDeadlineType.VISA_RENEWAL_REMINDER,
                CaseDeadlineType.VISA_EXPIRY_REMINDER
            ).contains(caseTrackingEntity.deadlineAgainst)) &&
            (!listOf(
                CaseDeadlineType.VISA_RENEWAL,
                CaseDeadlineType.VISA_EXPIRY
            ).contains(caseTrackingEntity.deadlineAgainst))
        ) {
            return caseTrackingEntity
        }

        val caseDate = getCaseDate(case, caseTrackingEntity.deadlineAgainst!!)

        caseTrackingEntity.date = when (caseTrackingEntity.deadlineAgainst) {
            CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_EXPIRY_REMINDER -> {
                caseDate?.minus(
                    caseTrackingEntity.deadline!!,
                    ChronoUnit.valueOf(caseTrackingEntity.deadlineUnit!!.name)
                )
            }

            CaseDeadlineType.VISA_RENEWAL, CaseDeadlineType.VISA_EXPIRY -> {
                caseDate
            }

            else -> {
                caseTrackingEntity.date
            }
        }
        return caseTrackingEntity
    }

    private fun getCaseDate(case: CaseEntity, deadlineAgainst: CaseDeadlineType): LocalDateTime? {

        val caseTimestamp = when (case) {
            is SingleVisaEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            is MultipleVisaEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            is RightToWorkCheckEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            is DependentVisaEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            is BusinessVisaEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            is EntryVisaEntity -> {
                when (deadlineAgainst) {
                    CaseDeadlineType.VISA_EXPIRY_REMINDER, CaseDeadlineType.VISA_EXPIRY -> {
                        case.visaExpiryDate
                    }

                    CaseDeadlineType.VISA_RENEWAL_REMINDER, CaseDeadlineType.VISA_RENEWAL -> {
                        case.visaRenewalDate
                    }

                    else -> {
                        null
                    }
                }
            }

            else -> {
                log.error("Case: ${case.id} is not of type visa")
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }

        return caseTimestamp?.let { TimeUtil.fromInstantMillis(it) }

    }

    fun getReminders(caseId: Long, authenticatedUser: AuthenticatedUser): CaseTracking? {

        val reminders = reminderRepository.findByReferenceIdAndDeadlineAgainstIn(
            caseId, listOf(
                CaseDeadlineType.VISA_EXPIRY_REMINDER,
                CaseDeadlineType.VISA_RENEWAL_REMINDER
            )
        )
        val case = super.get(caseId, authenticatedUser, caseRepository, "USER")

        val visaExpiry: Long?
        val visaRenewal: Long?

        when (case) {
            is SingleVisaEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            is MultipleVisaEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            is RightToWorkCheckEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            is DependentVisaEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            is BusinessVisaEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            is EntryVisaEntity -> {
                visaExpiry = case.visaExpiryDate
                visaRenewal = case.visaRenewalDate
            }

            else -> {
                log.error("Case: ${case.id} is not of type visa")
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }

        return case.trackingType?.let {
            CaseTracking(
                trackingType = it,
                reminders = reminders.map { reminder -> CaseReminder.ModelMapper.from(reminder) },
                visaExpiry = visaExpiry,
                visaRenewalDeadline = visaRenewal
            )
        }
    }

    fun uploadDocuments(
        request: CaseDocumentsRequest,
        authenticatedUser: AuthenticatedUser
    ): List<CaseDocumentsEntity>? {
        val case = getDetails(request.caseId, authenticatedUser)
        return docRepoService.caseDocuments(case.id!!, request.documents, authenticatedUser.userId)
    }

    fun generateCase(assessmentId: Long, caseType: String?, authenticatedUser: AuthenticatedUser): Map<String, Any?> {
        val caseData = travelAssessmentService.generateCase(assessmentId, authenticatedUser, caseType)
        return mapOf("caseDetails" to caseData)
    }

    fun getDistinctMilestones(): List<Map<String, String>>? {
        return milestonesRepository.findAllDistinctMilestones()
    }

    @Transactional
    fun replaceCase(
        caseId: Long,
        caseType: String,
        caseRequest: GenericCaseRequest,
        authenticatedUser: AuthenticatedUser
    ): Boolean {

        val existingCase = getDetails(caseId, authenticatedUser)

        val assessmentId = existingCase.assessmentId ?: throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)

        // only cases created from assessment can be replaced

        if (!listOf(
                "BUSINESS_VISA",
                "ENTRY_VISA",
                "SINGLE_VISA",
                "POSTED_WORKER_NOTIFICATION",
                "RIGHT_TO_WORK_CHECK"
            ).contains(caseType)
        ) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
        val oldCaseType = existingCase.category?.subCategoryName!!
        caseRepository.delete(existingCase)
        groupChatService.delete(caseId, ChatType.CASE)
        groupChatService.delete(caseId, ChatType.APPLICANT_CASE)
        groupChatService.delete(caseId, ChatType.CLIENT_CASE)

        val (userId, partnerId) = getUserId(authenticatedUser, caseRequest)
        val accountId = caseRequest.additionalData?.get("accountId")?.longValue()
        val cases = createCase(
            caseType,
            caseRequest,
            userId,
            accountId,
            false,
            if (isExpert(authenticatedUser.role)) null else partnerId
        )

        createGroupChat(cases[0], userId, false)

        // update case id
        val newCaseId = cases[0].id!!

        val newCase = caseRepository.findById(newCaseId).get()
        newCase.status = "PENDING_VERIFICATION"
        newCase.assessmentId = assessmentId
        caseRepository.save(newCase)

        caseDocumentsRepository.deleteByCase(newCase)
        caseRepository.replaceCaseId(newCaseId, caseId)

        when (caseType) {
            "BUSINESS_VISA" -> {
                caseRepository.replaceBusinessVisa(newCaseId, caseId)
                docRepoService.createCaseDocument(caseId, arrayOf("PASSPORT"), userId)
            }

            "ENTRY_VISA" -> {
                caseRepository.replaceEntryVisa(newCaseId, caseId)
                docRepoService.createCaseDocument(caseId, arrayOf("PASSPORT"), userId)
            }

            "SINGLE_VISA" -> {
                caseRepository.replaceSingleVisa(newCaseId, caseId)
                docRepoService.createCaseDocument(
                    caseId,
                    arrayOf("PASSPORT", "RESUME", "RESIDENCY", "SCOPE", "DEGREE"),
                    userId
                )
            }

            "POSTED_WORKER_NOTIFICATION" -> {
                caseRepository.replacePWN(newCaseId, caseId)
            }

            "RIGHT_TO_WORK_CHECK" -> {
                caseRepository.replaceRTW(newCaseId, caseId)
                createCaseDocument(ObjectMapper(), caseRequest, caseId, userId)
            }
        }
        val updatedBy = loginAccountRepository.getFirstNameLastNameById(authenticatedUser.userId)
        //trigger email
        val emails = super.getCorporateTeamEmails(cases[0].createdBy?.userId)

        caseEmailService.caseTypeChangeEmail(cases[0], oldCaseType, caseId, updatedBy, emails)
        return true
    }
    fun getWorkflowReport(id: Long, authenticatedUser: AuthenticatedUser): List<TaskWorkflowReport>? {
        //just to make sure user has access to the case
        super.get(id, authenticatedUser, caseRepository, "USER")
        return taskWorkflowService.getReport(id, "CASE", authenticatedUser)
    }
    @Transactional
    fun createCaseUsingForm(
        subCaseCategoryId: String,
        jsonObj: GenericCaseRequest,
        caseFormId: Long,
        authenticatedUser: AuthenticatedUser
    ): Pair<List<Long?>, Boolean> {
        val (userId, partnerId) = getUserId(authenticatedUser, jsonObj)
        val accountId = jsonObj.additionalData?.get("accountId")?.longValue()
        
        val mapper = jacksonObjectMapper()
        mapper.findAndRegisterModules()
        
        val additionalData = jsonObj.additionalData
        
        val caseForm =
            caseFormRepository.findById(caseFormId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        val parentCategoryId = caseForm.category
        val caseEntityData = jsonObj.caseEntityData

        val caseEntity = GenericCaseEntity(mapper.writeValueAsString(caseEntityData))

        caseEntity.caseForm = caseForm
        caseEntity.category = caseCategoryRepository.getReferenceBySubCategoryId(subCaseCategoryId)!!

        val fieldMapping = caseForm.mappingFields!!

        val caseCountry = extractCaseFormMappingValue("country", fieldMapping.country, caseEntityData)
        val initiatedFor =
            "${extractCaseFormMappingValue("firstName", fieldMapping.firstName, caseEntityData)} ${extractCaseFormMappingValue("lastName", fieldMapping.lastName, caseEntityData)}"
        val email = extractCaseFormMappingValue("email", fieldMapping.email, caseEntityData)

        caseEntity.personalDetails?.companyName = extractCaseFormMappingValue("companyName", fieldMapping.company, caseEntityData)

        val casePartnerId = if(isExpert(authenticatedUser.role)) null else partnerId
        
       val cases = saveCommonData(
            userId, caseEntity, null, parentCategoryId,
            caseCountry, initiatedFor,
            false, accountId, casePartnerId, additionalData, email
        )
        
        
        val docExists = documentExists(cases[0].id!!)
        //create a group chat for case type
        cases.forEach {
            createGroupChat(it, userId, false)

            //case submit email
            val emails = super.getCorporateTeamEmails(it.createdBy?.userId)
            caseEmailService.caseSubmitEmail(it, false, emails)

            // create documents if present in case form
            if (caseForm.defaultDocuments!=null) {
                docRepoService.createCaseDocument(
                    it.id!!,
                    caseForm.defaultDocuments!!.split(",").toTypedArray(),
                    userId
                )
            }
        }
        return Pair(cases.map { it.id!! }, docExists)
    }

    @Transactional
    fun updateCaseUsingForm(
        jsonObj: GenericCaseRequest,
        caseId: Long,
        authenticatedUser: AuthenticatedUser
    ): CaseEntity {

        val caseEntity = getDetails(caseId, authenticatedUser) as GenericCaseEntity

        val caseEntityData = jsonObj.caseEntityData
        
        val mapper = jacksonObjectMapper()
        mapper.findAndRegisterModules()
        caseEntity.caseData = mapper.writeValueAsString(caseEntityData)

        return caseRepository.save(caseEntity)
    }

    private fun extractCaseFormMappingValue(mappingKey: String, key: String, caseEntityData: JsonNode): String? {
        val pages = caseEntityData["pages"]
        if (pages != null && pages.isArray) {
            for (page in pages) {
                val elements = page["elements"]
                val result = searchElements(elements, key)
                if (result != null) {
                    if (result.isObject) {
                        return result[mappingKey]?.textValue()
                    }
                    if (result.isTextual) {
                        return result.textValue()
                    }
                }
            }
        }
        return null
    }

    private fun searchElements(elements: JsonNode?, targetKey: String): JsonNode? {
        if (elements == null || !elements.isArray) return null

        for (element in elements) {
            val nameNode = element.get("name")
            if (nameNode != null && nameNode.asText() == targetKey && element.has("userResponse")) {
                return element.get("userResponse")
            }

            if (element.has("elements")) {
                val result = searchElements(element.get("elements"), targetKey)
                if (result != null) return result
            }
        }
        return null
    }
}
