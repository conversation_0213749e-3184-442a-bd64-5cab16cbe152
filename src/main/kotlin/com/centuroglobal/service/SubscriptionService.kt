package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.subscription.SubscriptionDetailsEntity
import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import com.centuroglobal.shared.data.entity.subscription.SubscriptionUsageEntity
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.data.payload.account.SubscriptionChangeRequest
import com.centuroglobal.shared.data.payload.account.SubscriptionUpdateRequest
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansRequest
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansResponse
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionUsageDetailsResponse
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionUsageResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.subscription.SubscriptionPlanRepository
import com.centuroglobal.shared.repository.subscription.SubscriptionUsageRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.data.pojo.subscription.usage.AbstractUsageResponse
import com.centuroglobal.shared.service.GenericEmailService
import com.centuroglobal.shared.service.subscription.SharedSubscriptionService
import com.centuroglobal.shared.service.subscription.UsageFactory
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

private val log = KotlinLogging.logger {}

private const val RECEIPT_REQUEST_MAIL_TEMPLATE = "email/subscription_receipt_request_mail"

@Service
class SubscriptionService(
    @Value("\${app.subscription.expiry-days}")
    private val expiryDays: Long,
    @Value("\${app.subscription.receipt-request-mail.cc}")
    private val ccList: List<String>,
    val loginAccountRepository: LoginAccountRepository,
    val bandsRepository: BandsRepository,
    val corporateUserRepository: CorporateUserRepository,
    val corporateRepository: CorporateRepository,
    val subscriptionPlanRepository: SubscriptionPlanRepository,
    val subscriptionUsageRepository: SubscriptionUsageRepository,
    val userActionRepository: UserActionRepository,
    val usageFactory: UsageFactory,
    val sharedSubscriptionService: SharedSubscriptionService,
    val genericEmailService: GenericEmailService

) {
    @Transactional
    fun update(request: SubscriptionChangeRequest, userId: Long) {

        val user = loginAccountRepository.getReferenceById(request.userId)
        user.subscriptionType = request.subscription
        loginAccountRepository.save(user)

        if(user is CorporateUserEntity) {
            val users = mutableListOf<CorporateUserEntity>()

            val superAdminFreeBand = bandsRepository.findByNameAndCorporate("Super Admin (free)", user.corporate)
            val superAdminPaidBand = bandsRepository.findByNameAndCorporate("Super Admin", user.corporate)

            val managerFreeBand = bandsRepository.findByNameAndCorporate("Account Manager (free)", user.corporate)
            val managerPaidBand = bandsRepository.findByNameAndCorporate("Account Manager", user.corporate)

            val superAdminUsers = mutableListOf<CorporateUserEntity>()
            superAdminUsers.addAll(superAdminFreeBand!!.corporateUsers!!)
            superAdminUsers.addAll(superAdminPaidBand!!.corporateUsers!!)

            val managerUsers = mutableListOf<CorporateUserEntity>()
            managerUsers.addAll(managerFreeBand!!.corporateUsers!!)
            managerUsers.addAll(managerPaidBand!!.corporateUsers!!)

            if (request.subscription == SubscriptionType.PAID) {
                superAdminUsers.forEach { it.band = superAdminPaidBand }
                managerUsers.forEach { it.band = managerPaidBand }

                users.addAll(superAdminUsers)
                users.addAll(managerUsers)

            } else if(request.subscription == SubscriptionType.FREE){
                superAdminUsers.forEach { it.band = superAdminFreeBand }
                managerUsers.forEach { it.band = managerFreeBand }

                users.addAll(superAdminUsers)
                users.addAll(managerUsers)
            }
            corporateUserRepository.saveAll(users)
            val loginUsers = corporateUserRepository.findByCorporateId(user.corporate.id!!)
            loginUsers.forEach { it.subscriptionType = request.subscription }
            loginAccountRepository.saveAll(loginUsers)
        }
    }

    fun updateSubscription() {
        val date = LocalDate.now().minusDays(expiryDays)

        val userList: MutableList<LoginAccountEntity> = mutableListOf()
        val subscriptionUserList = loginAccountRepository.findAllBySubscriptionTypeAndRoleAndCreatedDateBetween(
            SubscriptionType.PAID, Role.ROLE_CORPORATE, date.atStartOfDay(), date.atTime(LocalTime.MAX)
        )
        for (subscriptionUser in subscriptionUserList) {
            subscriptionUser.subscriptionType = SubscriptionType.FREE
            userList.add(subscriptionUser)
        }
        log.info(
            "Users marked as free after trial period is over {}",
            userList.map { it.id }.toList().joinToString(",") { it.toString() })
        loginAccountRepository.saveAll(userList)
    }

    @Transactional
    fun updateCorporateSubscriptionType(request: SubscriptionUpdateRequest) {
        val corporateEntity= corporateRepository.findById(request.id).get()
        val users = mutableListOf<CorporateUserEntity>()

        val superAdminFreeBand = bandsRepository.findByNameAndCorporate("Super Admin (free)",corporateEntity)
        val superAdminPaidBand = bandsRepository.findByNameAndCorporate("Super Admin", corporateEntity)

        val managerFreeBand = bandsRepository.findByNameAndCorporate("Account Manager (free)", corporateEntity)
        val managerPaidBand = bandsRepository.findByNameAndCorporate("Account Manager", corporateEntity)
        val superAdminUsers = mutableListOf<CorporateUserEntity>()
        superAdminUsers.addAll(superAdminFreeBand!!.corporateUsers!!)
        superAdminUsers.addAll(superAdminPaidBand!!.corporateUsers!!)

        val managerUsers = mutableListOf<CorporateUserEntity>()
        managerFreeBand?.let { managerUsers.addAll(it.corporateUsers!!) }
        managerPaidBand?.let { managerUsers.addAll(it.corporateUsers!!) }


        if (request.subscription == SubscriptionType.PAID) {
            superAdminUsers.forEach { it.band = superAdminPaidBand }
            managerUsers.forEach { it.band = managerPaidBand }

            users.addAll(superAdminUsers)
            users.addAll(managerUsers)
            corporateEntity.subscriptionActive=true
        } else if(request.subscription == SubscriptionType.FREE){
            superAdminUsers.forEach { it.band = superAdminFreeBand }
            managerUsers.forEach { it.band = managerFreeBand }

            users.addAll(superAdminUsers)
            users.addAll(managerUsers)
            corporateEntity.subscriptionActive=false
        }
        corporateUserRepository.saveAll(users)
        val loginUsers = corporateUserRepository.findByCorporateId(request.id!!)
        loginUsers.forEach { it.subscriptionType = request.subscription }
        loginAccountRepository.saveAll(loginUsers)
        corporateRepository.save(corporateEntity)
    }

    fun listPlans(currency: String?): List<SubscriptionPlansResponse> {

        val plans = subscriptionPlanRepository.searchByCriteria(-1, if (currency.isNullOrBlank()) null else currency)

        return plans.map { SubscriptionPlansResponse.ModelMapper.from(it,getUpdatedByName(it.createdBy!!), getUpdatedByName(it.updatedBy!!)) }
    }

    fun getPlans(corporateId: Long): List<SubscriptionPlansResponse> {
        val plans = subscriptionPlanRepository.findAllByCompanyId(corporateId)
        return plans.map {
            SubscriptionPlansResponse.ModelMapper.from(
                it,
                getUpdatedByName(it.createdBy!!),
                getUpdatedByName(it.updatedBy!!)
            )
        }
    }

    fun createCustomSubscription(
        planRequest: SubscriptionPlansRequest,
        corporateId: Long,
        subscriptionStartDate: Long? = null,
        subscriptionEndDate: Long? = null
    ): SubscriptionPlanEntity {

        val plan = SubscriptionPlanEntity(
            name = planRequest.name,
            currency = planRequest.currency,
            companyId = corporateId,
            companyType = "CORPORATE",
            price = planRequest.price,
            isActive = planRequest.isActive,
            startDate = subscriptionStartDate?.let {  TimeUtil.fromInstantMillis(it) },
            endDate = subscriptionEndDate?.let {  TimeUtil.fromInstantMillis(it) }
        )
        plan.modules = planRequest.modules.map {
            SubscriptionDetailsEntity(
                name = it.name,
                code = it.code,
                threshold = it.threshold?:Int.MAX_VALUE.toLong(),
                unit = it.unit,
                overageRate = it.overageRate,
                trackingDuration = it.trackingDuration,
                plan = plan,
                isUnlimited = it.isUnlimited
            )
        }.toMutableList()
        return subscriptionPlanRepository.save(plan)

    }

    fun createDefaultSubscription(
        planId: Long,
        corporateId: Long,
        subscriptionStartDate: Long?,
        subscriptionEndDate: Long?,
        isActive: Boolean = true
    ): SubscriptionPlanEntity {

        val defaultPlan = subscriptionPlanRepository.findById(planId).orElseThrow {
            ApplicationException(
                ErrorCode.NOT_FOUND
            )
        }
        val plan = SubscriptionPlanEntity(
            name = defaultPlan.name,
            currency = defaultPlan.currency,
            companyId = corporateId,
            companyType = "CORPORATE",
            price = defaultPlan.price,
            startDate = subscriptionStartDate?.let {  TimeUtil.fromInstantMillis(it) },
            endDate = subscriptionEndDate?.let {  TimeUtil.fromInstantMillis(it) },
            isActive = isActive
        )
        plan.modules = defaultPlan.modules.map {
            SubscriptionDetailsEntity(
                name = it.name,
                code = it.code,
                threshold = it.threshold,
                unit = it.unit,
                overageRate = it.overageRate,
                trackingDuration = it.trackingDuration,
                isUnlimited = it.isUnlimited,
                plan = plan
            )
        }.toMutableList()
        return subscriptionPlanRepository.save(plan)
    }

    fun updateSubscriptionPlan(
        planRequest: SubscriptionPlansRequest,
        planId: Long,
        authenticatedUser: AuthenticatedUser
    ): Boolean {

        val plan = subscriptionPlanRepository.findById(planId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        plan.name = planRequest.name
        plan.currency = planRequest.currency
        plan.price = planRequest.price
        plan.isActive = planRequest.isActive

        planRequest.modules.forEach { planDetail ->

            val module = plan.modules.find { it.code == planDetail.code } ?: return@forEach

            module.name = planDetail.name
            module.threshold = planDetail.threshold?:Int.MAX_VALUE.toLong()
            module.unit = planDetail.unit
            module.overageRate = planDetail.overageRate
            module.trackingDuration = planDetail.trackingDuration
            module.isUnlimited = planDetail.isUnlimited
        }
        /* Updating audit info explicitly because if no details changed at parent entity(subscription in this case)
            jpa doesn't update it.*/
        plan.updatedBy = authenticatedUser.userId
        plan.lastUpdatedDate = LocalDateTime.now()

        subscriptionPlanRepository.save(plan)
        return true
    }

    fun getUpdatedByName(createdBy: Long): String {
        val user = loginAccountRepository.findById(createdBy).get()
        return "${user.firstName} ${user.lastName}"
    }

    fun listSubscriptionUsage(corporateId: Long): List<SubscriptionUsageResponse> {
        val usages = subscriptionUsageRepository.findAllByCompanyId(corporateId)
        return usages.map { SubscriptionUsageResponse.ModelMapper.from(it) }
    }

    @Transactional(readOnly = true)
    fun listSubscriptionUsageDetails(corporateId: Long, monthYear: String): SubscriptionUsageResponse? {

        val pair = getDateFromMonthYear(monthYear)
        val from  = pair.first
        val to = pair.second

        val currentDateTime = LocalDateTime.now()

        val corporateEntity = corporateRepository.findById(corporateId).get()

        //TODO This code can be removed once subscription usage data pipeline is stable
        //if usage details requested for current month
        /*var usagePerMonth = if(currentDateTime.month == from.month && currentDateTime.year == from.year) {
            sharedSubscriptionService.generateUsage(corporateEntity, from, to)
        }
        else {
            subscriptionUsageRepository.findByCompanyIdAndDateBetween(corporateId, from, to)
        }*/

        var usagePerMonth = subscriptionUsageRepository.findByCompanyIdAndDateBetween(corporateId, from, to)

        if(usagePerMonth == null) {
            val plan = subscriptionPlanRepository.findByCompanyIdAndIsActive(corporateId, true)
            if (plan != null) {
                usagePerMonth = SubscriptionUsageEntity(
                    companyId = corporateEntity.id!!,
                    companyType = "CORPORATE",
                    currency = plan.currency,
                    date = from,
                    name = plan.name,
                    price = plan.price,
                    startDate = plan.startDate,
                    endDate = plan.endDate
                )
            }
        }
        val modules = usagePerMonth?.modules?.map {
            SubscriptionUsageDetailsResponse.ModelMapper.from(
                it,
                usagePerMonth.currency
            )
        } ?: emptyList()

        return usagePerMonth?.let {
            SubscriptionUsageResponse.ModelMapper.from(
                it,
                modules,
                corporateEntity.subscriptionStartDate,
                corporateEntity.subscriptionEndDate
            )
        }
    }

    private fun getDateFromMonthYear(monthYear: String): Pair<LocalDateTime, LocalDateTime> {
        val split = monthYear.split("-")

        if (split.size != 2) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        val month = split[0].toInt()
        val year = split[1].toInt()

        val startDate = LocalDate.of(year, month, 1)

        return  Pair(TimeUtil.startDateTimeOfMonth(startDate), TimeUtil.lastDateTimeOfMonth(startDate))
    }

    @Transactional(readOnly = true)
    fun getFeatureUsage(id: Long?, featureKey: String, corporateId: Long): List<AbstractUsageResponse> {

        return if(id!=null) {
            val factory = usageFactory.getUsageFactory(featureKey, listOf())
            factory.getHistoricalUsageDetailsResponse(id)

        }
        else {
            val from = TimeUtil.startDateTimeOfMonth()
            val to = TimeUtil.lastDateTimeOfMonth()

            val users = corporateRepository.findById(corporateId).get().users.map { it.id!! }

            log.debug("start time: {}", from)
            log.debug("end time: {}", to)

            val actions = userActionRepository.findAllByActionAndUserIdInAndStartTimeBetween(featureKey, users, from, to)
            usageFactory.getUsageFactory(featureKey, actions).getUsageDetailsResponse()
        }
    }

    fun requestReceipt(corporateId: Long, monthYear: String, authenticatedUser: AuthenticatedUser): Boolean {

        val pair = getDateFromMonthYear(monthYear)
        val from  = pair.first

        val user = loginAccountRepository.findById(authenticatedUser.userId).get()


        genericEmailService.sendEmail(
            subject = "Receipt Request – ${from.month.name} ${from.year}",
            templateName = RECEIPT_REQUEST_MAIL_TEMPLATE,
            variableMap = mapOf(
                "BILLING_MONTH" to "${from.month.name} ${from.year}",
                "DISPLAY_NAME" to "${user.firstName} ${user.lastName}"
            ),
            recipient = listOf(user.email),
            cc = ccList
        )
        return true
    }

    @Transactional
    fun deleteInActivePlan(corporateId: Long) {
        subscriptionPlanRepository.deleteByCompanyIdAndIsActive(corporateId, false)
    }

}