package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.ReferralCodeEntity
import com.centuroglobal.shared.data.pojo.Referral
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.ReferralCodeRepository
import com.centuroglobal.shared.util.StringUtil
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.time.LocalDateTime


private val referralUrl: (webUrl: String, code: String) -> String =
    { webUrl, code -> "${webUrl}/auth/sign-up?referralCode=${code}" }

@Service
class ReferralCodeService(
    @Value("\${app.web-url}")
    private val webUrl: String,
    private val referralCodeRepository: ReferralCodeRepository,
    private val loginAccountRepository: LoginAccountRepository
) {

    private val expiryDays: Long = 30

    fun getReferralCode(userId: Long, loginUserId: Long): Referral {
        val loginAccount: LoginAccountEntity =
            loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.BAD_REQUEST)
        var referralCodeEntity: ReferralCodeEntity? = referralCodeRepository.findFirstByRootUserIdAndExpiryAfter(
            userId,
            LocalDateTime.now()
        )
        if (referralCodeEntity == null) {
            referralCodeEntity = createReferralCode(userId, loginUserId)
        } else {
            referralCodeEntity.expiry = LocalDateTime.now().plusDays(expiryDays)
            referralCodeEntity.lastUpdatedBy = loginUserId
            referralCodeRepository.save(referralCodeEntity)
        }
        return Referral(
            referralCodeEntity.referralCode,
            referralUrl(webUrl, referralCodeEntity.referralCode),
            loginAccount.firstName
        )
    }

    private fun createReferralCode(userId: Long, loginUserId: Long): ReferralCodeEntity {
        var referralCode: String
        do {
            referralCode = StringUtil.createRandomString(8)
        } while (referralCodeRepository.findFirstByReferralCodeAndExpiryAfter(
                referralCode,
                LocalDateTime.now()
            ) != null
        )

        val referralCodeEntity = ReferralCodeEntity(
            referralCode = referralCode,
            expiry = LocalDateTime.now().plusDays(expiryDays),
            rootUserId = userId,
            lastUpdatedBy = loginUserId
        )
        return referralCodeRepository.save(referralCodeEntity)
    }

    fun getUserIdByReferralCode(referralCode: String?): Long? {
        referralCode ?: return null
        val referralCodeEntity = referralCodeRepository.findFirstByReferralCodeAndExpiryAfter(
            referralCode,
            LocalDateTime.now()
        ) ?: return null

        return referralCodeEntity.rootUserId
    }
}