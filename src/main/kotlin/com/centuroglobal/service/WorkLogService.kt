package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.subscription.WorkLogEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.subscription.WorkLogCorporateStats
import com.centuroglobal.shared.data.pojo.subscription.WorkLogRequest
import com.centuroglobal.shared.data.pojo.subscription.WorkLogResponse
import com.centuroglobal.shared.data.pojo.subscription.WorkLogSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.RfpRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.repository.subscription.WorkLogRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}

@Service
class WorkLogService(

    private val corporateRepository: CorporateRepository,
    private val workLogRepository: WorkLogRepository,
    private val caseRepository: CaseRepository,
    private val queryRepository: QueryRepository,
    private val rfpRepository: RfpRepository,
    private val userProfileUtil: UserProfileUtil

) {
    fun create(request: WorkLogRequest, userId: Long): Long {

        val corporate = corporateRepository.getReferenceById(request.corporateId)

        if(request.referenceType != ReferenceType.OTHER && request.referenceId == null) {
            log.error("ReferenceId can not be null for referenceType: ${request.referenceType}")
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        val workLogEntity = WorkLogEntity(
            corporate = corporate,
            description = request.description,
            eventDate = TimeUtil.fromInstantMillis(request.eventDate),
            referenceId = request.referenceId,
            referenceType = request.referenceType,
            timeSpent = request.timeSpent
        )

        val savedEntity = workLogRepository.save(workLogEntity)

        return savedEntity.id!!

    }

    fun list(filter: WorkLogSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser): PagedResult<WorkLogResponse> {

        val page = workLogRepository.searchByCriteria(filter, pageRequest)

        return PagedResult.ModelMapper.from(
            page,
            page.map {
                WorkLogResponse.ModelMapper.from(
                    it,
                    userProfileUtil.retrieveProfile(it.createdBy!!),
                    isDeleteAllowed(it, authenticatedUser)
                )
            }.toList()
        )

    }

    private fun isDeleteAllowed(entity: WorkLogEntity, authenticatedUser: AuthenticatedUser): Boolean {

        if(entity.isDeleted) {
            return false
        }

        if (authenticatedUser.role == Role.ROLE_SUPER_ADMIN.name) {
            return true
        }
        return (entity.createdBy == authenticatedUser.userId && entity.createdDate.month == LocalDateTime.now().month)
    }

    fun delete(id: Long, authenticatedUser: AuthenticatedUser): Boolean {

        val workLog = workLogRepository.findById(id).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        if (isDeleteAllowed(workLog, authenticatedUser)) {
            workLog.isDeleted = true
            workLogRepository.save(workLog)
            return true
        }
        throw ApplicationException(ErrorCode.FORBIDDEN)
    }

    fun usageStats(corporateId: Long, authenticatedUser: AuthenticatedUser): WorkLogCorporateStats {

        val timeSpent = workLogRepository.totalTimeSpentByCorporate(corporateId)

        //TODO add logic to get time allocated from subscription plan
        //val timeAllocated = subscriptionPlanRepository.findAllByCompanyId(corporateId)[0].modules.firstOrNull { it.name == "" }?.threshold?: 0
        val timeAllocated = 1000L

        return WorkLogCorporateStats(timeAllocated, timeSpent, timeAllocated-timeSpent)
    }

    fun referenceIdList(type: ReferenceType, corporateId: Long, authenticatedUser: AuthenticatedUser): List<Long> {

        return when (type) {
            ReferenceType.CASE -> {
                caseRepository.findByCreatedById(corporateId).map { it.id!! }
            }

            ReferenceType.QUERY -> {
                queryRepository.findByCreatedByCorporateId(corporateId).map { it.id!! }
            }

            ReferenceType.RFP -> {
                rfpRepository.findByCreatedByCorporateId(corporateId).filter { it.status!=RfpStatus.DRAFT }.map { it.id!! }
            }

            else -> {
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }

    }

    fun extractOwnerUserIdAndDate(payload: WorkLogRequest): Pair<Long?, LocalDateTime?> {
        return when (payload.referenceType) {
            ReferenceType.CASE -> {
                val entity = caseRepository.findById(payload.referenceId!!).get()
                Pair(entity.createdBy?.id, entity.createdDate)
            }

            ReferenceType.QUERY -> {
                val entity = queryRepository.findById(payload.referenceId!!).get()
                Pair(entity.createdBy.id, entity.createdDate)
            }

            ReferenceType.RFP -> {
                val entity = rfpRepository.findById(payload.referenceId!!).get()
                Pair(entity.createdBy.id, entity.createdDate)
            }
            else -> {
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }
    }


}