package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.LeadEntity
import com.centuroglobal.shared.data.entity.LeadNotificationEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.service.ExpertiseService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional



private const val DUMMY_ID = 0L


@Service
class LeadNotificationService(
    @Value("\${app.web-url}")
    private val webUrl: String,
    @Value("\${app.lead-notification.cleanup-after-sent-in-days}")
    private val cleanupAfterSentInDays: Int,
    @Value("\${app.lead-notification.max-email-per-cron}")
    private val maxEmailPerCron: Int,
    @Value("\${app.lead-notification.centuro-address}")
    private val cgEmail: String,
    private val leadNotificationRepository: LeadNotificationRepository,
    private val leadRepository: LeadRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val corporateRepository: CorporateRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service,
    private val countryService: CountryService,
    private val expertiseService: ExpertiseService
) {

    @Transactional
    fun createNotification(lead: LeadEntity, type: LeadNotificationType) {
        val notificationList = mutableListOf<LeadNotificationEntity>()

        when (type) {
            LeadNotificationType.NEW_LEAD -> {
                expertUserRepository.findAllByCountryCodeAndExpertiseIdAndStatusAndIdNot(
                    lead.countryCode, lead.expertises.map { it.id }, AccountStatus.ACTIVE, lead.createdBy
                ).map { it.id!! }
                    .ifEmpty { listOf(DUMMY_ID) }
                    .forEach {
                        notificationList.add(
                            LeadNotificationEntity(
                                type = type,
                                state = LeadNotificationState.CREATED,
                                leadId = lead.id!!,
                                userId = it
                            )
                        )
                    }
            }
            LeadNotificationType.RESPOND -> {
                when (lead.leadType) {
                    LeadType.EXPERT -> {
                        val expertUser =
                            loginAccountRepository.findByIdAndStatus(lead.leadTypeId!!, AccountStatus.ACTIVE)
                        if (expertUser != null) {
                            notificationList.add(
                                LeadNotificationEntity(
                                    type = LeadNotificationType.RESPOND,
                                    state = LeadNotificationState.CREATED,
                                    leadId = lead.id!!,
                                    userId = expertUser.id!!
                                )
                            )
                        }
                    }
                    LeadType.CORPORATE -> {
                        val corporate = corporateRepository.findByIdAndStatus(lead.leadTypeId!!, CorporateStatus.ACTIVE)
                        if (corporate != null) {
                            val corporateUsers = corporateUserRepository.findAllByCorporateIdAndStatus(
                                corporate.id!!, AccountStatus.ACTIVE
                            )
                            corporateUsers.forEach {
                                notificationList.add(
                                    LeadNotificationEntity(
                                        type = LeadNotificationType.RESPOND,
                                        state = LeadNotificationState.CREATED,
                                        leadId = lead.id!!,
                                        userId = it.id!!
                                    )
                                )
                            }
                        }
                    }
                    else->{

                    }
                }
            }
        }


        if (notificationList.size > 0) leadNotificationRepository.saveAll(notificationList)
    }






}