package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.view.ExpertiseView
import com.centuroglobal.shared.data.pojo.Expertise
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.view.ExpertiseViewRepository
import mu.KotlinLogging
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import java.util.*

private val log = KotlinLogging.logger {}

@Service
class ExpertiseService(private val expertiseCacheService: ExpertiseCacheService) {

    fun listExpertise(): List<Expertise> {
        val expertiseList: MutableList<Expertise> = mutableListOf()
        expertiseCacheService.listExpertise()
            .groupBy { it.groupName }
            .forEach { (k, v) ->
                if (k != "") {
                    val childList = v.map { Expertise(id = it.id, name = it.name) }
                    expertiseList.add(
                        Expertise(name = k, children = childList)
                    )
                } else {
                    expertiseList.addAll(
                        v.map { Expertise(id = it.id, name = it.name) }
                    )
                }
            }
        return expertiseList.sortedBy { it.name }
    }

    fun searchByGroupNameOrNamePrefix(prefix: String?): List<Expertise> {
        val theList = expertiseCacheService.listExpertise()

        return if (prefix.isNullOrBlank()) {
            theList
                .map { Expertise(id = it.id, name = it.name) }
                .sortedBy { it.name }
        } else {
            theList
                .filter {
                    it.groupName.lowercase(Locale.getDefault()).startsWith(prefix.lowercase(Locale.getDefault())) ||
                            it.name.lowercase(Locale.getDefault()).startsWith(prefix.lowercase(Locale.getDefault()))
                }
                .map { Expertise(id = it.id, name = it.name) }
                .sortedBy { it.name }
        }
    }

    fun retrieveExpertiseById(id: Int): Expertise {
        return expertiseCacheService.listExpertise().map { Expertise(id = it.id, name = it.name) }
            .find { it.id == id } ?: throw ApplicationException(ErrorCode.EXPERTISE_INVALID)
    }

    fun retrieveExpertiseByIds(ids: List<Int>): List<ExpertiseView> {
        return expertiseCacheService.listExpertise().filter { ids.contains(it.id) }
    }
}

@Service
class ExpertiseCacheService(private val expertiseViewRepository: ExpertiseViewRepository) {

    @Cacheable("expertise")
    fun listExpertise(): List<ExpertiseView> {
        log.debug("Retrieving expertise from repository")
        return expertiseViewRepository.findAll()
    }
}
