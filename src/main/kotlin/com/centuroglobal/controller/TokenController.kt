package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.service.BandService
import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "Token Controller", description = "Centuro Global Token API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/switch-role")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
class TokenController(
    private val tokenVerificationService: TokenVerificationService
) {

    @PostMapping
    @Operation(
        summary = "Switch role for User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun switchRole(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam("role") role: Role
    ): Response<TokenResult> {

        val tokenResult = tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role)

        return Response(true, tokenResult)

    }

}