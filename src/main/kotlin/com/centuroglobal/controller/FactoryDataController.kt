package com.centuroglobal.controller

import com.centuroglobal.costofliving.data.payload.Currency
import com.centuroglobal.costofliving.service.CostOfLivingService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.AuthoritiesEntity
import com.centuroglobal.shared.data.entity.DocumentMasterEntity
import com.centuroglobal.shared.data.entity.ImmigrationCategoryEntity
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.case.SICCode
import com.centuroglobal.facade.CountryFacade
import com.centuroglobal.facade.ExpertiseFacade
import com.centuroglobal.facade.FactoryDataFacade
import com.centuroglobal.service.*
import com.centuroglobal.service.CaseService
import com.centuroglobal.shared.data.entity.BusinessIndustryEntity
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse

import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult

@RestController
@Tag(name = "Country", description = "Centuro Global Country & Region API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/country")
class CountryController(
    private val countryFacade: CountryFacade
) {

    @GetMapping
    @Operation(
        summary = "List Countries Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun list(
        @RequestParam(name = "prefix", required = false)
        @Parameter(
            name = "prefix",
            required = false,
            description = "Country name prefix. Sending countries without prefix will return all countries."
        )
        prefix: String?
    ): DeferredResult<Response<List<Country>>> {
        return ResponseUtil.toDeferredResult(countryFacade.listCountries(prefix))
    }

    @GetMapping("/{countryCode}")
    @Operation(
        summary = "Retrieve country by ",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun retrieve(
        @PathVariable(name = "countryCode")
        countryCode: String
    ): DeferredResult<Response<Country>> {
        return ResponseUtil.toDeferredResult(countryFacade.retrieveByCountryCode(countryCode))
    }
}

@RestController
@Tag(name = "Expertise", description = "Centuro Global Expertise API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/expertise")
class ExpertiseController(
    private val expertiseFacade: ExpertiseFacade
) {

    @GetMapping
    @Operation(
        summary = "List Expertise Request. If prefix is defined, the result will never by grouped.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun list(
        @RequestParam(name = "prefix", required = false)
        @Parameter(name = "prefix", required = false, description = "Expertise group or name prefix.")
        prefix: String?,
        @RequestParam(name = "grouped", required = false, defaultValue = "false")
        @Parameter(
            name = "grouped", required = false,
            description = "Should the return result be grouped? This value is ignored if prefix is not null or not empty."
        )
        grouped: Boolean?
    ): DeferredResult<Response<List<Expertise>>> {
        return ResponseUtil.toDeferredResult(expertiseFacade.listExpertise(prefix, grouped ?: false))
    }
}

@RestController
@Tag(name = "Misc Data", description = "Centuro Global Misc Factory Data API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data")
class MiscFactoryDataController(
    private val factoryDataFacade: FactoryDataFacade,
    private val bandService: BandService
) {

    @GetMapping("/company-size")
    @Operation(
        summary = "List of Enums for valid Company Sizes.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listCompanySizes(): DeferredResult<Response<List<EnumValue<String>>>> {
        return ResponseUtil.toDeferredResult(factoryDataFacade.retrieveCompanySizeList())
    }

    @GetMapping("/features")
    @Operation(
        summary = "List of features",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listFeatures(): Response<List<FeatureData>> {
        return Response(true, bandService.featureListing())
    }
}

@RestController
@Tag(name = "Case", description = "Centuro Global Case API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/case")
class CaseDataController(
    private val caseService: CaseService,
    private val documentMasterService: DocumentMasterService,
    private val blueprintService: BlueprintService,
    private val migrationservice: MigrationService
) {

    @GetMapping("/sic-codes")
    @Operation(
        summary = "List SIC Codes Request.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listSicCodes(
    ): Response<List<SICCode>> {
        val codes = caseService.listSICCodes()
        return Response(true, codes)
    }

    @GetMapping("/documents")
    @Operation(
        summary = "Case Documents master data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun getDocument(): Response<List<DocumentMasterEntity>> {
        return Response(true, documentMasterService.getDocument())
    }

    @GetMapping("/case-category")
    @Operation(
        summary = "List of Enums for Case Category.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listCompanySizes(): Response<List<CaseCategory>> {
        val categories = blueprintService.getCategories()
        return Response(true, categories)
    }

    @GetMapping("/case-subcategory")
    @Operation(
        summary = "List of Case statuses for case Sub categories",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listCaseSubcategories( @RequestParam(name = "casesubcategory", required = true)
                               @Parameter(name = "casesubcategory", required = true, description = "Case sub Category")
                               caseSubCategory: String): Response<List<Map<String, Any>>> {
        val subCategories = caseService.getSubCategories(caseSubCategory)
        return Response(true, subCategories)
    }

    @GetMapping("/case-categories")
    @Operation(
        summary = "List of Case statuses for case categories",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listCaseCategories(): Response<List<String>> {
        val subCategories = caseService.getCategories()
        return Response(true, subCategories)
    }

    @GetMapping("/case-statuses")
    @Operation(
        summary = "List of all distinct Case statuses",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listDistinctStatues(): Response<List<Map<String, String>>> {
        val statuses = caseService.getStatues()
        return Response(true, statuses)
    }

    @GetMapping("/case-milestones")
    @Operation(
        summary = "List of all distinct Case milestones",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listDistinctMilestones(): Response<List<Map<String, String>>> {
        val statuses = caseService.getDistinctMilestones()
        return Response(true, statuses)
    }

}


@RestController
@Tag(name = "Immigration", description = "Centuro Global Immigration API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/immigration")
class ImmigrationVisaCategoryController(
    private val immigrationService: ImmigrationService
) {

    @GetMapping("/visaCategory")
    @Operation(
        summary = "Get Visa Category",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun getVisaCategory(
        @RequestParam(name = "visaType", required = false) visaType: String?
    ): Response<List<ImmigrationCategoryEntity>> {
        val visaCategory = immigrationService.getVisaCategory(visaType)
        return Response(true, visaCategory)
    }
}


@RestController
@Tag(name = "Authorities", description = "Centuro Global Authorities API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/authorities")
class AuthoritiesController(
    private val adminMasterDataService: AdminMasterDataService
) {

    @GetMapping
    @Operation(
        summary = "Get Authorities",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun getAuthorities(): Response<List<AuthoritiesEntity>> {
        val authorities = adminMasterDataService.getAuthorities()
        return Response(true, authorities)
    }
}

@RestController
@Tag(name = "Business Industry", description = "Centuro Global Business Industry API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/business-industry")
class BusinessIndustryController(
    private val adminMasterDataService: AdminMasterDataService
) {

    @GetMapping
    @Operation(
        summary = "Get Business Industry List",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun getAuthorities(): Response<List<BusinessIndustryEntity>> {
        val authorities = adminMasterDataService.getBusinessIndustries()
        return Response(true, authorities)
    }
}

@RestController
@Tag(name = "Currency", description = "Centuro Global Currency API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/factory-data/currency")
class CurrencyController(
    private val costOfLivingService: CostOfLivingService
){
    @GetMapping
    @Operation(
        summary = "Get Currency list",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
    )
    fun getCurrencies(
    ): Response<List<Currency>> {
        val currencyList = costOfLivingService.getCurrencies()
        return Response(true, currencyList)
    }
}