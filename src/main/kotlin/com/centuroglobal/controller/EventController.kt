package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.event.*
import com.centuroglobal.facade.EventFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Min

@RestController
@Tag(name = "Event", description = "Centuro Global Event API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/event")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
class EventController(
    private val eventFacade: EventFacade
) {

    @GetMapping("/{eventId}")
    @Operation(
        summary = "Retrieve a Event",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(
        @PathVariable eventId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<EventDetails>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.retrieve(eventId, authenticatedUser)
        )
    }

    @GetMapping("/event-listing")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Event name or event description to search.")
        search: String?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "Starting date from which event is searched.")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "Date upto which event is searched.")
        to: Long?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status", required = false,
            description = "Event status.",
            schema = Schema(allowableValues = ["PUBLISHED", "UNPUBLISHED", "ONGOING", "UPCOMING", "PAST", "CANCELLED"])
        )
        status: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String
    ): DeferredResult<Response<PagedResult<EventDetails>>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.listEvents(
                EventSearchFilter.Builder.build(search, from, to, status),
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            )
        )
    }


    @GetMapping("/speaker-host/{eventId}")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun speakerList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable eventId: Long,
        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int
    ): DeferredResult<Response<PagedResult<EventSpeaker>>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.speakerList(
                eventId, if (pageIndex >= 0) PageRequest.of(pageIndex, pageSize)
                else Pageable.unpaged(), authenticatedUser
            )
        )
    }

    @GetMapping("/session/{eventId}")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun sessionList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable eventId: Long,
        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int
    ): DeferredResult<Response<List<List<EventSession>>>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.sessionList(eventId, pageIndex, authenticatedUser)
        )
    }

    @GetMapping("/invitee/{eventId}")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun inviteeList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable eventId: Long,
        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int
    ): DeferredResult<Response<PagedResult<EventClientProfile>>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.inviteeList(
                eventId, if (pageIndex >= 0) PageRequest.of(pageIndex, pageSize)
                else Pageable.unpaged(), authenticatedUser
            )
        )
    }

    @GetMapping("/attendee/{eventId}")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun attendeeList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable eventId: Long,
        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int
    ): DeferredResult<Response<PagedResult<EventClientProfile>>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.attendeeList(
                eventId, if (pageIndex >= 0) PageRequest.of(pageIndex, pageSize)
                else Pageable.unpaged(), authenticatedUser
            )
        )
    }

    @PostMapping("/rsvp/{eventId}")
    @Operation(
        summary = "Events",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun rsvp(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable eventId: Long
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(
            eventFacade.rsvp(
                eventId, authenticatedUser
            )
        )
    }
}