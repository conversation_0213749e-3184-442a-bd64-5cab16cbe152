package com.centuroglobal.controller

import com.centuroglobal.annotation.IsExpertOrSupplier
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.ExpertProfile
import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.shared.data.payload.account.UpdateExpertProfileRequest
import com.centuroglobal.shared.data.pojo.CorporateUsers
import com.centuroglobal.shared.data.pojo.ExpertUserReferenceData
import com.centuroglobal.shared.data.pojo.ReferenceData
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import org.springframework.web.multipart.MultipartFile
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.springframework.http.MediaType

@RestController
@Tag(name = "Expert Profile", description = "Centuro Global Expert Profile API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert/{userId}/profile")
@IsExpertOrSupplier
class ExpertProfileController(
    private val expertFacade: ExpertFacade,
    private val expertUserService: ExpertUserService
) {

    @GetMapping
    @Operation(
        summary = "Retrieve Expert Profile",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(@PathVariable userId: Long): DeferredResult<Response<ExpertProfile>> {
        return ResponseUtil.toDeferredResult(expertFacade.retrieveProfile(userId))
    }

    @PutMapping
    @Operation(
        summary = "Update Expert Profile",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: UpdateExpertProfileRequest
    ): DeferredResult<Response<ExpertProfile>> {
        return ResponseUtil.toDeferredResult(expertFacade.updateProfile(userId, request, authenticatedUser.userId))
    }

    @PutMapping(value = ["/photo"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Update Profile Picture. Return full path url of the image if successful.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadProfilePicture(
        @PathVariable userId: Long,
        @RequestParam(value = "photo") photo: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(expertFacade.uploadProfilePicture(userId, photo, user.userId))
    }

    @DeleteMapping("/photo")
    @Operation(
        summary = "Delete Profile Picture",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteProfilePicture(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(expertFacade.deleteProfilePicture(userId, user.userId))
    }

    @PutMapping(value = ["/company-logo"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Update Company Logo. Return full path url of the image if successful.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadCompanyLogo(
        @PathVariable userId: Long,
        @RequestParam(value = "companyLogo") photo: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(expertFacade.uploadCompanyLogo(userId, photo, user.userId))
    }

    @DeleteMapping("/company-logo")
    @Operation(
        summary = "Delete Company Logo",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteCompanyLogo(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(expertFacade.deleteCompanyLogo(userId, user.userId))
    }
    @GetMapping("expert-users")
    @Operation(
        summary = "expert-company users Listing for given user's expert company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun expertUsersByCompanyIds(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser)
            : Response<List<ExpertUserReferenceData>> {
        return Response(true, expertUserService.retrieveExpertUsersByUserId(userId))
    }

    @GetMapping("companies")
    @Operation(
        summary = "Corporate Listing for given user's Partner's associated corporates",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun expertAssociatedPartnerCorporates(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser)
            : Response<List<ReferenceData>> {
        return Response(true, expertUserService.retrieveExpertCorporates(userId, authenticatedUser))
    }

    @GetMapping("{corporateId}/users")
    @Operation(
        summary = "Corporate User Listing - based on CorporateId",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun corporateUserListing(
        @PathVariable userId: Long,
        @PathVariable("corporateId") corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CorporateUsers>> {
        return Response(true, expertUserService.retrieveCorporateUsers(authenticatedUser, corporateId))
    }
}