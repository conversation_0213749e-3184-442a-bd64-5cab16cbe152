package com.centuroglobal.controller

import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Expert Company Management", description = "Centuro Global Admin Expert Company API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/expert")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #companyId, 'EXPERT', 'USERS')")
class AdminExpertController(
    private val partnerService: PartnerService,
    private val expertUserService: ExpertUserService
) {
    //TODO check about this API
//    @PutMapping("/contract-details/{companyId}")
//    @Operation(
//        summary = "Update Contract Details",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun updateContractDetails(
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
//        @PathVariable companyId: Long,
//        @Valid @RequestBody request: ExpertProfile
//    ): DeferredResult<Response<String>> {
//        return ResponseUtil.toDeferredResult(expertFacade.updateContractDetails(companyId, request, user))
//    }

    @GetMapping("/{companyId}/secondary")
    @Operation(
        summary = "Retrieve Secondary Experts",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveSecondaryExpertList(
        @PathVariable companyId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    ): Response<MutableList<Client>> {
        return Response(true,expertUserService.retrieveSecondaryExpert(companyId, authenticatedUser.partnerId))
    }

    @PutMapping("/{companyId}")
    @Operation(
        summary = "Update Expert Company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "UPDATE_EXPERT")
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @PathVariable companyId: Long,
        @Valid @RequestBody request: ExpertCompanyProfile
    ): Response<Long> {
        return Response(true, expertUserService.updateCompanyProfile(companyId, request).id)
    }

    @GetMapping("/{companyId}")
    @Operation(
        summary = "Retrieve Expert Company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(@PathVariable companyId: Long): Response<ExpertCompanyProfile> {
        return Response(true, expertUserService.retrieveExpertCompany(companyId))
    }

    //TODO check about this API
    @GetMapping("/primary")
    @Operation(
        summary = "Retrieve Primary Experts - Get Id and Name, Email",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrievePrimaryExpert(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<MutableList<ExpertUserSummary>> {
        return Response(true, expertUserService.retrievePrimaryExpert(authenticatedUser.partnerId))
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Create Expert Company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreatePrimaryExpertUserRequest
    ): Response<Long> {
        authenticatedUser.partnerId?.let { request.companyProfile.partnerId = it }
        return Response(true,  expertUserService.createPrimaryExpertUser(authenticatedUser.userId, request))
    }

    //this can be deprecated
//    @PutMapping(value = ["/{userId}/company-logo"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
//    @Operation(
//        summary = "Update Company Logo. Return full path url of the image if successful.",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun uploadCompanyLogo(
//        @PathVariable userId: Long,
//        @RequestParam(value = "companyLogo") photo: MultipartFile,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): DeferredResult<Response<String>> {
//        return ResponseUtil.toDeferredResult(expertFacade.uploadCompanyLogo(userId, photo, user.userId))
//    }
//
//    //this can be deprecated
//    @DeleteMapping("/{userId}/company-logo")
//    @Operation(
//        summary = "Delete Company Logo",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun deleteCompanyLogo(
//        @PathVariable userId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): DeferredResult<Response<String>> {
//        return ResponseUtil.toDeferredResult(expertFacade.deleteCompanyLogo(userId, user.userId))
//    }

//TODO Remove usage of this
//    @GetMapping("/expert-company-list")
//    @Operation(
//        summary = "Retrieve Expert Company Profile - Id and Name",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun retrieveExpertCompanyProfile(
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//    ): Response<MutableList<ExpertCompany>> {
//        return Response(true, expertUserService.retrieveExpertCompanyProfile(authenticatedUser.partnerId))
//    }

    @GetMapping("expert-users")
    @Operation(
        summary = "expert-company users Listing for given expert company ids",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun expertUsersByCompanyIds(
        @RequestParam("companyIds") companyIds: String?,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser)
    : Response<List<ExpertUserReferenceData>> {
        return Response(true, expertUserService.retrieveExpertUsersByCompanyIds(authenticatedUser.partnerId,companyIds?.split(",")
            ?.map { it.toLong() }?: emptyList()
        ))
    }

    @GetMapping("/companies")
    @Operation(
        summary = "List of expert company - Name and Id only",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listExpertCompany(
        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(
            name = "isPartnerCompany",
            required = false,
            description = "is Partner Company or not."
        )
        isPartnerCompany: Boolean?,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<ReferenceData>> {
        return Response(true, expertUserService.retrieveExpertCompany(authenticatedUser.partnerId, isPartnerCompany))
    }

    //TODO URL Changes
    @GetMapping("/listing")
    @Operation(
        summary = "expert company listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listing(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Expert company's name")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "User's country to search.")
        country: String?,


        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "Membership status to search.",
        )
        status: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(
            name = "isPartnerCompany",
            required = false,
            description = "is Partner Company or not."
        )
        isPartnerCompany: Boolean?,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "Partner Id")
        partnerId: Long?,

        @RequestParam(name = "type", required = false, defaultValue = "")
        @Parameter(name = "type", required = false, description = "Expert Type(Supplier, Expert)")
        type: ExpertCompanyType?

        ): Response<ListingWithStatsDetails<ExpertList>?> {

        return Response(
            true, partnerService.listExpertStats(
                authenticatedUser.partnerId ?: partnerId,
                PartnerExpertSearchFilter.Builder.build(
                    search,
                    country,
                    status,
                    isPartnerCompany,
                    type
                ),
                PageRequest.of(
                    pageIndex,
                    if (isDownload) Int.MAX_VALUE else pageSize,
                    SearchConstant.SORT_ORDER(sortBy, sort)
                ), authenticatedUser
            )
        )
    }

    @PutMapping("/{companyId}/status")
    @Operation(
        summary = "Update company status Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @RequestBody @Valid updateCompanyStatusRequest:UpdateCompanyStatusRequest
    ):Response<Boolean>{
        return Response(true, partnerService.updateCompanyStatus(updateCompanyStatusRequest,authenticatedUser))
    }

    @DeleteMapping("/{companyId}")
    @Operation(
        summary = "Remove expert Company and all its expert users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable("companyId") companyId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        return Response(true, expertUserService.deleteExpertCompany(companyId))
    }
}