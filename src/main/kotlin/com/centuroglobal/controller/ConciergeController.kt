package com.centuroglobal.controller

import com.centuroglobal.annotation.IsClient
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.ConciergeRequest
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.facade.ConciergeFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid

@RestController
@Tag(name = "Concierge", description = "Centuro Global Concierge API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/concierge")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
@Validated
class ConciergeController(private val conciergeFacade: ConciergeFacade) {
    @PostMapping
    @Operation(
        summary = "Post concierge service request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun post(
        @Valid @RequestBody request: ConciergeRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(conciergeFacade.post(request, authenticatedUser.userId))
    }
}
