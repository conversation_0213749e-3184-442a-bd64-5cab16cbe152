package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriber
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.facade.BlueprintFacade
import com.centuroglobal.service.BlueprintService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.AppConstant.REQUEST_ID_HEADER
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Blueprint
import com.centuroglobal.shared.data.pojo.CountrySummary
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintStep
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "Blueprint", description = "Centuro Global Blueprint API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/blueprint")
class BlueprintController(
    private val blueprintFacade: BlueprintFacade,
    private val blueprintService: BlueprintService
) {

    @GetMapping
    @Operation(
        summary = "List Blueprints",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listActive(): DeferredResult<Response<List<CountrySummary>>> {
        return ResponseUtil.toDeferredResult(blueprintFacade.listActive())
    }

    @GetMapping("/{countryCode}")
    @Operation(
        summary = "Retrieve Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
    fun retrieve(
        @PathVariable countryCode: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<Blueprint>> {
        return ResponseUtil.toDeferredResult(blueprintFacade.retrieveBluePrint(countryCode, null))
    }

    @GetMapping("/{countryCode}/download-pdf/{step}")
    @Operation(
        summary = "Download Pdf",
        responses = [ApiResponse(content = [Content(mediaType = "application/pdf")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriber
    fun downloadPdf(
        @PathVariable countryCode: String,
        @PathVariable step: StepName
    ): ResponseEntity<StreamingResponseBody>? {
        return blueprintFacade.downloadPdf(countryCode, step)
    }

    @GetMapping("/{countryCode}/teaser")
    @Operation(
        summary = "Retrieve Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "BLUEPRINT")
    fun retrieveTeaser(
        @LogValue @PathVariable countryCode: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestAttribute(value = REQUEST_ID_HEADER, required = false) @Parameter(hidden = true) requestId: String?
    ): DeferredResult<Response<Blueprint>> {
        //TODO: corporate subscription removal
        //return ResponseUtil.toDeferredResult(blueprintFacade.retrieveTeaser(countryCode))
        return ResponseUtil.toDeferredResult(blueprintFacade.retrieveBluePrint(countryCode, requestId))
    }

    @GetMapping("/step/{countryCode}/{stepName}")
    @Operation(
        summary = "Retrieve Step",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveStep(
        @PathVariable countryCode: String,
        @PathVariable stepName: StepName
    ): DeferredResult<Response<BlueprintStep>> {
        return ResponseUtil.toDeferredResult(blueprintFacade.retrieveStep(countryCode, stepName))
    }
}
