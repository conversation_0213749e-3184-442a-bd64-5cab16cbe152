package com.centuroglobal.controller

import com.centuroglobal.facade.ClientReferralFacade
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.referral.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult

private val DEFAULT_SORT_ORDER_CLIENT = Sort.by(
    Sort.Order(Sort.Direction.DESC, "createdDate"),
    Sort.Order(Sort.Direction.ASC, "displayName", Sort.NullHandling.NULLS_LAST)
)

@RestController
@Tag(name = "Referral", description = "Centuro Global Admin Client Referral API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/referral")
@PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'CLIENT_REFERRALS')")
class AdminClientReferralController(
    private val clientReferralFacade: ClientReferralFacade
) {

    @PostMapping
    @Operation(
        summary = "Post a referral",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @Valid @RequestBody request: ClientReferralCreateRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<ClientReferralDetails>> {
        return ResponseUtil.toDeferredResult(clientReferralFacade.create(request, authenticatedUser))
    }

    @PutMapping("/{clientReferralId}")
    @Operation(
        summary = "update a referral",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable clientReferralId: Long,
        @Valid @RequestBody request: ClientReferralCreateRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<ClientReferralDetails>> {
        return ResponseUtil.toDeferredResult(clientReferralFacade.update(clientReferralId, request, authenticatedUser))
    }

    @GetMapping("/{clientReferralId}")
    @Operation(
        summary = "Get referral details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun get(
        @PathVariable clientReferralId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    )
            : DeferredResult<Response<ClientReferralDetails>> {
        return ResponseUtil.toDeferredResult(clientReferralFacade.get(clientReferralId, authenticatedUser))
    }

    @GetMapping("/client-referral-list")
    @Operation(
        summary = "Search Referral",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun search(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid search: ClientReferralSearchCriteria
    ): DeferredResult<Response<PagedResult<ClientReferralSummary>>> {
        return ResponseUtil.toDeferredResult(
            clientReferralFacade.search(
                ClientReferralSearchFilter.Builder.build(search),
                PageRequest.of(
                    search.pageIndex,
                    search.pageSize,
                    SearchConstant.SORT_ORDER(search.sortBy, search.sort)
                ),
                authenticatedUser
            )
        )
    }

    @DeleteMapping("/{clientReferralId}")
    @Operation(
        summary = "Delete a Referral",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable clientReferralId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(clientReferralFacade.delete(clientReferralId, authenticatedUser.userId))
    }

    @GetMapping("/{clientReferralId}/experts")
    @Operation(
        summary = "Referral Expert List",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleMemberSearch(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @PathVariable
        clientReferralId: Long,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "User's name or company name to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCode", required = false, description = "Country of users")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(
            name = "expertiseId",
            required = false,
            description = "Expertise id in case of userType is expert selected."
        )
        expertiseId: String
    ): DeferredResult<Response<PagedResult<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            clientReferralFacade.clientReferralExpertSearch(
                clientReferralId,
                ExpertSearchFilter.Builder.build(search, countryCode, expertiseId),
                if (pageIndex >= 0) PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER_CLIENT)
                else Pageable.unpaged(),
                authenticatedUser
            )
        )
    }
}