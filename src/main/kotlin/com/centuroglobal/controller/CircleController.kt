package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpert
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.circle.CircleMessageTrailRequest
import com.centuroglobal.shared.data.pojo.circle.CircleResponseTrail
import com.centuroglobal.facade.ExpertCircleFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid

@RestController
@Tag(name = "Admin Circle API", description = "Centuro Global Admin Circle API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/circle")
@IsSuperAdminOrAdminOrExpert
class CircleController(
    private val expertCircleFacade: ExpertCircleFacade
) {


    @GetMapping("/circle-message-trail/{circleId}")
    @Operation(
        summary = "Get Message Trail",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getMessageTrail(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable circleId: Long,
        @RequestParam lastMessageDateTime: Long
    ): DeferredResult<Response<List<List<CircleResponseTrail>>>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.getMessageTrail(circleId, lastMessageDateTime, authenticatedUser)
        )
    }

    @PostMapping("/circle-message-trail/{circleId}")
    @Operation(
        summary = "Save Message Trail",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun saveMessageTrail(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable circleId: Long,
        @Valid @RequestBody circleMessageTrailRequest: CircleMessageTrailRequest
    ): DeferredResult<Response<List<List<CircleResponseTrail>>>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.saveMessageTrail(circleId, circleMessageTrailRequest, authenticatedUser)
        )
    }
}