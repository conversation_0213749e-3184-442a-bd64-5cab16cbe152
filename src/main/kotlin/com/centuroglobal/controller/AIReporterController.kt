package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdmin
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.fasterxml.jackson.databind.JsonNode
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "AI Reporter", description = "Centuro Global AI Reporter API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/reporter")
@IsSuperAdminOrAdmin
class AIReporterController(
    private val pythonApiClient: PythonApiClient
) {

    @PostMapping
    @Operation(
        summary = "AI Case Reporter",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun caseReporter(
        @RequestBody chatRequest: AIMessageRequest
    ): Response<JsonNode> {
        val response = pythonApiClient.caseReporter(chatRequest)
        return Response(true, response)
    }
}
