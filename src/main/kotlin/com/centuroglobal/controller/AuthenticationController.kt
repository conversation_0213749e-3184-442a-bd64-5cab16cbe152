package com.centuroglobal.controller

import com.centuroglobal.data.payload.auth.*
import com.centuroglobal.facade.PasswordFacade
import com.centuroglobal.facade.TokenVerificationFacade
import com.centuroglobal.service.ApiAuthService
import com.centuroglobal.service.PasswordService
import com.centuroglobal.service.TermsAndConditionService
import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.TermsAndCondition
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.VerifiedUser
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult

@RestController
@Tag(name = "Authentication", description = "Centuro Global Authentication API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/auth")
class AuthenticationController(
    private val passwordFacade: PasswordFacade,
    private val passwordService: PasswordService,
    private val apiAuthService: ApiAuthService
) {

    @PostMapping("/token")
    @Operation(
        summary = "Token Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun token(@RequestBody request: TokenRequest): DeferredResult<Response<TokenResult>>? {
        // NOTE: Just a placeholder for swagger docs
        return null
    }

    @PostMapping("/linkedin-auth")
    @Operation(
        summary = "LinkedIn Token Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun linkedinAuth(@RequestBody request: LinkedinAuthRequest): DeferredResult<Response<TokenResult>>? {
        // NOTE: Just a placeholder for swagger docs
        return null
    }

    @PostMapping("/recover-password")
    @Operation(summary = "Recover Password",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun recoverPassword(@RequestBody request: RecoverPasswordRequest): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(passwordFacade.recoverPassword(request))
    }

    @PostMapping("/reset-password")
    @Operation(summary = "Reset Password",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun resetPassword(@RequestBody request: ResetPasswordRequest): DeferredResult<Response<TokenResult>> {
        return ResponseUtil.toDeferredResult(passwordFacade.resetPassword(request))
    }

    @PostMapping("/change-password")
    @Operation(
        summary = "Change Password",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun changePassword(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody changePasswordRequest: ChangePasswordRequest
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(
            passwordFacade.changePassword(
                authenticatedUser.userId,
                changePasswordRequest
            )
        )
    }

    @PostMapping("/logout")
    @Operation(
        summary = "Logout ",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun logout(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    ): Response<Boolean> {
        return Response(true,passwordService.logout(authenticatedUser.userId))
    }

    // Used from python container to authorize requests
    @GetMapping("/validate")
    @Operation(
        summary = "Validate Token ",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun validateToken(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam userId: Long,
        @RequestParam type: String,
    ): Response<Map<String, Any?>> {
        return Response(true, apiAuthService.validateUser(userId, authenticatedUser, type, "USERS"))
    }

    @PutMapping("/approve/{uuid}")
    @Operation(description =  "Approve UUID for login account",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")])
    fun approveLoginAccountUuid(
        @PathVariable("uuid") uuid: String
    ): DeferredResult<Response<Boolean>> {
        return  ResponseUtil.toDeferredResult(passwordFacade.loginAccountUuidApproval(uuid))
    }
}

@RestController
@Tag(name = "Email Code Validation", description = "Centuro Global Email Code Validation API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/auth")
class TokenValidationController(
    private val tokenVerificationFacade: TokenVerificationFacade,
    private val tokenVerificationService: TokenVerificationService
) {

    @GetMapping("/validate-token")
    @Operation(summary = "Verify code validity",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun validateToken(
        @RequestParam(name = "code", required = true) code: String
    ): DeferredResult<Response<VerifiedUser>> {
        return ResponseUtil.toDeferredResult(tokenVerificationFacade.validateCode(code))
    }

    @PutMapping("/extend-token")
    @Operation(summary = "Extend code validity",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun extendTokenValidity(
        @RequestParam(name = "code", required = true) code: String
    ): Response<Boolean> {
        return Response(true, tokenVerificationService.extendTokenValidity(code))
    }
}

@RestController
@Tag(name = "Terms and Conditions", description = "Centuro Global Email Code terms and conditions API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/auth")
class TAndCAuthController(
    private val tokenVerificationService: TokenVerificationService,
    private val termsAndConditionService: TermsAndConditionService
) {

    @GetMapping("/t-and-c")
    @Operation(summary = "Verify code validity",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun getTermsAndConditions(
        @RequestParam(name = "code", required = true) code: String
    ): Response<TermsAndCondition> {
        return Response(true, termsAndConditionService.retrieveTermsAndConditionData(code))
    }

    @PutMapping("/t-and-c")
    @Operation(
        summary = "Update Last Terms and conditions View Date",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @RequestParam(name = "code", required = true) code: String,
        @RequestParam(name = "status", required = true) status: Boolean
    ): Response<String> {
        val user = tokenVerificationService.getUserFromToken(code)
        termsAndConditionService.update(user.email, status, true, status)
        return Response(success = true, payload = "SUCCESS")
    }
}
