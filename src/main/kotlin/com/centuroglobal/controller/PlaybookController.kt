package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.ContentLibraryService
import com.centuroglobal.service.PlaybookService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.AppConstant.REQUEST_ID_HEADER
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.playbook.PlaybookType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.playbook.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Playbook", description = "Centuro Global Playbook API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/playbook")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
class PlaybookController(
    private val playbookService: PlaybookService,
    private val contentLibraryService: ContentLibraryService
) {

    @PostMapping
    @Operation(
        summary = "Create Playbook",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction("PLAYBOOK")
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestBody request: PlaybookRequest,
        @LogValue @RequestAttribute(value = "x-request-id", required = false) @Parameter(hidden = true) requestId: String?
    ): Response<PlaybookResponse> {
        return Response(true, playbookService.create(request, requestId))
    }

    @GetMapping("/countries")
    @Operation(
        summary = "List Active playbook Countries ",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])]
    )
    fun listCountries(
        @RequestParam(name = "prefix", required = false)
        @Parameter(
            name = "prefix",
            required = false,
            description = "Country name prefix. Sending countries without prefix will return all countries."
        )
        prefix: String?
    ): Response<List<String>> {
        return Response(true,playbookService.listCountries(prefix))
    }

    @GetMapping("listing")
    @Operation(
        summary = "Playbook Listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun retrieve(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "type", required = false, defaultValue = "OWN")
        @Parameter(name = "type", required = false, description = "Playbook type to search.")
        type: PlaybookType = PlaybookType.OWN,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "Country to search.")
        country: String?,

        @RequestParam(name = "from", required = false)
        @Parameter(name = "from", required = false, description = "Created date (From) in epoch milliseconds.")
        from: Long?,

        @RequestParam(name = "to", required = false)
        @Parameter(name = "to", required = false, description = "Created date (Until) in epoch milliseconds.")
        to: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String


    ): Response<PagedResult<PlaybookListResponse>> {
        val data = playbookService.list(
            PlaybookSearchFilter.Builder.build(type, country, from, to, authenticatedUser.userId),
            PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
        )
        return Response(true, data)
    }

    @DeleteMapping("{playbookId}")
    @Operation(
        summary = "Delete Playbook",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun delete(@PathVariable playbookId: Long): Response<Boolean> {
        return Response(true, playbookService.delete(playbookId))
    }

    @PutMapping("{playbookId}/share")
    @Operation(
        summary = "Share a Playbook",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun share(
        @PathVariable playbookId: Long,
        @RequestBody users: List<Long>
    ): Response<Boolean> {
        return Response(true, playbookService.share(playbookId, users))
    }

    @GetMapping("share-users")
    @Operation(
        summary = "Share a Users reference data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun shareUsers(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<PlaybookShareUserReferenceData>> {
        return Response(true, playbookService.shareUsersList(authenticatedUser))
    }

    @GetMapping("{playbookId}")
    @Operation(
        summary = "Get a playbook data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "PLAYBOOK")
    fun get(
        @PathVariable playbookId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(required = false) session: Boolean = true,
        @LogValue @RequestAttribute(value = REQUEST_ID_HEADER, required = false) @Parameter(hidden = true) requestId: String?
    ): Response<PlaybookResponse> {
        return Response(true, playbookService.get(playbookId, authenticatedUser, session, requestId))
    }

    @GetMapping
    @Operation(
        summary = "Get Content item by Country and Identifier",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getByCountryAndIdentifier(
        @Valid @RequestParam country: String,
        @Valid @RequestParam identifier: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<ContentLibraryResponse> {
        val result = contentLibraryService.getByCountryAndIdentifier(country, identifier)
        return Response(true, result)
    }

//    @GetMapping("/countries")
//    @Operation(
//        summary = "Active Countries",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name = "jwt")]
//    )
//    fun activeCountries(
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//    ): Response<List<String>> {
//        val result = contentLibraryService.getActiveCountries()
//        return Response(true, result)
//    }

    @PutMapping("{sessionId}/close")
    @Operation(
        summary = "End a Playbook Session",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun closeSession(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable sessionId: String
    ): Response<Boolean> {
        return Response(true, playbookService.closeSession(sessionId, authenticatedUser))
    }

    @PostMapping("{sessionId}/ai-message")
    @Operation(
        summary = "Create an ai message for playbook",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "ASK_AI")
    fun aiMessage(
        @PathVariable sessionId: String,
        @RequestBody request: AIMessageRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        return Response(true, playbookService.aiMessage(sessionId, request, authenticatedUser))
    }

    @PostMapping("{sessionId}/assessment-ai-message")
    @Operation(
        summary = "Create an ai message for Travel assessment",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "ASK_AI")
    fun aiMessageForTravelAssessment(
        @PathVariable sessionId: String,
        @RequestBody request: AIMessageRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        return Response(true, playbookService.assessmentAiMessage(sessionId, request, authenticatedUser))
    }

    @GetMapping("logs-listing")
    @Operation(
        summary = "Playbook logs Listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun logs(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "Country to search.")
        country: String?,

        @RequestParam(name = "from", required = false)
        @Parameter(name = "from", required = false, description = "Created date (From) in epoch milliseconds.")
        from: Long?,

        @RequestParam(name = "to", required = false)
        @Parameter(name = "to", required = false, description = "Created date (Until) in epoch milliseconds.")
        to: Long?,

        @RequestParam(name = "corporate", required = false, defaultValue = "")
        @Parameter(name = "corporate", required = false, description = "Corporate to search.")
        corporate: Long?,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "Partner to search.")
        partnerId: Long?,

        @RequestParam(name = "user", required = false, defaultValue = "")
        @Parameter(name = "user", required = false, description = "Partner User to search.")
        user: Long?,

        @RequestParam(name = "isPartner", required = false, defaultValue = "false")
        @Parameter(
            name = "isPartner",
            required = false,
            description = "Partner logs."
        )
        isPartner: Boolean = false,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String


    ): Response<PagedResult<PlaybookLogsResponse>> {
        val size = if (isDownload) Int.MAX_VALUE else pageSize
        val data = playbookService.logs(
            PlaybookLogsSearchFilter.Builder.build(
                country,
                from,
                to,
                authenticatedUser.companyId ?: corporate,
                authenticatedUser.partnerId ?: partnerId,
                user,
                isPartner
            ),
            PageRequest.of(pageIndex, size, SearchConstant.SORT_ORDER(sortBy, sort))
        )
        return Response(true, data)
    }

    @GetMapping("{sessionId}/ai-message")
    @Operation(
        summary = "Playbook messages by session",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getAiMessages(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable sessionId: String
    ): Response<List<AIChatResponse>> {
        val result = playbookService.getChatMessages(sessionId, authenticatedUser)
        return Response(true, result)
    }
    
}