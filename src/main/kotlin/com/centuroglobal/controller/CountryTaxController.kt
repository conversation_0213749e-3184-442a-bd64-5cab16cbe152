package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.CountryTaxEntity
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.service.CountryTaxService
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "CountryTax", description = "Centuro Country Tax API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/countryTax")
class CountryTaxController(
    private val countryTaxService: CountryTaxService
) {

    @GetMapping("/")
    @Operation(
        summary = "Get Country Tax",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCountryTax(): Response<List<CountryTaxEntity>> {
        val countryTax = countryTaxService.getCountryTax()
        return Response(true, countryTax)
    }
}