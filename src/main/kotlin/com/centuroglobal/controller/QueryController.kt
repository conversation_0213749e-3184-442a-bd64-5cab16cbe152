package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.data.payload.query.QueryRequest
import com.centuroglobal.service.QueryService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.query.ProposalCardDetails
import com.centuroglobal.shared.data.pojo.query.QueryCardDetails
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody


@RestController
@Tag(name = "Queries", description = "Centuro Global Queries API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/queries")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
open class  QueryController(val queryService: QueryService) {

    @PostMapping
    @Operation(
        summary = "Create a query",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun postQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody queryRequest: QueryRequest
    ): Response<Long> {
        val queryId = queryService.createQuery(queryRequest, authenticatedUser)
        return Response(true, queryId)
    }

    @GetMapping
    @Operation(
        summary = "Fetch all the queries of the user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    open fun getQueries(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "")
        search: String?,

        @RequestParam(name = "account", required = false, defaultValue = "")
        @Parameter(name = "account", required = false, description = "based on account fetch all users and fetch queries of them")
        account: String?,

        @RequestParam(name = "user", required = false, defaultValue = "")
        @Parameter(name = "user", required = false, description = "")
        user: String?,

        @RequestParam(name = "responses", required = false, defaultValue = "")
        @Parameter(name = "responses", required = false, description = "")
        responses: String?,

        @RequestParam(name = "categories", required = false, defaultValue = "")
        @Parameter(name = "categories", required = false, description = "")
        categories: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "")
        country: String?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "Partner id for search")
        partnerId: Long?,

        @RequestParam(name = "archive", required = false, defaultValue = "")
        @Parameter(name = "archive", required = false, description = "")
        archive: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isPartner", required = false)
        @Parameter(name = "isPartner", required = false, description = "Partner cases")
        isPartner: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean


    ): Response<PagedResult<QueryCardDetails>> {
        val status = if(archive == null) null else if(archive) (listOf(QueryStatus.RESOLVED, QueryStatus.CANCELLED)) else listOf(QueryStatus.OPEN)
        return Response(true, queryService.listQueries(
            QuerySearchFilter.Builder.build(
                search,
                account,
                user,
                responses,
                categories,
                country,
                status,
                null,
                null,
                null,
                partnerId,
                cgRequested,
                isPartner
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser)
        )
    }

    @GetMapping("/{queryId}")
    @Operation(
        summary = "Retrieve a Query by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getQueryById(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long
    ): Response<QueryCardDetails> {
        return Response(true, queryService.getQueryById(queryId, authenticatedUser))
    }

    @PutMapping("/{queryId}")
    @Operation(
        summary = "Update Query status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateQueryStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @RequestParam("status") queryStatus: QueryStatus
    ): Response<Boolean> {
        return Response(true, queryService.updateStatus(queryId, queryStatus, authenticatedUser))
    }

    @PutMapping("/{queryId}/approve")
    @Operation(
        summary = "Approve Query",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun approveQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long
    ): Response<Boolean> {
        return Response(true, queryService.approveQueryProposals(queryId, authenticatedUser))
    }

    @GetMapping("/{queryId}/proposals")
    @Operation(
        summary = "GET list of proposals - based on query Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getProposalsByQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long
    ): Response<List<ProposalCardDetails>> {
        return Response(true, queryService.getProposalsByQuery(queryId, authenticatedUser))
    }

    @GetMapping("/{queryId}/proposals/{proposalId}/download")
    @Operation(
        summary = "Download proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun download(
        @PathVariable("queryId") queryId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): ResponseEntity<StreamingResponseBody> {
        return queryService.downloadProposal(queryId, proposalId, authenticatedUser)
    }

    @GetMapping("/{queryId}/proposals/{proposalId}/view")
    @Operation(
        summary = "View proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun viewProposal(
        @PathVariable("queryId") queryId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        return Response(true, queryService.viewProposalUrl(queryId, proposalId, authenticatedUser))
    }

    @PostMapping(value = ["/{queryId}/proposal"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Submit a proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun postProposal(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @RequestParam("fileName") fileName: String,
        @RequestParam("fileType") fileType: String,
        @RequestParam("fileSize") fileSize: Long,
        @RequestParam("file") file: MultipartFile,
        @PathVariable("partnerId") partnerId: Long?,
    ): Response<Long> {
        return Response(
            true,
            queryService.createProposal(queryId, fileName, fileType, fileSize, file, authenticatedUser)
        )
    }

    @DeleteMapping("/{queryId}/proposals/{proposalId}")
    @Operation(
        summary = "Delete a Query proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun deleteProposal(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @PathVariable("partnerId") partnerId: Long?,
    ): Response<Boolean> {
        return Response(true, queryService.deleteProposal(queryId, proposalId, authenticatedUser))
    }

}