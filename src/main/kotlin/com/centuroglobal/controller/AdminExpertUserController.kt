package com.centuroglobal.controller

import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.CreateSecondaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateExpertPersonalInfoRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Expert User Management", description = "Centuro Global Admin Expert User API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/expert/user")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #userId, 'EXPERT_USER', 'USERS')")
class AdminExpertUserController(
     private val expertUserService: ExpertUserService
) {

    @GetMapping("/{userId}")
    @Operation(
        summary = "Retrieve Expert",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "RETRIEVE_EXPERT")
    fun retrieve(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @PathVariable userId: Long
    ): Response<ExpertProfile> {
        return Response(true,expertUserService.retrieveProfile(userId))
    }

    @PutMapping("/{userId}")
    @Operation(
        summary = "Update Expert",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "UPDATE_EXPERT")
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @PathVariable userId: Long,
        @Valid @RequestBody request: UpdateExpertPersonalInfoRequest
    ): Response<ExpertProfile> {
        return Response(true,expertUserService.updateProfileByAdmin(userId, authenticatedUser.userId, request))
    }


    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Create secondary Expert",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateSecondaryExpertUserRequest
    ): Response<Unit> {
        authenticatedUser.partnerId?.let { request.partnerId = it }
        return Response(true, expertUserService.createSecondaryExpertUser(authenticatedUser.userId, request))
    }

//    //this can be deprecated
//    @PutMapping(value = ["/{userId}/photo"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
//    @Operation(
//        summary = "Update Profile Picture. Return full path url of the image if successful.",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun uploadProfilePicture(
//        @PathVariable userId: Long,
//        @RequestParam(value = "photo") photo: MultipartFile,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): DeferredResult<Response<String>> {
//        return ResponseUtil.toDeferredResult(expertFacade.uploadProfilePicture(userId, photo, user.userId))
//    }
//
//    //this can be deprecated
//    @DeleteMapping("/{userId}/photo")
//    @Operation(
//        summary = "Delete Profile Picture",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun deleteProfilePicture(
//        @PathVariable userId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): DeferredResult<Response<String>> {
//        return ResponseUtil.toDeferredResult(expertFacade.deleteProfilePicture(userId, user.userId))
//    }

    @GetMapping("{userId}/accounts")
    @Operation(
        summary = "accounts listing for expert company profile",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun userAccounts(@PathVariable("userId") userId: Long): Response<Map<String, Any>> {
        return Response(true,expertUserService.listAccounts(userId))
    }

    @GetMapping("/listing")
    @Operation(
        summary = "expert user listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listing(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Search expert user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "expert user's country to search.")
        countryCode: String?,

        @RequestParam(name = "companyId", required = false)
        @Parameter(name = "companyId", required = false, description = "expert user's company-id to search.")
        companyId: Long?,

        @RequestParam(name = "expertType", required = false)
        @Parameter(name = "expertType", required = false, description = "expert user's expert-type to search.")
        expertType: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status", required = false, description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: String?,

        @RequestParam(name = "joinedFrom", required = false, defaultValue = "")
        @Parameter(name = "joinedFrom", required = false, description = "Starting date from which corporate user has joined.")
        joinedFrom: Long?,

        @RequestParam(name = "joinedTo", required = false, defaultValue = "")
        @Parameter(name = "joinedTo", required = false, description = "Date up to which corporate user had joined.")
        joinedTo: Long?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which corporate user has created.")
        createdFrom: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdTo", required = false, description = "Date up to which corporate user had created.")
        createdTo: Long?,

        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(name = "isPartnerCompany", required = false, description = "is Partner Company to search.")
        isPartnerCompany: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "Partner Id")
        partnerId: Long?,

        @RequestParam(name = "expertiseIds", required = false, defaultValue = "")
        @Parameter(
            name = "expertiseIds",
            required = false,
            description = "Expertise ids comma separated."
        )
        expertiseIds: String

    ): Response<ListingWithStatsDetails<ExpertUserDetails>?> {

        return Response(true, expertUserService.listExpertUser(
            ExpertUserDetailsFilter.Builder.build(
                search,
                countryCode,
                companyId,
                expertType,
                status,
                createdFrom,
                createdTo,
                isPartnerCompany,
                authenticatedUser.partnerId ?: partnerId,
                joinedFrom,
                joinedTo,
                expertiseIds

            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    @DeleteMapping("/{userId}")
    @Operation(
        summary = "Remove expert user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable("userId") expertUserId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        return Response(true, expertUserService.deleteExpertUser(expertUserId))
    }
}