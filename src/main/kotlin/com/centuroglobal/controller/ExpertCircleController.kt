package com.centuroglobal.controller

import com.centuroglobal.annotation.IsExpertUser
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.CircleListing
import com.centuroglobal.shared.data.pojo.circle.CircleMemberActionRequest
import com.centuroglobal.shared.data.pojo.circle.CircleSearchFilter
import com.centuroglobal.facade.ExpertCircleFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern

private val DEFAULT_SORT_ORDER_CLIENT = Sort.by(
    Sort.Order(Sort.Direction.DESC, "createdDate"),
    Sort.Order(Sort.Direction.ASC, "displayName", Sort.NullHandling.NULLS_LAST)
)

private val DEFAULT_SORT_ORDER = Sort.by(
    Sort.Order(Sort.Direction.DESC, "createdDate"),
    Sort.Order(Sort.Direction.ASC, "name", Sort.NullHandling.NULLS_LAST)
)

@RestController
@Tag(name = "Expert Circle API", description = "Centuro Global Expert Circle API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert/circle")
@IsExpertUser
class ExpertCircleController(
    private val expertCircleFacade: ExpertCircleFacade
) {

    @GetMapping("/listing")
    @Operation(
        summary = "List Circles",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listing(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status",
            required = false,
            description = "Circle status to search.",
            schema = Schema(allowableValues = ["DRAFT","PUBLISH"])
        )
        @Pattern(regexp = "^(|DRAFT|PUBLISH)$")
        status: String,

        @RequestParam(name = "circleType", required = false, defaultValue = "")
        @Parameter(
            name = "circleType",
            required = false,
            description = "Circle type to search.",
            schema = Schema(allowableValues = ["PUBLIC","PRIVATE"])
        )
        @Pattern(regexp = "^(|PUBLIC|PRIVATE)$")
        circleType: String,

        @RequestParam(name = "from", required = false)
        @Parameter(
            name = "from",
            required = false,
            description = "Created date (From) in epoch milliseconds."
        )
        from: Long?,

        @RequestParam(name = "to", required = false)
        @Parameter(
            name = "to",
            required = false,
            description = "Created date (Until) in epoch milliseconds."
        )
        to: Long?,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Circle's name or about to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCode", required = false, description = "Country of user")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(name = "expertiseId", required = false, description = "Expertise id")
        expertiseId: String
    ): DeferredResult<Response<CircleListing>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.listCircle(
                CircleSearchFilter.Builder.build(status, circleType, from, to, search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER), authenticatedUser
            )
        )
    }


    @GetMapping("/circle-details/{circleId}")
    @Operation(
        summary = "Create Details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable circleId: Long
    ): DeferredResult<Response<Circle>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.circleDetails(circleId, authenticatedUser)
        )
    }

    /*@PostMapping("/request-to-join/{circleId}")
    @Operation(
        summary = "Create Details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun requestToJoin(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable circleId: Long
    ): DeferredResult<Response<Any>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.requestToJoin(circleId,authenticatedUser)
        )
    }*/

    @PostMapping("/circle-member-action")
    @Operation(
        summary = "Circle Member Action",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleMemberAction(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody circleMemberActionRequest: CircleMemberActionRequest
    ): DeferredResult<Response<Any>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.circleMemberAction(
                circleMemberActionRequest.id,
                circleMemberActionRequest.action,
                authenticatedUser
            )
        )
    }

    @GetMapping("/circle-member-search/{circleId}")
    @Operation(
        summary = "Circle Member List",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleMemberSearch(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @PathVariable
        circleId: Long,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "User's name or company name to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCode", required = false, description = "Country of users")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(
            name = "expertiseId",
            required = false,
            description = "Expertise id in case of userType is expert selected."
        )
        expertiseId: String
    ): DeferredResult<Response<PagedResult<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            expertCircleFacade.circleMemberSearch(
                circleId,
                ExpertSearchFilter.Builder.build(search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER_CLIENT),
                authenticatedUser
            )
        )
    }

}