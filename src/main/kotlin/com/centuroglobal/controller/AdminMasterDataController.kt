package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.MasterContentEntity
import com.centuroglobal.data.payload.Content
import com.centuroglobal.data.payload.MasterDataDetails
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.facade.AdminMasterDataFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.AdminMasterDataService
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize

@RestController
@Tag(name = "Admin Master Data", description = "Centuro Global Admin Master Data API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/master-data")
class AdminMasterDataController(
    private val adminMasterDataService: AdminMasterDataService,
    private val adminMasterDataFacade: AdminMasterDataFacade
) {
    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'MASTERS')")
    @GetMapping("/download")
    @Operation(
        summary = "Get Template",
        responses = [ApiResponse(content = [io.swagger.v3.oas.annotations.media.Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun downloadTemplate(
        @RequestParam(name = "templateType", required = true) templateType: String
    ): ResponseEntity<StreamingResponseBody> {
        return adminMasterDataService.downloadTemplate(templateType)
    }

    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'MASTERS')")
    @PostMapping(value=["/upload"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Upload the master data based on template type",
        responses = [ApiResponse(content = [io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadTemplate(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(name = "templateType", required = true) templateType: String,
        @RequestParam("file") uploadedExcel: MultipartFile
    ): Response<HttpStatus> {
        val template = adminMasterDataFacade.uploadTemplate(
            templateType,
            uploadedExcel,
            authenticatedUser.userId,
            authenticatedUser.displayName
        )
        return Response(true, template)
    }

    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'MASTERS')")
    @GetMapping
    @Operation(
        summary = "Get list of Master Data",
        responses = [ApiResponse(content = [io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getMasterData(): Response<List<MasterDataDetails>> {
        val masterDataList = adminMasterDataService.getMasterData()
        val response = masterDataList.map { MasterDataDetails.ModelMapper.from(it) }.toMutableList()
        return Response(true, response)
    }

    //TODO move this code to regular controller and update path on UI accordingly
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
    @GetMapping("/content")
    @Operation(
        summary = "Get list of Master Content",
        responses = [ApiResponse(content = [io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getMasterContent(
        @RequestParam(name = "templateType", required = true) templateType: String
    ): Response<MasterContentEntity> {
        val masterDataList = adminMasterDataService.getMasterContent(templateType)
        return Response(true, masterDataList)
    }

    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'MASTERS')")
    @PostMapping("/content")
    @Operation(
        summary = "Upload of Master Content",
        responses = [ApiResponse(content = [io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadMasterContent(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(name = "templateType", required = true) templateType: String,
        @Valid @RequestBody content: Content
    ): Response<MasterContentEntity> {
        val masterContent = adminMasterDataService.uploadMasterContent(
            templateType,
            authenticatedUser.userId,
            content.content,
            authenticatedUser.displayName
        )
        return Response(true, masterContent)
    }
}