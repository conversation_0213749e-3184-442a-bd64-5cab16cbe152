package com.centuroglobal.controller

import com.centuroglobal.annotation.IsExpertUser
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter

@RestController
@Tag(name = "Expert Profile", description = "Centuro Global Expert Profile API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert/profile")
@IsExpertUser
class ExpertContactController(
    private val expertFacade: ExpertFacade
) {

    @PutMapping("/notify-admin/{contactedExpertId}")
    @Operation(
        summary = "Notify Admin",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun notifyAdmin(
        @PathVariable contactedExpertId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<Boolean>> {
        return ResponseUtil.toDeferredResult(expertFacade.notifyAdmin(contactedExpertId, user.userId))
    }

}