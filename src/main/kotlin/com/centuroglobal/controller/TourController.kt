package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.data.payload.TourRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.TourService
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.Parameter

@RestController
@Tag(name = "Tour", description = "Centuro Global Tour API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/tour")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
@Validated
class TourController(
    private val tourService: TourService
) {

    @GetMapping("/")
    @Operation(
        summary = "Get Tour",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun tour(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<TourRequest> {
        return Response(true, TourRequest(tourService.getTour(user)))
    }


    @PostMapping("/")
    @Operation(
        summary = "Update Tour",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateTour(
        @RequestBody tour: TourRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<Boolean> {
        return Response(true, tourService.updateTour(user, tour.enable))
    }
}