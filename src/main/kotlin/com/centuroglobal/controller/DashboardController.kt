package com.centuroglobal.controller

import com.centuroglobal.data.payload.dashboard.CountryGdp
import com.centuroglobal.service.DashboardService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.CountryHighlightsEntity
import com.centuroglobal.shared.data.entity.CountryIndicesCategoryEntity
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.dashboard.KeyValuePair
import com.centuroglobal.shared.data.pojo.DashboardCompanyInfo
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "Dashboard", description = "Centuro Country Dashboard API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/dashboard")
class DashboardController(
    private val dashboardService: DashboardService
) {

    @GetMapping("/aggregate-data")
    @Operation(
        summary = "Get Aggregate Data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun aggregateData(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<KeyValuePair>> {
        val output = dashboardService.generateAggregateData(authenticatedUser)
        return Response(true, output)
    }

    @GetMapping("/country-data")
    @Operation(
        summary = "Get Country Gdp",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCountryData(
        @RequestParam(name = "filter", required = false, defaultValue = "")
        @Parameter(name = "filter", required = false, description = "Category to get top countries")
        filter: String?
    ): Response<List<CountryGdp>> {
        val output = dashboardService.getTopCountryDataByFilter(filter)
        return Response(true, output)
    }

    @GetMapping("/rating-categories")
    @Operation(
        summary = "Get Country Indices Category list",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCountryIndicesCategory(
    ): Response<List<CountryIndicesCategoryEntity>> {
        val ratingCategoryList = dashboardService.getIndicesCategory()
        return Response(true, ratingCategoryList)
    }

    @GetMapping("/did-you-know")
    @Operation(
        summary = "Get Did you know country facts",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getDidYouKnowData(
    ): Response<List<CountryHighlightsEntity>> {
        val countryHighlightsList = dashboardService.getCountryHighlights()
        return Response(true, countryHighlightsList)
    }

    @GetMapping("/company-info")
    @Operation(
        summary = "Get company info",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCompanyInfo(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<DashboardCompanyInfo> {
        val teamDocs = dashboardService.getTeamAndDocs(authenticatedUser)
        return Response(true, teamDocs)
    }
}