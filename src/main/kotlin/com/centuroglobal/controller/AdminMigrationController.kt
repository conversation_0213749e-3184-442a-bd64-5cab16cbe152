package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdmin
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.MigrationService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.repository.ExpertCompanyProfileRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@Tag(name = "Admin Blueprint", description = "Centuro Global Admin Blueprint API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/migration")
@IsSuperAdminOrAdmin
class AdminMigrationController(
    private val adminUserService: AdminUserService,
    private val expertUserRepository: ExpertUserRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val migrationService: MigrationService
) {
    @PutMapping
    @Operation(
        summary = "Update Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    ): Response<Boolean> {
        val expertUserList = expertUserRepository.findAll()
        for(expert in expertUserList){
           val company= expertCompanyProfileRepository.findById(expert.companyProfile!!.id!!).get()
            company.contractAcceptedDate = expert.lastTermsViewDate
            expertCompanyProfileRepository.save(company)
        }
        return Response(true, true)
    }

    @PostMapping(value = ["/migration/{type}"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun migrateDb(@PathVariable("type") type: String, @RequestParam(name= "file", required = false) uploadedExcel: MultipartFile): Response<Boolean> {
        return when (type) {
            "bands" -> Response(true, migrationService.bandMigration())
            "corporate_accounts" -> Response(true, migrationService.corporateUserAccountMigration())
            "expert_accounts" -> Response(true, migrationService.expertCompanyAccountMigration())
            "cases" -> Response(true, migrationService.caseAccountMigration())
            "cases_actionFor" -> Response(true, migrationService.caseActionForMigration())
            "case_history_actionFor" -> Response(true, migrationService.caseHistoryActionForMigration(uploadedExcel))
            else -> Response(false)
        }
    }

    @PostMapping("/subscription")
    @Operation(
        summary = "Update Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun migrateSubscription(
        @RequestParam planId: Long,
        @RequestParam startDate: Long,
        @RequestParam endDate: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Int> {
        return Response(true, migrationService.subscriptionPlansMigration(planId, startDate, endDate))
    }


    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/migrate-client-doc-file"])
    @Operation(
        summary = "migrate client doc file data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "MIGRATE_CLIENT_DOCS_TO_FILE")
    fun migrateClientDocsFiles(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<Boolean> {
        return Response(true, adminUserService.migrateClientDocsFiles(user))
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/migrate-case-group-chat"])
    @Operation(
        summary = "migrate case group chats",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "MIGRATE_CASE_GROUP_CHATS")
    fun migrateCaseGroupChats(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<Boolean> {
        migrationService.migrateCaseGroupChat()
        return Response(true)
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/migrate-task-details"])
    @Operation(
        summary = "migrate task created by and company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun migrateTaskDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<Boolean> {
        migrationService.migrateTaskCompanyAndCreatedDetails()
        return Response(true)
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/migrate-case-emails-by-category"])
    @Operation(
        summary = "migrate case emails by category",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun migrateCaseEmailsByCategory(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<Boolean> {
        migrationService.migarateCaseEmailsByCategory()
        return Response(true)
    }

}