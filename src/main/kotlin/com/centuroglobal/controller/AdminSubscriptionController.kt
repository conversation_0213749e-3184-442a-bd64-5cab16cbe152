package com.centuroglobal.controller


import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.SubscriptionChangeRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.SubscriptionService
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansRequest
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansResponse
import com.centuroglobal.shared.service.subscription.SharedSubscriptionService
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Admin Subscription Management", description = "Centuro Global Admin Subscription API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/subscription")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CORPORATE', 'SUBSCRIPTION')")
class AdminSubscriptionController(
    val subscriptionService: SubscriptionService,
    val sharedSubscriptionService: SharedSubscriptionService
    ) {

    @PutMapping
    @Operation(
        summary = "Update a subscription",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @Valid @RequestBody request: SubscriptionChangeRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        subscriptionService.update(request, authenticatedUser.userId)
        return Response(true)
    }

    @GetMapping
    @Operation(
        summary = "Subscription plans listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun plans(
        @RequestParam(name = "currency", required = false, defaultValue = "")
        @Parameter(name = "currency", required = false, description = "Currency to search.")
        currency: String?
    ): Response<List<SubscriptionPlansResponse>> {
        return Response(true, subscriptionService.listPlans(currency))
    }

    @PostMapping
    @Operation(
        summary = "Create a subscription plan",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @Valid @RequestBody request: SubscriptionPlansRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Long> {
        val result = subscriptionService.createCustomSubscription(request, -1)
        return Response(true, result.id)
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update a subscription plan",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updatePlan(
        @PathVariable id: Long,
        @Valid @RequestBody request: SubscriptionPlansRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Boolean> {
        val result = subscriptionService.updateSubscriptionPlan(request, id, authenticatedUser)
        return Response(true, result)
    }



/*
TODO tmp api to generate billing for prev month
    @GetMapping("test")
    @Operation(
        summary = "Test",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun test(
    ): Response<Unit> {
        return Response(true, sharedSubscriptionService.generate())
    }*/

}