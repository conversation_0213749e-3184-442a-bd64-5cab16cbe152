package com.centuroglobal.controller


import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.service.CaseFormService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseFormDetails
import com.centuroglobal.shared.data.pojo.case.CaseFormSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Case Forms", description = "Centuro Global Case Forms API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/case-form")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class CaseFormController(open val caseFormService: CaseFormService) {

    @GetMapping("{id}")
    @Operation(
        summary = "Get a case form by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun get(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long
    ): Response<CaseFormDetails> {
        return Response(true, caseFormService.get(id, authenticatedUser))
    }

    @GetMapping
    @Operation(
        summary = "LIst all the case forms",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "")
        search: String?,

        @RequestParam(name = "category", required = false, defaultValue = "")
        @Parameter(name = "category", required = false, description = "")
        category: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "")
        country: String?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(name = "status", required = false, description = "")
        status: CaseFormStatus?,

        @RequestParam(name = "visibility", required = false, defaultValue = "")
        @Parameter(name = "visibility", required = false, description = "")
        visibility: TaskVisibility?,

        @RequestParam(name = "updatedBy", required = false, defaultValue = "")
        @Parameter(name = "updatedBy", required = false, description = "")
        updatedBy: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "Partner id for search")
        partnerId: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,


        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "isPartner", required = false)
        @Parameter(name = "isPartner", required = false, description = "Partner case forms")
        isPartner: Boolean?

    ): Response<PagedResult<CaseFormDetails>> {

        return Response(true, caseFormService.list(
            CaseFormSearchFilter.Builder.build(
                search,
                category,
                country,
                status,
                visibility,
                updatedBy,
                authenticatedUser.partnerId ?: partnerId,
                isPartner
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser)
        )
    }
}