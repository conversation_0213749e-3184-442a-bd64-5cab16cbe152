package com.centuroglobal.controller

import com.centuroglobal.annotation.IsPartner
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody


@RestController
@Tag(name = "Partner Management", description = "Centuro Global Partner API v1")
@IsPartner
@RequestMapping("/api/${AppConstant.API_VERSION}/partner")
class PartnerController(
    private val partnerService:PartnerService,
    private val adminUserService: AdminUserService
){

    @GetMapping("/{partnerId}")
    @Operation(
        summary = "Return partner details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getAllPartnerDetails(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<AllPartnerList> {
        val result = partnerService.retrieveAllPartnerDetails(partnerId)
        return Response(true, result)
    }


    @PutMapping("/{partnerId}/company-status")
    @Operation(
        summary = "Update company status Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @PathVariable("partnerId") partnerId: Long,
        @RequestBody @Valid updateCompanyStatusRequest:UpdateCompanyStatusRequest
    ):Response<Boolean>{
        return Response(true, partnerService.updatePartnerCompanyStatus(partnerId, updateCompanyStatusRequest,authenticatedUser))
    }

    @GetMapping("/company-info")
    @Operation(
        summary = "Get company info",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCompanyInfo(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<PartnerDashboardCompanyInfo> {
        val info = partnerService.getDashboardInfo(authenticatedUser)
        return Response(true, info)
    }

    @GetMapping("/view-doc/{fileId}")
    @Operation(
            summary = "View Corporate document",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name =  "jwt")]
    )
    fun viewUrl(
            @PathVariable("fileId") fileId: Long,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        return Response(true, adminUserService.getDocUrlCorporate(fileId, authenticatedUser.partnerId!!, authenticatedUser))
    }

    @GetMapping("/download-doc/{fileId}")
    @Operation(
            summary = "Download Corporate document",
            responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
            security = [SecurityRequirement(name =  "jwt")]
    )
    fun downloadDoc(
            @PathVariable("fileId") fileId: Long?,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): ResponseEntity<StreamingResponseBody> {
        return adminUserService.downloadDocCorporate(fileId, authenticatedUser.partnerId!!, authenticatedUser)
    }

    @GetMapping("/access")
    @Operation(
        summary = "Return partner band accesses",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getPartnerBandAccesses(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<List<String?>> {
        val result = partnerService.retrievePartnerBandAccesses(authenticatedUser.partnerId!!)
        return Response(true, result)
    }

}