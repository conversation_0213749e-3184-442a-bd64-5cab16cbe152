package com.centuroglobal.controller

import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.UpdateCorporateInfoRequest
import com.centuroglobal.shared.data.pojo.CorporateUserResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Corporate User Management", description = "Centuro Global Corporate User API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/corporate/user-profile")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #userId, 'CORPORATE_USER', 'USERS')")
class AdminCorporateUserProfileController (
    private val corporateUserService: CorporateUserService
    ){

    @GetMapping("/{corporateId}/profile/{userId}")
    @Operation(
        summary = "Corporate user - Get user by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getUser(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long,
        @PathVariable corporateId: Long,
    ): Response<CorporateUserResponse> {
        return Response(true,corporateUserService.fetchUser(userId, corporateId))
    }

    @PutMapping("/{userId}")
    @Operation(
        summary = "Update Corporate Profile",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody profileRequest: UpdateCorporateInfoRequest,
        @PathVariable userId: Long
    ): Response<CorporateUserResponse> {
        return Response(true,
            corporateUserService.updateCorporateUserForAdmins(
                userId,
                profileRequest
            )
        )
    }
}