package com.centuroglobal.controller

import com.centuroglobal.data.payload.blueprint.UpdateBlueprintRequest
import com.centuroglobal.data.payload.blueprint.UpdateBlueprintStatusRequest
import com.centuroglobal.facade.AdminBlueprintFacade
import com.centuroglobal.service.BlueprintService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Blueprint
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.StepExpertRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import org.springframework.web.multipart.MultipartFile

@RestController
@Tag(name = "Admin Blueprint", description = "Centuro Global Admin Blueprint API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/blueprint/{countryCode}")
@PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'BLUEPRINT')")
class AdminBlueprintController(
    private val adminBlueprintFacade: AdminBlueprintFacade,
    private val blueprintService: BlueprintService
) {

    @GetMapping
    @Operation(
        summary = "Retrieves Blueprint by country or default template",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(
        @PathVariable countryCode: String
    ): DeferredResult<Response<Blueprint>> {
        return ResponseUtil.toDeferredResult(adminBlueprintFacade.retrieveBluePrintForAdmin(countryCode))
    }

    @PutMapping
    @Operation(
        summary = "Update Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable countryCode: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: UpdateBlueprintRequest
    ): Response<Boolean> {
        val response = blueprintService.updateBlueprint(
            countryCode,
            authenticatedUser.userId,
            request
        )
        return Response(true, response)
    }


    @PutMapping("/status")
    @Operation(
        summary = "Update Blueprint Status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateStatus(
        @PathVariable countryCode: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: UpdateBlueprintStatusRequest
    ): Response<Boolean> {
        val response = blueprintService.updateBlueprintStatus(
            countryCode,
            authenticatedUser.userId,
            request.status
        )
        return Response(true, response)
    }

    @PutMapping("/expert/{step}")
    @Operation(
        summary = "Update Blueprint",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateExpert(
        @PathVariable countryCode: String,
        @PathVariable step: StepName,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: StepExpertRequest
    ): DeferredResult<Response<List<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            adminBlueprintFacade.updateExpert(
                countryCode,
                step,
                authenticatedUser.userId,
                request
            )
        )
    }

    @PutMapping(value = ["/pdf/{step}"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Upload Pdf. Return full path url of the blueprint if successful.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadPdf(
        @PathVariable countryCode: String,
        @PathVariable step: StepName,
        @RequestParam(value = "pdf") pdf: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(adminBlueprintFacade.uploadPdf(countryCode, step, pdf, user.userId))
    }

    @DeleteMapping("/pdf/{step}")
    @Operation(
        summary = "Delete Pdf",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deletePdf(
        @PathVariable countryCode: String,
        @PathVariable step: StepName,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(adminBlueprintFacade.deletePdf(countryCode, step, user.userId))
    }
}