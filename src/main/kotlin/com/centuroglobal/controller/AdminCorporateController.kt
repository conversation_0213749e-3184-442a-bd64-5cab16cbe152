package com.centuroglobal.controller

import com.centuroglobal.facade.CorporateFacade
import com.centuroglobal.service.BandService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.SubscriptionService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.SubscriptionUpdateRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateRequest
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Corporate Management", description = "Centuro Global Admin Corporate API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/corporate")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CORPORATE', 'USERS')")
class AdminCorporateController(
    private val corporateFacade: CorporateFacade,
    private val bandService: BandService,
    private val partnerService: PartnerService,
    private val corporateService: CorporateService,
    private val subscriptionService: SubscriptionService
) {
    @GetMapping("/{corporateId}")
    @Operation(
        summary = "Retrieve Corporate data and Root User Details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun get(@PathVariable corporateId: Long): Response<CorporateResponse>  {
        return Response(true, corporateService.retrieveByCorporateId(corporateId))
    }

    @GetMapping("{corporateId}/users")
    @Operation(
        summary = "Corporate User Listing - based on CorporateId",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun corporateUserListing(@PathVariable("corporateId") corporateId: Long): Response<List<CorporateUsers>> {
        return Response(true,corporateService.retrieveCorporateUsers(corporateId))
    }

    @PutMapping("/{corporateId}")
    @Operation(
        summary = "Update Corporate based on corporateId",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable corporateId: Long,
        @Valid @RequestBody updateRequest: UpdateCorporateRequest
    ): Response<Boolean> {
        return Response(true,corporateService.update(corporateId, updateRequest))
    }

    @PostMapping
    @Operation(
        summary = "Invite Corporate",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun post(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody @Valid signUpRequest: SignUpRequest
    ): Response<Long> {
        authenticatedUser.partnerId?.let { signUpRequest.partnerId = it }
        return Response(true, corporateService.createCorporate(signUpRequest))
    }

    @GetMapping("{corporateId}/secondary")
    @Operation(
        summary = "Retrieve Secondary Corporate users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun secondaryCorporates(@PathVariable corporateId: Long): Response<List<CorporateUserDetail>> {
        return Response(true,corporateService.retrieveSecondaryCorporateUsers(corporateId))
    }

    @GetMapping("/companies")
    @Operation(
        summary = "Corporate Listing - Only Name and Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun refList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(
            name = "isPartnerCompany",
            required = false,
            description = "is Partner Company or not."
        )
        isPartnerCompany: Boolean?

    ): Response<List<ReferenceData>> {
        return Response(true,corporateService.retrieveCorporates(isPartnerCompany, authenticatedUser.partnerId))
    }

    //URI Change corporate-accounts to accounts
    @GetMapping("{corporateId}/accounts")
    @Operation(
        summary = "Corporate Accounts listing- Only Name and Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun corporateAccounts(@PathVariable("corporateId") corporateId: Long): Response<List<ReferenceData>> {
        return Response(true,corporateFacade.listCorporateAccounts(corporateId))
    }

    @GetMapping("{corporateId}/bands")
    @Operation(
        summary = "Corporate Bands listing - Only Name and Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun corporateBands(@PathVariable("corporateId") corporateId: Long): Response<List<ReferenceData>> {
        return Response(true,bandService.listBands(corporateId))
    }

    //URI Changes / corporates to /listing
    @GetMapping("/listing")
    @Operation(
        summary = "CG corporate company listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "name", required = false)
        @Parameter(name = "name", required = false, description = "Corporate's name")
        name: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Corporate's country to search.")
        country: String?,

        @RequestParam(name = "account id", required = false)
        @Parameter(name = "account id", required = false, description = "account id to search.")
        accountId: Long?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: CorporateStatus?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which corporate is searched.")
        from: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdTo", required = false, description = "Date up to which corporate is searched.")
        to: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(
            name = "isPartnerCompany",
            required = false,
            description = "is Partner Company or not."
        )
        isPartnerCompany: Boolean?,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "Partner Id")
        partnerId: Long?

    ): Response<ListingWithStatsDetails<CorporateList>?> {

        return Response(true, partnerService.listCorporateStats(authenticatedUser.partnerId ?: partnerId,
            PartnerCorporateSearchFilter.Builder.build(
                name,
                country,
                accountId,
                status,
                from,
                to,
                isPartnerCompany
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    @PutMapping("/{corporateId}/status")
    @Operation(
        summary = "Update company status Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @RequestBody @Valid updateCompanyStatusRequest:UpdateCompanyStatusRequest
    ):Response<Boolean>{
        return Response(true, partnerService.updateCompanyStatus(updateCompanyStatusRequest,authenticatedUser))
    }

    @DeleteMapping("/{corporateId}")
    @Operation(
        summary = "Remove corporate and all its users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteCorporate(
        @PathVariable("corporateId") corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        return Response(true, corporateService.delete(corporateId))
    }

    // URI disassociate-corporate to This
    @DeleteMapping("/{corporateId}/disassociate")
    @Operation(
        summary = "Disassociate and suspend corporates associated with partner",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun disassociateCorporate(
        @PathVariable("corporateId") corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        val result = corporateService.disassociateCorporate(corporateId, authenticatedUser)
        return Response(true,result)
    }

    //URI Change - Done
    @PutMapping("/{corporateId}/subscription")
    @Operation(
        summary = "Update a subscription",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @Valid @RequestBody request: SubscriptionUpdateRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        subscriptionService.updateCorporateSubscriptionType(request)
        return Response(true)
    }

    @GetMapping("/{corporateId}/account-users")
    @Operation(
        summary = "Corporate accounts and users associated with it",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listAccounts(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable corporateId: Long
    ): Response<MutableList<Account>> {
        return Response(true, corporateService.retrieveCorporateAccountUsers(authenticatedUser, corporateId))
    }

}