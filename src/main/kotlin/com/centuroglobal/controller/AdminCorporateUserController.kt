package com.centuroglobal.controller

import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.UserDocType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.CorporateDocumentRequest
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateProfileRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "Admin Corporate User Management", description = "Centuro Global Admin Corporate Users API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/corporate/user")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #userId, 'CORPORATE_USER', 'USERS')")
class AdminCorporateUserController(
    private val corporateUserService: CorporateUserService,
    private val corporateService: CorporateService,
    private val adminUserService: AdminUserService
) {

    @PostMapping
    @Operation(
        summary = "Corporate user - Add new user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "USER")
    fun post(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestBody @Valid request: CorporateUserRequest
    ): Response<String> {
        corporateUserService.createCorporateUser(request, authenticatedUser, request.corporateId!!)
        return Response(true, "SUCCESS")
    }

    @GetMapping
    @Operation(
        summary = "List Corporate Users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CorporateUsers>> {
        return Response(true,corporateService.retrieveAllCorporateUsers(authenticatedUser.partnerId))
    }

    @GetMapping("/{userId}")
    @Operation(
        summary = "Retrieve Corporate root User and corporate data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(@PathVariable userId: Long): Response<Corporate> {
        return Response(true,corporateService.retrieveCorporate(userId))
    }

    @GetMapping("{userId}/accounts")
    @Operation(
        summary = "Corporate User accounts listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun corporateUserAccountListing(@PathVariable("userId") userId: Long): Response<List<ReferenceData>> {
        return Response(true, corporateService.retrieveCorporateUserAccounts(userId))
    }

    @GetMapping("/listing")
    @Operation(
        summary = "corporate user listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listCorporateUsers(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Search Corporate user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Corporate user's country to search.")
        countryCode: String?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "Corporate user's corporate-id to search.")
        corporateId: Long?,

        @RequestParam(name = "subscription", required = false)
        @Parameter(name = "subscription", required = false, description = "Corporate user's subscription to search.")
        subscription: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: String?,

        @RequestParam(name = "bandId", required = false)
        @Parameter(name = "bandId", required = false, description = "Corporate user's bandName to search.")
        bandName: String?,

        @RequestParam(name = "joinedFrom", required = false, defaultValue = "")
        @Parameter(name = "joinedFrom", required = false, description = "Starting date from which corporate user has joined.")
        joinedFrom: Long?,

        @RequestParam(name = "joinedTo", required = false, defaultValue = "")
        @Parameter(name = "joinedTo", required = false, description = "Date up to which corporate user had joined.")
        joinedTo: Long?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which corporate is searched.")
        createdFrom: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdTo", required = false, description = "Date up to which corporate is searched.")
        createdTo: Long?,


        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "isPartnerCompany", required = false)
        @Parameter(name = "isPartnerCompany", required = false, description = "is Partner Company to search.")
        isPartnerCompany: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean

    ): Response<ListingWithStatsDetails<CorporateUserDetails>?> {
        return Response(true, corporateUserService.listCorporateUser(
            CorporateUserDetailsFilter.Builder.build(
                search,
                countryCode,
                corporateId,
                subscription,
                status,
                bandName,
                createdFrom,
                createdTo,
                authenticatedUser.partnerId ?: partnerId,
                isPartnerCompany,
                joinedFrom,
                joinedTo
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    @DeleteMapping("/{userId}")
    @Operation(
        summary = "Remove corporate user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteCorporateUser(
        @PathVariable("userId") corporateUserId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        return Response(true, corporateService.deleteCorporateUser(corporateUserId))
    }

    @PutMapping("/{userId}")
    @Operation(
        summary = "Update Corporate user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long,
        @Valid @RequestBody profileRequest: UpdateCorporateProfileRequest
    ): Response<Corporate> {
        return Response(true,
            corporateService.updateCorporate(userId, authenticatedUser.userId,
                authenticatedUser.userType, profileRequest
            )
        )
    }

    @PostMapping("/{userId}/verification-email")
    @Operation(
        summary = "Send user verification email",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun generateVerificationLink(
        @PathVariable userId: Long
    ): Response<Boolean> {
        return Response(true, corporateUserService.sendVerificationEmail(userId))
    }

}