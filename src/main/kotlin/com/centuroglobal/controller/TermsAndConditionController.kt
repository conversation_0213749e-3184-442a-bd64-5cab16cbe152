package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.data.payload.TNCRequest
import com.centuroglobal.service.TermsAndConditionService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.TermsAndCondition
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Terms And Condition", description = "Centuro Global Terms And Condition API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/t-and-c/")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class TermsAndConditionController(
    private val termsAndConditionService: TermsAndConditionService
) {

    @GetMapping
    @Operation(
        summary = "Retrieve Terms And Condition",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(@AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser): Response<Map<String, Any?>> {
        val response = termsAndConditionService.retrieveTermsAndCondition(authenticatedUser)
        return Response(success = true, payload = response)
    }

    @PutMapping
    @Operation(
        summary = "Update Last Contract View Date",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: TNCRequest
    ): Response<String> {
        termsAndConditionService.update(authenticatedUser.email, request.tncAccepted, request.renewContract,
            request.contractAccepted)
        Thread.sleep(2000)
        return Response(success = true, payload = "SUCCESS")
    }


    @GetMapping("/data")
    @Operation(
        summary = "Get Terms and conditions and Contract data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])])
    fun getTermsAndConditions(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<TermsAndCondition> {
        return Response(true, termsAndConditionService.retrieveTermsAndConditionData(authenticatedUser))
    }
}