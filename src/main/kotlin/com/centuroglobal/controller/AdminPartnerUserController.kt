package com.centuroglobal.controller
import com.centuroglobal.data.pojo.*
import com.centuroglobal.facade.AdminClientFacade
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Partner User Management", description = "Centuro Global Admin Partner User API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/partner")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #userId, 'PARTNER_USER', 'PARTNER')")
class AdminPartnerUserController(
    val partnerService: PartnerService,
    val adminClientFacade: AdminClientFacade
){

    @PutMapping("/{partnerId}/users/{userId}")
    @Operation(
    summary = "Update Partner User",
    responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
    security = [SecurityRequirement(name =  "jwt")]
)
    fun updatePartnerUser(
    @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    @PathVariable("partnerId") partnerId: Long,
    @PathVariable("userId") userId: Long,
    @RequestBody @Valid updatePartnerUserRequest: UpdatePartnerUserRequest
): Response<UserDetails> {
    return Response(true, partnerService.updatePartnerUser(partnerId,userId, updatePartnerUserRequest,authenticatedUser))
}

    @GetMapping("/{partnerId}/users/base-company")
    @Operation(
        summary = "Return list of users from Partners base company",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun listOfBaseUsers(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response <List<Map<String, String>>> {
        val result = partnerService.retrieveBaseUsersList(partnerId)
        return Response(true, result)
    }


    @GetMapping("/{partnerId}/users/listing")
    @Operation(
        summary = "Partner listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listPartnerUser(
        @PathVariable("partnerId") partnerId: Long,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Partner user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Partner user's country to search.")
        country: String?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "Account status to search.",
            schema = Schema(allowableValues = ["ACTIVE", "SUSPENDED", "PENDING_VERIFICATION"])
        )
        status: AccountStatus?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<PartnerUserList>> {
        return Response(
            true, partnerService.listPartnerUser(
                PartnerUserSearchFilter.Builder.build(
                    search,
                    country,
                    corporate,
                    status
                ),
                authenticatedUser.partnerId ?: partnerId,
                PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
            )
        )
    }

    @PostMapping("/{partnerId}/users")
    @Operation(
        summary = "Create Partner Admin",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun createPartnerAdmin(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @PathVariable("partnerId") partnerId: Long,
        @RequestBody @Valid partnerUserCreateRequest:PartnerUserCreateRequest
    ):Response<Long>{
        return Response(true, partnerService.createPartnerUser(partnerUserCreateRequest))
    }

//
//    @GetMapping("/{partnerId}/users/{userId}")
//    @Operation(
//        summary = "Return partner user details",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name = "jwt")]
//    )
//    fun getPartnerUserDetails(
//        @PathVariable("partnerId") partnerId: Long,
//        @PathVariable("userId") userId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//
//    ): Response<UserDetails> {
//        val result = partnerService.retrievePartnerUserDetails(partnerId, userId)
//        return Response(true, result)
//    }

    @GetMapping("/{partnerId}/user/{userId}")
    @Operation(
        summary = "Return partner user details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listUserDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("userId") userId: Long,
        @PathVariable("partnerId") partnerId: Long
    ): Response<UserDetails> {
        return Response(true, partnerService.listUserDetails(userId))
    }

    @GetMapping("/{partnerId}/associated-users")
    @Operation(
        summary = "Return partner associated users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getPartnerAssociatedUsers(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<List<ClientUser>> {
        val result = partnerService.retrievePartnerAssociatedUsers(partnerId)
        return Response(true, result)
    }

    @GetMapping("{partnerId}/users")
    @Operation(
        summary = "Return partner users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getPartnerUsers(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<List<ClientUser>> {
        val result = partnerService.retrievePartnerUsers(partnerId)
        return Response(true, result)
    }
}