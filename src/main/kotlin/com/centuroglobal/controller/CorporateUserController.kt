package com.centuroglobal.controller

import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.*
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateCompanyInfoRequest
import com.centuroglobal.shared.data.payload.account.signup.UpdateAccountStatusRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.subscription.response.SubscriptionUsageDetails
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Corporate User Management", description = "Centuro Global Corporate User API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/corporate/user")
class CorporateUserController(
    private val adminUserService: AdminUserService,
    private val corporateService: CorporateService,
    private val corporateUserService: CorporateUserService,
    private val contentLibraryService: ContentLibraryService,
    private val clientService: ClientService
) {
    @PostMapping
    @Operation(
        summary = "Corporate user - Add new user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "USER")
    fun post(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestBody @Valid request: CorporateUserRequest
    ): Response<String> {
        corporateUserService.createCorporateUser(request, authenticatedUser, authenticatedUser.companyId!!)
        return Response(true, "SUCCESS")
    }

    @GetMapping("/usage-details")
    @Operation(
        summary = "Corporate usage details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getSubscriptionUsageDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Parameter(name = "token", required = true, description = "Usage token")
        token: String?,
        @Parameter(name = "corporateId", required = false, description = "Corporate Id")
        corporateId: Long?): Response<SubscriptionUsageDetails>{

        val subscriptionUsageDetails=corporateUserService.getCorporateUsageDetails(token, authenticatedUser, corporateId);
        return Response(true, subscriptionUsageDetails)

    }

    @GetMapping("/listing")
    @Operation(
        summary = "Corporate user listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "firstname, lastname or email of an user to search.")
        search: String?,

        @RequestParam(name = "account", required = false, defaultValue = "")
        @Parameter(name = "account", required = false, description = "Account Id of user.")
        accountId: Long?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(name = "status", required = false, description = "Status of user.")
        status: String?,

        @RequestParam(name = "band", required = false, defaultValue = "")
        @Parameter(name = "band", required = false, description = "Band Id of user.")
        bandId: Long?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "Country code of user.")
        countryCode: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
        ): Response<PagedResult<UserCardDetails>> {

        return Response(true, corporateUserService.listUsers(
            CorporateUserSearchFilter.Builder.build(search, accountId, status, bandId, countryCode),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser))
    }

    //TODO move this to use new way of Upload - just accept the S3 key instead of full file
//    @PutMapping(value = ["/photo/{userId}"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
//    @Operation(
//        summary = "Update Profile Picture. Return full path url of the image if successful.",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun uploadProfilePicture(
//        @PathVariable userId: Long,
//        @RequestParam(value = "photo") photo: MultipartFile,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): Response<String> {
//        if(!corporateService.canUpdateUser(userId, user)) {
//            throw ApplicationException(ErrorCode.FORBIDDEN)
//        }
//        return Response(true, adminUserService.uploadProfilePicture(userId, user.userId, photo))
//    }

    @PutMapping("/{userId}")
    @Operation(
        summary = "Corporate user - Edit user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody @Valid corporateUserRequest: CorporateUserRequest,
        @PathVariable("userId") userId: Long
    ): Response<String> {
        if(!corporateService.canUpdateUser(userId, authenticatedUser)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser)
        return Response(true,"SUCCESS" )
    }

    @GetMapping("/{userId}")
    @Operation(
        summary = "Corporate user - Get user by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun get(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("userId") userId: Long
    ): Response<CorporateUserResponse> {
        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        return Response(true,corporateUserService.fetchUser(userId, authenticatedUser.companyId!!))
    }

    @PostMapping("/{userId}/access-password")
    @Operation(
        summary = "Access Password",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun accessPassword(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        return Response(true,adminUserService.accessPassword(userId, authenticatedUser))
    }

    @PutMapping("/update-status")
    @Operation(
        summary = "Update user status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateUserStatus(
        @Valid @RequestBody request: UpdateAccountStatusRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Any> {
        if(!corporateService.canUpdateUser(request.userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        return Response(true, clientService.updateUserStatus(request.userId, request.accountStatus))
    }

    //TODO check usage of this API
    @GetMapping("/client-users")
    @Operation(
        summary = "Retrieve User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveClientUsers(): Response<List<ClientUser>> {
        return Response(true, adminUserService.getClientExpertAndAdminUsers())
    }

    //REMOVE THIS API Deprecate
//    @Deprecated("REMOVE THIS API Deprecate")
//    @PutMapping(value=["/photo-upload"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
//    @Operation(
//        summary = "Update Profile Picture. Return full path url of the image if successful.",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun uploadProfilePicture(
//        @RequestParam(value = "photo") photo: MultipartFile,
//        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
//    ): Response<String> {
//        return Response(true, adminUserService.uploadProfilePicture(user.userId, user.userId, photo))
//    }


    //TODO check usage of this API
    @PutMapping(value=["/company-info"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Update corporate name and country",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateCorporateInfo(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @RequestBody updateRequest: UpdateCompanyInfoRequest
    ): Response<Boolean> {
        return Response(true, adminUserService.updateCorporateInfo(updateRequest, user))
    }

    @GetMapping
    @Operation(
        summary = "Get Content item by Country and Identifier",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getByCountryAndIdentifier(
        @Valid @RequestParam country: String,
        @Valid @RequestParam identifier: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<ContentLibraryResponse> {
        val result = contentLibraryService.getByCountryAndIdentifier(country, identifier)
        return Response(true, result)
    }

}