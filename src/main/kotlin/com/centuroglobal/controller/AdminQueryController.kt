package com.centuroglobal.controller


import com.centuroglobal.data.payload.query.QueryRequest
import com.centuroglobal.service.QueryService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.query.QueryCardDetails
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import com.centuroglobal.shared.data.pojo.query.QueryStatsResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Queries", description = "Centuro Global Admin Queries API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/queries")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'QUERY', 'QUERY')")
class AdminQueryController(open val queryService: QueryService) {
    @GetMapping
    @Operation(
        summary = "Fetch all the queries of the user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getQueries(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "")
        search: String?,

        @RequestParam(name = "categories", required = false, defaultValue = "")
        @Parameter(name = "categories", required = false, description = "")
        categories: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "")
        country: String?,

        @RequestParam(name = "corporate", required = false, defaultValue = "")
        @Parameter(name = "corporate", required = false, description = "")
        corporateId: Long?,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "")
        partnerId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "")
        to: Long?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(name = "status", required = false, description = "Query status")
        status: QueryStatus?,

        @RequestParam(name = "isDownload", required = false, defaultValue = false.toString())
        @Parameter(name = "isDownload", required = false, description = "Download Queries")
        isDownload: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isPartner", required = false)
        @Parameter(name = "isPartner", required = false, description = "Partner cases")
        isPartner: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?,

    ): Response<QueryStatsResponse> {
        val page = if (isDownload == true) {
            Int.MAX_VALUE
        } else {
            pageSize
        }
        return Response(
            true,
            queryService.listQueriesForAdmin(
                QuerySearchFilter.Builder.build(
                    search, null, null, null, categories, country, if (status != null) listOf(status) else listOf(
                        QueryStatus.OPEN, QueryStatus.RESOLVED
                    ), corporateId, from, to, authenticatedUser.partnerId ?: partnerId,cgRequested,isPartner
                ),
                PageRequest.of(pageIndex, page, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            )
        )
    }

    @GetMapping("/{queryId}")
    @Operation(
        summary = "Retrieve a Query by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getQueryById(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @PathVariable("partnerId") partnerId: Long?
    ): Response<QueryCardDetails> {
        return Response(true, queryService.getQueryById(queryId, authenticatedUser))
    }

    @PutMapping("/{queryId}")
    @Operation(
        summary = "Update Query status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
     fun updateQueryStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @PathVariable("partnerId") partnerId: Long?,
        @RequestParam("status") queryStatus: QueryStatus
    ): Response<Boolean> {
        return Response(true, queryService.updateStatus(queryId, queryStatus, authenticatedUser))
    }

    @PutMapping("/edit/{queryId}")
    @Operation(
        summary = "Edit Query",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
     fun editQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        partnerId: Long?,
        @RequestBody queryRequest: QueryRequest
    ): Response<Boolean> {
        return Response(true, queryService.editQuery(queryId, queryRequest, authenticatedUser))
    }


    @PutMapping("/{queryId}/request-cg")
    @Operation(
        summary = "Update Partner cg for query",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updatePartnerCg(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("queryId") queryId: Long,
        @RequestParam @Valid cgRequested: Boolean
    ): Response<Boolean> {
        return Response(true, queryService.updatePartnerCg(authenticatedUser.partnerId!!, queryId, cgRequested, authenticatedUser))
    }
}