package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "File-Upload", description = "Centuro Global File upload API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/uploads")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
class FileUploadController(val awsS3Service: AwsS3Service) {

    @GetMapping("/{type}")
    @Operation(
        summary = "Get pre signed url for file upload",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getPreSignedUploadUrl(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("type") type: UploadType
    ): Response<Map<String, String>> {
        return Response(true, awsS3Service.getPreSignedUploadUrl())
    }
}

enum class UploadType {
    PROFILE_PHOTO, ONBOARDING_DOC, CORPORATE_DOC
}