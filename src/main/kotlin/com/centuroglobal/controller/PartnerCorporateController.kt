package com.centuroglobal.controller

import com.centuroglobal.annotation.IsPartner
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrPartner
import com.centuroglobal.service.BandService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.UpdatePartnerCorporateRequest
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.partner.UpdatePartnerCorporateUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Partner Corporate Management", description = "Centuro Global Partner Corporate API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/partner")
@IsSuperAdminOrAdminOrPartner
class PartnerCorporateController(
    partnerService: PartnerService,
    corporateService: CorporateService,
    private val corporateUserService: CorporateUserService,
    private val bandService: BandService,
    ):BasePartnerCorporateController(partnerService, corporateService, corporateUserService) {
//This is being used
    @GetMapping("/corporates")
    @Operation(
        summary = "Partner Corporate listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listPartnerCorporates(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "name", required = false)
        @Parameter(name = "name", required = false, description = "Corporate's name")
        name: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Corporate's country to search.")
        country: String?,

        @RequestParam(name = "account id", required = false)
        @Parameter(name = "account id", required = false, description = "account id to search.")
        accountId: Long?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: CorporateStatus?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which corporate is searched.")
        from: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdTo", required = false, description = "Date up to which corporate is searched.")
        to: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<ListingWithStatsDetails<CorporateList>> {

        return Response(true, partnerService.listCorporateStats(authenticatedUser.partnerId,
            PartnerCorporateSearchFilter.Builder.build(
                name,
                country,
                accountId,
                status,
                from,
                to,
                true
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    override fun updatePartnerCorporate(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable partnerId: Long,
        @PathVariable corporateId: Long,
        @RequestBody @Valid updatePartnerRequest: UpdatePartnerCorporateRequest
    ): Response<Long> {
        return super.updatePartnerCorporate(authenticatedUser,
            partnerId,corporateId,updatePartnerRequest)
    }

    override fun listCorporate(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<ReferenceData>> {
        return super.listCorporate(partnerId, authenticatedUser)
    }

    override fun signUpCorporate(
        @RequestBody @Valid signUpRequest: SignUpRequest,
        @PathVariable("partnerId") partnerId: Long
    ): Response<Long> {
        return super.signUpCorporate(signUpRequest, partnerId)
    }

    override fun disassociateCorporate(
        @PathVariable("partnerId") partnerId: Long,
        @PathVariable("corporateId") corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<Boolean> {
        return super.disassociateCorporate(authenticatedUser.partnerId!!, corporateId, authenticatedUser)
    }

    override fun corporateUserListing(@PathVariable("partnerId") partnerId: Long,
                                      @PathVariable("corporateId") corporateId: Long,
                                      @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser):
            Response<List<CorporateUsers>> {
        return super.corporateUserListing(authenticatedUser.partnerId!!,corporateId, authenticatedUser)
    }

    override fun corporateUserAccountListing(
        @PathVariable("partnerId") partnerId: Long,
        @PathVariable("userId") userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser):
            Response<List<ReferenceData>> {
        return super.corporateUserAccountListing(partnerId,userId, authenticatedUser)
    }

    override fun retrieveCorporate(
        @PathVariable("corporateUserId") corporateUserId: Long,
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<CorporateUserDetailsResponse> {
        return super.retrieveCorporate(corporateUserId, partnerId, authenticatedUser)
    }

    override fun update(
        @RequestBody @Valid updateRequest: UpdatePartnerCorporateUserRequest,
        @PathVariable("partnerId") partnerId: Long,
        @PathVariable("corporateUserId") corporateUserId: Long
    ): Response<Long> {
        return super.update(updateRequest, partnerId, corporateUserId)
    }
//
//    @GetMapping("/{corporateId}/bands")
//    @Operation(
//        summary = "Corporate Bands listing - Only Name and Id",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun bandsForCorporate(@PathVariable("corporateId") corporateId: Long,
//                          @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
//    ): Response<List<Map<String, Any?>>> {
//        return Response(true,bandService.listBands(corporateId,authenticatedUser.partnerId))
//    }
//
//    @GetMapping("/{corporateId}/secondary")
//    @Operation(
//        summary = "Retrieve Secondary Corporate users",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun secondaryCorporates(@PathVariable corporateId: Long,
//                            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser):
//            Response<List<SecondaryCorporate>> {
//        return Response(true,corporateService.retrieveSecondaryCorporateUsers(corporateId,authenticatedUser.partnerId))
//    }

    @GetMapping("/corporates/users")
    @Operation(
        summary = "corporate user listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listCorporateUser(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Search Corporate user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Corporate user's country to search.")
        countryCode: String?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "Corporate user's corporate-id to search.")
        corporateId: Long?,

        @RequestParam(name = "subscription", required = false)
        @Parameter(name = "subscription", required = false, description = "Corporate user's subscription to search.")
        subscription: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: String?,

        @RequestParam(name = "bandId", required = false)
        @Parameter(name = "bandId", required = false, description = "Corporate user's bandName to search.")
        bandName: String?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which corporate is searched.")
        createdFrom: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdTo", required = false, description = "Date up to which corporate is searched.")
        createdTo: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean

    ): Response<ListingWithStatsDetails<CorporateUserDetails>?> {

        return Response(true, corporateUserService.listCorporateUser(
            CorporateUserDetailsFilter.Builder.build(
                search,
                countryCode,
                corporateId,
                subscription,
                status,
                bandName,
                createdFrom,
                createdTo,
                authenticatedUser.partnerId,
                true,
                null,
                null

            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

}