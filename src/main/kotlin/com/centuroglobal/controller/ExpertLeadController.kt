package com.centuroglobal.controller

import com.centuroglobal.annotation.IsExpert
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.data.payload.lead.LeadResponseRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.facade.LeadFacade
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid

@RestController
@Tag(name = "Responding to Posted Leads", description = "Centuro Global Expert Lead API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert/{userId}/lead")
@IsExpert
class ExpertLeadController(
    private val leadFacade: LeadFacade,
    private val expertUserRepository: ExpertUserRepository
) {

    @GetMapping
    @Operation(
        summary = "Search Posted Leads",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun search(
        @PathVariable userId: Long,
        @Valid search: LeadsSearchCriteria,
        @AuthenticationPrincipal @Parameter(hidden = true) loginUser: AuthenticatedUser
    ): DeferredResult<Response<PagedResult<ExpertLeadSummary>>> {
        val expertUser = expertUserRepository.findByIdOrNull(loginUser.userId)
        return ResponseUtil.toDeferredResult(
            leadFacade.searchLeadsForExpert(
                LeadSearchFilter.Builder.leadBuild(search, expertUser),
                userId,
                PageRequest.of(search.pageIndex, search.pageSize, SearchConstant.SORT_ORDER(search.sortBy, search.sort))
            )
        )
    }

    @GetMapping("/{leadId}")
    @Operation(
        summary = "Retrieve Posted Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(
        @PathVariable userId: Long,
        @PathVariable leadId: String
    ): DeferredResult<Response<LeadDetail>> {
        return ResponseUtil.toDeferredResult(leadFacade.retrieveLeadForExpert(userId, leadId))
    }

    @PostMapping("/{leadId}/response")
    @Operation(
        summary = "Respond to Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun respond(
        @PathVariable userId: Long,
        @PathVariable leadId: String,
        @Valid @RequestBody responseRequest: LeadResponseRequest
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(leadFacade.respond(userId, leadId, responseRequest))
    }

    @PutMapping("/{leadId}/response")
    @Operation(
        summary = "Respond to Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateResponse(
        @PathVariable userId: Long,
        @PathVariable leadId: String,
        @Valid @RequestBody responseRequest: LeadResponseRequest
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(leadFacade.updateResponse(userId, leadId, responseRequest))
    }

    @GetMapping("/recent")
    @Operation(
        summary = "Retrieve recently posted Leads",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun recent(@PathVariable userId: Long): DeferredResult<Response<List<RecentLeadSummary>>> {
        return ResponseUtil.toDeferredResult(leadFacade.recent())
    }
}
