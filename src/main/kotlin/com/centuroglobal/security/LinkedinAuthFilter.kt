package com.centuroglobal.security

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.GrantType
import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.enums.Scope
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.data.payload.auth.LinkedinAuthRequest
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.metrics.RequestMetadata
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.interceptor.ATTRIBUTE_METADATA
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.service.AuthService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.security.JwtHelper
import com.centuroglobal.util.ResponseUtil
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.JsonMappingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import mu.KotlinLogging
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import java.time.LocalDateTime
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse


private val log = KotlinLogging.logger {}

class LinkedinAuthFilter(
    url: String,
    private val accessLogService: AccessLogService,
    private val jwtHelper: JwtHelper,
    private val authManager: AuthenticationManager,
    private val mapper: ObjectMapper,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository,
    private val authService: AuthService
) : AbstractAuthenticationProcessingFilter(AntPathRequestMatcher(url)) {

    override fun attemptAuthentication(request: HttpServletRequest, response: HttpServletResponse): Authentication {
        val credentials: LinkedinAuthRequest = retrieveCredentials(request)
        log.info("keepMeInformed flag value ${credentials.keepMeInformed}")
        injectRequestAttribute(request, GrantType.LINKEDIN)
        val authentication: Authentication =
            AuthenticationToken(
                GrantType.LINKEDIN,
                Scope.APP,
                mapOf(
                    "code" to credentials.code,
                    "callerId" to credentials.callerId,
                    "redirectURL" to credentials.redirectURL,
                    "keepMeInformed" to credentials.keepMeInformed.toString()
                )
            )
        return authManager.authenticate(authentication)
    }

    override fun successfulAuthentication(
        request: HttpServletRequest,
        response: HttpServletResponse,
        chain: FilterChain,
        authResult: Authentication
    ) {
        val claims: MutableMap<String, Any?> = (if (authResult is AuthenticatedUser) {
            authResult.getClaims()
        } else {
            emptyMap()
        }).toMutableMap()

        val refreshToken: String? = if (authResult is AuthenticatedUser) {
            authResult.refreshToken
        } else {
            null
        }
        val date: LocalDateTime? = if (authResult is AuthenticatedUser) {
            authResult.lastLoginDate
        } else {
            null
        }
        val userAccess = if (authResult is AuthenticatedUser && "ROLE_CORPORATE" == authResult.role) {
            authService.getUserAccess(authResult.userId, authResult.role)
        } else {
            null
        }

        val visibilities = if (authResult is AuthenticatedUser && "ROLE_CORPORATE" == authResult.role) {
            authService.getUserVisibilities(authResult.userId, authResult.role)
        } else {
            null
        }

        claims[JwtClaim.USER_ACCESS.key] = userAccess?.first
        claims[JwtClaim.USER_VISIBILITY.key] = visibilities
        val tokenResult: TokenResult? = jwtHelper.generateTokenResult(refreshToken, claims, false, date)
        if (tokenResult != null) {
            tokenResult.userAccess = userAccess?.first
            tokenResult.userVisibilities = visibilities
            tokenResult.bandName = userAccess?.second
            tokenResult.companyName = userAccess?.third
            tokenResult.profilePhotoUrl = getProfilePhotoUrl(authResult)
            tokenResult.isLinkedin = true
            logRequest(request, HttpStatus.OK.value(), "${claims[JwtClaim.USER_ID.key]}".toLong())
            ResponseUtil.writeResponseAsJson(mapper, response, Response(payload = tokenResult, success = true))
        } else {
            logRequest(request, HttpStatus.INTERNAL_SERVER_ERROR.value())
            ResponseUtil.writeErrorResponseAsJson(mapper, response, ErrorCode.INTERNAL_SERVER_ERROR)
        }
    }

    private fun getProfilePhotoUrl(authResult: Authentication): String? {
        val userId = if(authResult is AuthenticatedUser) authResult.userId else return null
        val corporateUser = loginAccountRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        return corporateUser.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}
    }

    override fun unsuccessfulAuthentication(
        request: HttpServletRequest,
        response: HttpServletResponse,
        failed: AuthenticationException
    ) {
        val error = if (failed is ApplicationException) {
            failed.error
        } else {
            ErrorCode.UNAUTHORIZED
        }
        logRequest(request, error.httpStatus!!.value())
        ResponseUtil.writeErrorResponseAsJson(mapper, response, error)
    }

    private fun logRequest(request: HttpServletRequest, httpStatus: Int, userId: Long? = null) {
        val metadata: RequestMetadata = request.getAttribute(ATTRIBUTE_METADATA) as RequestMetadata
        metadata.completedAt = System.currentTimeMillis()
        metadata.responseStatus = httpStatus
        if (userId != null) {
            metadata.userId = userId
        }
        accessLogService.addToQueue(metadata)
    }

    private fun injectRequestAttribute(request: HttpServletRequest, grantType: GrantType?) {
        val metadata = request.getAttribute(ATTRIBUTE_METADATA)
        if (metadata is RequestMetadata) {
            request.setAttribute(
                ATTRIBUTE_METADATA, RequestMetadata.ModelMapper.fromAuthRequest(metadata, grantType)
            )
        } else {
            request.setAttribute(
                ATTRIBUTE_METADATA, RequestMetadata.ModelMapper.fromAuthRequest(request, grantType)
            )
        }
    }

    private fun retrieveCredentials(request: HttpServletRequest): LinkedinAuthRequest {
        try {
            if (request.method == HttpMethod.POST.name()) {
                val payload: LinkedinAuthRequest? = mapper.readValue(request.inputStream)
                when {
                    payload == null -> {
                        throw ApplicationException(ErrorCode.BAD_REQUEST)
                    }
                    payload.code.isBlank() -> {
                        throw ApplicationException(ErrorCode.BAD_REQUEST)
                    }
                    payload.callerId.isBlank() -> {
                        throw ApplicationException(ErrorCode.BAD_REQUEST)
                    }
                    else -> {
                        return payload
                    }
                }
            } else {
                throw ApplicationException(ErrorCode.METHOD_NOT_ALLOWED)
            }
        } catch (ex: Exception) {
            when (ex) {
                is JsonMappingException, is JsonParseException -> throw ApplicationException(ErrorCode.BAD_REQUEST)
                is ApplicationException -> throw ex
                else -> throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
            }
        }
    }
}