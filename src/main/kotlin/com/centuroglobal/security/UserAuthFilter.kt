package com.centuroglobal.security

import com.centuroglobal.data.payload.auth.TokenRequest
import com.centuroglobal.interceptor.ATTRIBUTE_METADATA
import com.centuroglobal.service.AuthService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.GrantType
import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.enums.Scope
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.metrics.RequestMetadata
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.LoginHistoryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.util.ResponseUtil
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.JsonMappingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import mu.KotlinLogging
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import java.util.*

private val log = KotlinLogging.logger {}
class UserAuthFilter(
    url: String,
    private val accessLogService: AccessLogService,
    private val authManager: AuthenticationManager,
    private val mapper: ObjectMapper,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository,
    private val authService: AuthService,
    private val loginHistoryRepository: LoginHistoryRepository
) : AbstractAuthenticationProcessingFilter(AntPathRequestMatcher(url)) {

    init {
        authenticationManager = this.authManager
    }

    override fun attemptAuthentication(request: HttpServletRequest, response: HttpServletResponse): Authentication {
        injectRequestAttribute(request, null)

        val credentials: TokenRequest = retrieveCredentials(request)
        val grantType = GrantType.fromValue(credentials.grantType) ?: throw ApplicationException(ErrorCode.BAD_REQUEST)
        val scope = Scope.fromValue(credentials.scope ?: "")

        injectRequestAttribute(request, grantType)

        val decodedBytes: ByteArray = Base64.getDecoder().decode(credentials.password)
        val decodedPwd = String(decodedBytes)

        val authentication: Authentication =
            AuthenticationToken(
                grantType,
                scope,
                mapOf(
                    "password" to decodedPwd,
                    "username" to credentials.username,
                    "refreshToken" to credentials.refreshToken,
                    "mfaCode" to credentials.mfaCode
                ),
                getRequestHeaders(request)
            )
        return authManager.authenticate(authentication)
    }

    private fun getRequestHeaders(request: HttpServletRequest): Map<String, String?> {
        var ip = request.getHeader("X-Forwarded-For")
        if(ip.isNullOrBlank()) {
            ip = request.remoteAddr
        }
        return mapOf(
            HttpHeaders.USER_AGENT to request.getHeader(HttpHeaders.USER_AGENT),
            "IP_ADDRESS" to ip
        )
    }

    override fun successfulAuthentication(
        request: HttpServletRequest,
        response: HttpServletResponse,
        chain: FilterChain,
        authResult: Authentication
    ) {

        val authenticatedUser = if (authResult is AuthenticatedUser) authResult else null

        log.debug("creating claims")
        val claims = authenticatedUser?.getClaims()?.toMutableMap() ?: mutableMapOf()
        val tokenResult = authService.generateTokenResult(
            claims,
            authenticatedUser?.refreshToken,
            authenticatedUser?.lastLoginDate,
            authenticatedUser?.userId,
            authenticatedUser?.role,
            authenticatedUser?.loginToken
        )

        log.debug("token generated")
        if (tokenResult != null) {
            logRequest(request, HttpStatus.OK.value(), "${claims[JwtClaim.USER_ID.key]}".toLong())
            ResponseUtil.writeResponseAsJson(mapper, response, Response(payload = tokenResult, success = true))
        } else {
            logRequest(request, HttpStatus.INTERNAL_SERVER_ERROR.value())
            ResponseUtil.writeErrorResponseAsJson(mapper, response, ErrorCode.INTERNAL_SERVER_ERROR)
        }
    }

    private fun isOnboardingSwitchAvailable(userId: Long): Boolean {
        return (loginHistoryRepository.countByUserId(userId) in 4..10)
    }

    private fun getLoginAccount(authResult: Authentication): LoginAccountEntity {
        val userId = (authResult as AuthenticatedUser).userId
        return loginAccountRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
    }
    private fun getProfilePhotoUrl(loginAccountEntity: LoginAccountEntity): String? {
        log.debug("getting profile picture url")
        return loginAccountEntity.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}
    }

    override fun unsuccessfulAuthentication(
        request: HttpServletRequest,
        response: HttpServletResponse,
        failed: AuthenticationException
    ) {
        val error = if (failed is ApplicationException) {
            failed.error
        } else {
            ErrorCode.UNAUTHORIZED
        }
        logRequest(request, error.httpStatus!!.value())
        ResponseUtil.writeErrorResponseAsJson(mapper, response, error)
    }

    private fun injectRequestAttribute(request: HttpServletRequest, grantType: GrantType?) {
        val metadata = request.getAttribute(ATTRIBUTE_METADATA)
        if (metadata is RequestMetadata) {
            request.setAttribute(
                ATTRIBUTE_METADATA, RequestMetadata.ModelMapper.fromAuthRequest(metadata, grantType)
            )
        } else {
            request.setAttribute(
                ATTRIBUTE_METADATA, RequestMetadata.ModelMapper.fromAuthRequest(request, grantType)
            )
        }
    }

    private fun logRequest(request: HttpServletRequest, httpStatus: Int, userId: Long? = null) {
        val metadata: RequestMetadata = request.getAttribute(ATTRIBUTE_METADATA) as RequestMetadata
        metadata.completedAt = System.currentTimeMillis()
        metadata.responseStatus = httpStatus
        if (userId != null) {
            metadata.userId = userId
        }
        accessLogService.addToQueue(metadata)
    }

    private fun retrieveCredentials(request: HttpServletRequest): TokenRequest {
        try {
            if (request.method == HttpMethod.POST.name()) {
                val payload: TokenRequest? = mapper.readValue(request.inputStream)
                if (payload == null) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                } else if (payload.grantType == GrantType.PASSWORD.value &&
                    (payload.username.isNullOrBlank() || payload.password.isNullOrBlank())
                ) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                } else if (payload.grantType == GrantType.REFRESH_TOKEN.value && payload.refreshToken.isNullOrBlank()) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                } else if (payload.scope != null && !Scope.values().map { it.value }.contains(payload.scope)) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                } else {
                    return payload
                }
            } else {
                throw ApplicationException(ErrorCode.METHOD_NOT_ALLOWED)
            }
        } catch (ex: Exception) {
            when (ex) {
                is JsonMappingException, is JsonParseException -> throw ApplicationException(ErrorCode.BAD_REQUEST)
                is ApplicationException -> throw ex
                else -> throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
            }
        }
    }
}