package com.centuroglobal.interceptor

import com.centuroglobal.shared.data.enums.MdcAttribute
import com.centuroglobal.shared.data.pojo.metrics.RequestMetadata
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.metrics.AccessLogService
import mu.KotlinLogging
import org.slf4j.MDC
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.AsyncHandlerInterceptor
import org.springframework.web.servlet.ModelAndView
import jakarta.servlet.DispatcherType
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse

const val ATTRIBUTE_METADATA = "requestMetadata"

private val log = KotlinLogging.logger {}

@Component
class LoggingInterceptor(val accessLogService: AccessLogService) : AsyncHandlerInterceptor {

    private var storedKeys: ThreadLocal<MutableSet<String>> = ThreadLocal.withInitial { mutableSetOf<String>() }

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        log.trace("---Logging PreHandle [Start] - ${request.requestURI} ---")

        val validDispatcherTypes = listOf(DispatcherType.ASYNC, DispatcherType.REQUEST)

        if (handler is HandlerMethod && validDispatcherTypes.contains(request.dispatcherType)) {
            if (request.dispatcherType == DispatcherType.REQUEST) {
                val principal = SecurityContextHolder.getContext().authentication.principal
                request.setAttribute(
                    ATTRIBUTE_METADATA,
                    RequestMetadata.ModelMapper.fromRequest(
                        request, handler,
                        if (principal != null && principal is AuthenticatedUser) principal else null
                    )
                )
            }
            val metadata: RequestMetadata = request.getAttribute(ATTRIBUTE_METADATA) as RequestMetadata
            addKey(MdcAttribute.XID.key, metadata.xid)
            addKey(MdcAttribute.CONTROLLER_NAME.key, metadata.controller)
            addKey(MdcAttribute.ACTION_NAME.key, metadata.action)
            addKey(MdcAttribute.CLIENT_IP.key, metadata.clientIp)
            addKey(MdcAttribute.CLIENT_Id.key, metadata.clientId)
            addKey(MdcAttribute.USER_Id.key, metadata.userId?.toString() ?: "")
            addKey(MdcAttribute.USER_AGENT.key, metadata.userAgent)
        }

        return true
    }

    override fun afterConcurrentHandlingStarted(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any
    ) {
        log.trace("---Logging AfterConcurrentHandlingStarted [Start] - ${request.requestURI} ---")
        if (handler is HandlerMethod) {
            removeKeys()
        }
    }

    override fun postHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        modelAndView: ModelAndView?
    ) {
        log.trace("---Logging PostHandler [Start] - ${request.requestURI} ---")
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        log.trace("---Logging AfterCompletion [Start] - ${request.requestURI} ---")
        if (handler is HandlerMethod) {
            val metadata: RequestMetadata = request.getAttribute(ATTRIBUTE_METADATA) as RequestMetadata
            metadata.completedAt = System.currentTimeMillis()
            metadata.responseStatus = response.status
            log.trace("---Logging AfterCompletion [HttpMethod=${metadata.method}; HttpStatus=${metadata.responseStatus}; Time taken=${metadata.getResponseTime()}] - ${request.requestURI} ---")
            if (request.requestURI.startsWith("/api/")) {
                accessLogService.addToQueue(metadata)
            }
            removeKeys()
        }
    }

    private fun addKey(key: String, value: String) {
        MDC.put(key, value)
        storedKeys.get().add(key)
    }

    private fun removeKeys() {
        storedKeys.get().forEach {
            MDC.remove(it)
        }
        storedKeys.remove()
    }
}