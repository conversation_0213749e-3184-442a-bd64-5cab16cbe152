package com.centuroglobal.shared.util

import java.time.*
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.*

class TimeUtil {
    companion object {

        @JvmStatic
        fun fromInstant(epochSecond: Long): LocalDateTime {
            return LocalDateTime.ofInstant(
                Instant.ofEpochSecond(epochSecond),
                TimeZone.getDefault().toZoneId()
            )
        }

        @JvmStatic
        fun fromInstantToLocalDate(epochSecond: Long): LocalDate {
            return LocalDate.ofInstant(
                Instant.ofEpochSecond(epochSecond),
                TimeZone.getDefault().toZoneId()
            )
        }

        @JvmStatic
        fun fromInstantToDate(epochSecond: Long): Date {

            val dates = LocalDate.ofInstant(
                Instant.ofEpochSecond(epochSecond),
                TimeZone.getDefault().toZoneId()
            )

            return Date.from(
                dates.atStartOfDay()
                    .atZone(ZoneId.systemDefault())
                    .toInstant()
            )
        }

        @JvmStatic
        fun fromInstantMillis(epochMilli: Long): LocalDateTime {

            return LocalDateTime.ofInstant(
                Instant.ofEpochMilli(epochMilli),
                TimeZone.getDefault().toZoneId()
            )
        }

        @JvmStatic
        fun toEpochMillis(localDateTime: LocalDateTime): Long {
            return localDateTime.atZone(
                TimeZone.getDefault().toZoneId()
            ).toInstant().toEpochMilli()
        }

        @JvmStatic
        fun toEpochMillis(localDate: LocalDate): Long {
            return localDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli()
        }

        @JvmStatic
        fun toEpochSeconds(localDateTime: LocalDateTime): Long {
            return toEpochMillis(localDateTime) / 1000
        }

        @JvmStatic
        fun getDuration(startTime: Date, endTime: Date): String {
            val duration = Duration.between(startTime.toInstant(), endTime.toInstant())
            val hours = duration.toHours()
            /*val formattedDiff = java.lang.String.format(
                Locale.ENGLISH,
                "%d days %d hours %d minutes",
                duration.toDays(), duration.toHours(), duration.toMinutes()
            )*/
            return "$hours + hrs"
        }

        @JvmStatic
        fun startDateTimeOfMonth(localDate: LocalDate = LocalDate.now()): LocalDateTime {
            return atStartOfDay(localDate.with(TemporalAdjusters.firstDayOfMonth()))
        }

        @JvmStatic
        fun lastDateTimeOfMonth(localDate: LocalDate = LocalDate.now()): LocalDateTime {
            return atEndOfDay(localDate.with(TemporalAdjusters.lastDayOfMonth()))
        }

        @JvmStatic
        fun atStartOfDay(localDate: LocalDate = LocalDate.now()): LocalDateTime {
            return localDate.atTime(LocalTime.MIDNIGHT)
        }
        @JvmStatic
        fun atEndOfDay(localDate: LocalDate = LocalDate.now()): LocalDateTime {
            return localDate.atTime(LocalTime.MAX)
        }

        @JvmStatic
        fun getNthWorkingDay(startDate: LocalDateTime, n: Long): LocalDateTime {
            var currentDate = startDate
            var workDaysCount = 0

            while (workDaysCount < n) {
                currentDate = currentDate.plus(1, ChronoUnit.DAYS)
                if (isWorkingDay(currentDate)) {
                    workDaysCount++
                }
            }

            return currentDate
        }

        fun isWorkingDay(date: LocalDateTime): Boolean {
            val dayOfWeek = date.dayOfWeek
            return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY
        }

        fun getWorkingDaysBetween(startDate: LocalDateTime, endDate: LocalDateTime): Long {
            var currentDate = startDate
            var workingDaysCount = 0L
            
            val step = if (currentDate.isBefore(endDate)) 1L else -1L

            while (currentDate.toLocalDate()!=endDate.toLocalDate()) {
                if (isWorkingDay(currentDate)) {
                    workingDaysCount++
                }
                currentDate = currentDate.plusDays(step)
            }
            return workingDaysCount * step
        }
    }
}