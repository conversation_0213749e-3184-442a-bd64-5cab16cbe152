package com.centuroglobal.shared.util


import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class UpdateCompanyStatusUtil(
    private val corporateUserRepository: CorporateUserRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val partnerRepository: PartnerRepository,
    private val corporateRepository: CorporateRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val userRoleRepository: UserRoleRepository
) {
    @Transactional
    fun updateCompanyStatus(
        request: UpdateCompanyStatusRequest
    ): Boolean {

        when (request.type) {
            UserType.PARTNER -> {
                val partner = request.id?.let {
                    partnerRepository.findById(it).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }
                }!!

                if (partner.status == CorporateStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                    throw ApplicationException(ErrorCode.PENDING_ACCOUNT_UPDATE_FAIL)
                }

                partner.partnerUsers.forEach {
                    if (it.status == AccountStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                        // do nothing
                    } else {
                        it.status = request.status!!
                        loginAccountRepository.save(it)
                    }
                }

                partner.status = CorporateStatus.valueOf(request.status.toString())
                partnerRepository.save(partner)
            }

            UserType.EXPERT -> {
                val expert = request.id?.let {
                    expertCompanyProfileRepository.findById(it)
                        .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                }

                expert?.users?.forEach {
                    if (it.status == AccountStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                        //do nothing
                    } else {
                        it.status = request.status!!
                        expertUserRepository.save(it)
                    }
                }
            }

            UserType.CORPORATE -> {
                val corporate = request.id?.let {
                    corporateRepository.findById(it).orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) }
                }!!

                if (corporate.status == CorporateStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                    throw ApplicationException(ErrorCode.PENDING_ACCOUNT_UPDATE_FAIL)
                }

                corporate.users.forEach {
                    if (it.status != AccountStatus.PENDING_VERIFICATION && request.status != AccountStatus.ACTIVE) {
                        // do nothing
                    } else {
                        it.status = request.status!!
                        corporateUserRepository.save(it)
                    }
                }

                corporate.status = CorporateStatus.valueOf(request.status.toString())
                corporateRepository.save(corporate)
            }

            else -> {
                return false
            }
        }
        return true


    }
}