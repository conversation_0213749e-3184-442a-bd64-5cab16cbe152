package com.centuroglobal.shared.util

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import java.security.SecureRandom
import java.util.*


class StringUtil {
    companion object {

        @JvmStatic
        fun trimSpaceIfNullOrBlankThrows(
            theString: String?,
            ex: ApplicationException = ApplicationException(ErrorCode.BAD_REQUEST)
        ): String {
            val ret = theString?.trim()
            if (ret.isNullOrBlank()) {
                throw ex
            } else {
                return ret
            }
        }

        @JvmStatic
        fun createRandomString(codeLength: Int): String {
            val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
            val sb = StringBuilder()
            val random: Random = SecureRandom()
            for (i in 0 until codeLength) {
                val c: Char = if (i == 0) {
                    chars[random.nextInt(26)]
                } else chars[random.nextInt(chars.size)]
                sb.append(c)
            }
            return sb.toString()
        }

        @JvmStatic
        fun getShortName(firstName: String, lastName: String): String {
            var shortName: String = ""
            if (firstName.isNotEmpty()) {
                shortName = firstName.toCharArray()[0].toString().uppercase(Locale.getDefault())
            }
            if (lastName.isNotEmpty()) {
                shortName += lastName.toCharArray()[0].toString().uppercase(Locale.getDefault())
            }
            return shortName
        }

    }
}