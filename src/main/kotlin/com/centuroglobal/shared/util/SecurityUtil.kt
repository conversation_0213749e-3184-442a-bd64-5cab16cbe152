package com.centuroglobal.shared.util

import org.apache.commons.codec.digest.DigestUtils

class SecurityUtil {
    companion object {
        private val charPool: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')

        @JvmStatic
        fun generateSalt(noOfChars: Int): String {
            return (1..noOfChars)
                .map { _ -> kotlin.random.Random.nextInt(0, charPool.size) }
                .map(charPool::get)
                .joinToString("")
        }

        @JvmStatic
        fun generateMd5Hash(data: String): String {
            // 1. generate salt
            val salt = generateSalt(8)

            // 2. generate code
            return DigestUtils.md5Hex("$data:${salt}")
        }

    }
}