package com.centuroglobal.shared.service.subscription

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.pojo.subscription.usage.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.subscription.usage.*
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}
@Service
class UsageFactory(
    private val corporateUserRepository: CorporateUserRepository,
    private val blueprintUsageRepository: BlueprintUsageRepository,
    private val caseUsageRepository: CaseUsageRepository,
    private val userUsageRepository: UserUsageRepository,
    private val askAiUsageRepository: AskAiUsageRepository,
    private val corporateDocUploadUsageRepository: CorporateDocUploadUsageRepository,
    private val costOfLivingUsageRepository: CostOfLivingUsageRepository,
    private val complianceCalendarUsageRepository: ComplianceCalendarUsageRepository,
    private val expertSupportUsageRepository: ExpertSupportUsageRepository,
    private val visaAssessmentUsageRepository: VisaAssessmentUsageRepository,
    private val travelAssessmentUsageRepository: TravelAssessmentUsageRepository,
    private val playbookUsageRepository: PlaybookUsageRepository,
    private val caseRepository: CaseRepository,
    private val loginAccountRepository: LoginAccountRepository
) {

        fun getUsageFactory(key: String, userActions: List<UserActionEntity>): AbstractUsage {

            return when(key) {
                "BLUEPRINT" -> {
                    BlueprintUsage(userActions, corporateUserRepository, blueprintUsageRepository)
                }
                "CASE" -> {
                    CaseUsage(userActions, corporateUserRepository, caseUsageRepository)
                }
                "USER" -> {
                    UserUsage(userActions, corporateUserRepository, userUsageRepository)
                }
                "ASK_AI" -> {
                    AskAiUsage(userActions, corporateUserRepository, askAiUsageRepository)
                }
                "VISA_ELIGIBILITY_ASSESSMENT" -> {
                    VisaAssessmentUsage(userActions, corporateUserRepository, visaAssessmentUsageRepository)
                }
                "EXPERT_SUPPORT" -> {
                    ExpertSupportUsage(userActions, corporateUserRepository, expertSupportUsageRepository)
                }
                "CORPORATE_UPLOAD_DOC" -> {
                    CorporateDocUploadUsage(userActions, corporateUserRepository, corporateDocUploadUsageRepository)
                }
                "COST_OF_LIVING" -> {
                    CostOfLivingUsage(userActions, corporateUserRepository, costOfLivingUsageRepository)
                }
                "COMPLIANCE_CALENDAR" -> {
                    ComplianceCalendarUsage(userActions, corporateUserRepository, complianceCalendarUsageRepository, caseRepository)
                }
                "PLAYBOOK" -> {
                    PlaybookUsage(userActions, corporateUserRepository, playbookUsageRepository)
                }
                "TRAVEL_ASSESSMENT" -> {
                    TravelAssessmentUsage(userActions, travelAssessmentUsageRepository, loginAccountRepository)
                }
                else -> {
                    log.error("$key is not yet implemented")
                    throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
                }
            }
        }
}

