package com.centuroglobal.shared.service.mail

import com.centuroglobal.shared.data.pojo.email.MailTemplate
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.stereotype.Service
import java.nio.charset.StandardCharsets
import jakarta.mail.internet.InternetAddress

private val log = KotlinLogging.logger {}

@Service
class MailSendingService(
    @Value("\${spring.mail.sender-email-address}")
    private val senderEmailAddress: String,
    @Value("\${spring.mail.mock-sending}")
    private val mockSending: Boolean,
    @Value("\${spring.mail.subject-prefix}")
    private val subjectPrefix: String,
    private val mailSender: JavaMailSender,
    private val templateEngineWrapper: TemplateEngineWrapper
) {
    fun sendEmail(mailTemplate: MailTemplate): Boolean {
        try {
            // 0) initial setup
            val message = mailSender.createMimeMessage()
            val helper = MimeMessageHelper(
                message,
                MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                StandardCharsets.UTF_8.name()
            )
            // 1) eval the context
            val ctx = mailTemplate.context
            // 2) set content and template
            if (ctx != null) {
                helper.setText(templateEngineWrapper.process(mailTemplate.templateName, ctx)!!, true)
            } else {
                helper.setText("", true)
            }
            if (mailTemplate.attachment != null) {
                helper.addAttachment(mailTemplate.attachmentName!!, mailTemplate.attachment)
            }
            // 3) set the recipient and sender
            val to = mailTemplate.recipient.split(",").toTypedArray()
            helper.setTo(to)
            message.setFrom(InternetAddress(senderEmailAddress, mailTemplate.from))
            // 4) add subject
            message.subject = "$subjectPrefix ${mailTemplate.subject}"
            // 5) add cc if needed
            if (!mailTemplate.ccRecipients.isNullOrEmpty()) helper.setCc(mailTemplate.ccRecipients.toTypedArray())
            // 6) add bcc if needed
            if (!mailTemplate.bccRecipients.isNullOrEmpty()) helper.setBcc(mailTemplate.bccRecipients.toTypedArray())
            // 7) send the email
            if (!mockSending) mailSender.send(message)
            return true
        } catch (ex: Exception) {
            log.error("Error sending email", ex)
            return false
        }
    }
}