package com.centuroglobal.shared.service.aws

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.properties.AwsS3Properties
import mu.KotlinLogging
import org.apache.tika.Tika
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import software.amazon.awssdk.auth.credentials.InstanceProfileCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.core.sync.ResponseTransformer
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest
import java.io.InputStream
import java.time.Duration
import java.util.*

private const val DEFAULT_CONTENT_TYPE = "application/octet-stream"

private val log = KotlinLogging.logger {}

@Service
class AwsS3Service(private val s3Prop: AwsS3Properties) {

    private val credentialsProvider: InstanceProfileCredentialsProvider = InstanceProfileCredentialsProvider.create()

    var s3: S3Client = S3Client.builder()
        .region(Region.of(AppConstant.AWS_REGION))
        .credentialsProvider(credentialsProvider)
        .build()

    var presigner: S3Presigner = S3Presigner.builder()
        .region(Region.of(AppConstant.AWS_REGION))
        .credentialsProvider(credentialsProvider)
        .build()

    fun uploadFile(
        multipartFile: MultipartFile,
        folder: String,
        publicReadAccess: Boolean = false,
        fileName: String? = null
    ): AwsS3FileMetadata? {
        var filename = fileName ?: multipartFile.originalFilename
        filename = if (filename.isNullOrBlank()) UUID.randomUUID().toString() else filename

        val s3Key = generateS3Key(folder)

        uploadFile(multipartFile.inputStream, s3Key, publicReadAccess, multipartFile.size)

        val url = if (publicReadAccess) getS3Url(s3Key) else null
        return AwsS3FileMetadata(
            contentType = (multipartFile.contentType ?: getMimeTypeFromInputStream(multipartFile.inputStream)), fileName = filename, key = s3Key,
            url = url, publicAccess = publicReadAccess
        )
    }

    fun uploadFile(
        multipartFile: MultipartFile,
        fileName: String,
        bucket: String
    ) {
        uploadFile(multipartFile.inputStream, fileName, bucket, multipartFile.size)
    }

    fun fileExist(bucketKey: String): Boolean {
        val key = extractKey(bucketKey)

        val headObjectResponse = s3.headObject(HeadObjectRequest.builder().bucket(s3Prop.defaultBucket).key(key).build())

        return headObjectResponse.contentType().isNotEmpty()
    }

    fun downLoadFile(bucketKey: String): InputStream {
        val key = extractKey(bucketKey)
        return downLoadFile(s3Prop.defaultBucket, key)
    }

    fun downLoadFile(bucket: String, key: String): InputStream {
        val getObjectRequest = GetObjectRequest.builder()
            .bucket(bucket)
            .key(key)
            .build()

        return s3.getObject(getObjectRequest, ResponseTransformer.toInputStream())
    }

    fun getBucket(): String {
        return s3Prop.defaultBucket
    }

    fun deleteFile(bucketKey: String) {
        val key = extractKey(bucketKey)
        deleteFileByS3Key(key, s3Prop.defaultBucket)
    }

    fun deleteFileByS3Key(s3Key: String) {
        deleteFileByS3Key(s3Key, s3Prop.defaultBucket)
    }

    fun deleteFileByS3Key(s3Key: String, bucket:String) {
        val deleteObjectRequest = DeleteObjectRequest.builder()
            .bucket(bucket)
            .key(s3Key)
            .build()

        s3.deleteObject(deleteObjectRequest)
    }

    fun getProfilePicUrl(profilePhotoUrl: String?): String {
        return if (profilePhotoUrl.isNullOrBlank()) "" else getS3Url(profilePhotoUrl)
    }

    fun getS3Url(s3Key: String): String {
        return if (s3Key.isBlank()) s3Key else getS3Url(s3Prop.defaultBucket, s3Key)
    }

    fun getS3PublicUrl(s3Key: String): String {
        return "${s3Prop.publicBucketUri}/${s3Key}"
//        return if (s3Prop.proxiedUrl.isNullOrBlank()) {
//            // S3 URL
//            // E.g. https://centuroglobal-prod.s3.eu-west-2.amazonaws.com/{s3Key}
//            // "https://${s3Prop.defaultBucket}.s3.${AppConstant.AWS_REGION}.amazonaws.com/${s3Key}"
//            val calendar = Calendar.getInstance()
//            calendar.time = Date()
//            calendar.add(Calendar.MINUTE, 5) // Generated URL will be valid for 5 mint
//            s3.generatePresignedUrl(s3Prop.defaultBucket, s3Key, calendar.time, HttpMethod.GET).toString()
//        } else {
//            // Proxied URL. In this case, nginx will be used to proxy request to S3,
//            // thus hiding the S3 URL to the front-end/client.
//            // E.g. https://assets.centuroglobal.co.uk/{s3Key}
//            "${s3Prop.proxiedUrl}/${s3Key}"
//        }
    }


    fun extractKey(bucketKey: String): String {
        return if (bucketKey.startsWith("https://${s3Prop.defaultBucket}.s3.${AppConstant.AWS_REGION}.amazonaws.com/")) {
            bucketKey.substringAfter("https://${s3Prop.defaultBucket}.s3.${AppConstant.AWS_REGION}.amazonaws.com/")
        } else {
            bucketKey.substringAfter("${s3Prop.proxiedUrl}/")
        }
    }

    fun uploadFile(
        inputStream: InputStream, s3Key: String,
        publicReadAccess: Boolean = false,
        fileSize: Long
    ) {
        val objectRequest = PutObjectRequest.builder()
            .bucket(s3Prop.defaultBucket)
            .key(s3Key)
            .acl(if (publicReadAccess) ObjectCannedACL.PUBLIC_READ else ObjectCannedACL.PRIVATE)
            .build()

        s3.putObject(objectRequest, RequestBody.fromInputStream(inputStream, fileSize))

    }

    fun uploadFile(
        inputStream: InputStream, fileName: String,
        bucketName: String? = null, fileSize: Long,  publicReadAccess: Boolean = false, contentType: String?="application/octet-stream"
    ) {
        val putObjectRequest = PutObjectRequest.builder()
            .bucket(bucketName)
            .key(fileName)
            .acl(if (publicReadAccess) ObjectCannedACL.PUBLIC_READ else ObjectCannedACL.PRIVATE)
            .contentType(contentType)
            .build()

        s3.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, fileSize))
    }

    fun getMimeTypeFromInputStream(inputStream: InputStream): String {
        return try {
            Tika().detect(inputStream)
        } catch (ex: Exception) {
            DEFAULT_CONTENT_TYPE
        }
    }


    private fun generateS3Key(folder: String, key: String? = null): String {
        return "$folder/${key ?: UUID.randomUUID()}"
    }

    fun getS3Url(s3Bucket: String, s3Key: String): String {
        return if (s3Prop.proxiedUrl.isNullOrBlank()) {
            // S3 URL
            // E.g. https://centuroglobal-prod.s3.eu-west-2.amazonaws.com/{s3Key}
            // "https://${s3Prop.defaultBucket}.s3.${AppConstant.AWS_REGION}.amazonaws.com/${s3Key}"

            val getObjectRequest = GetObjectRequest.builder()
                .bucket(s3Bucket)
                .key(s3Key)
                .build()

            val getObjectPresignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(5))
                .getObjectRequest(getObjectRequest)
                .build()

            val presignedGetObjectRequest: PresignedGetObjectRequest =
                presigner.presignGetObject(getObjectPresignRequest)

            presignedGetObjectRequest.url().toString()
        } else {
            // Proxied URL. In this case, nginx will be used to proxy request to S3,
            // thus hiding the S3 URL to the front-end/client.
            // E.g. https://assets.centuroglobal.co.uk/{s3Key}
            "${s3Prop.proxiedUrl}/${s3Key}"
        }
    }

    fun getPreSignedUploadUrl(): Map<String, String> {

        val s3Key = UUID.randomUUID().toString()

        val putObjectRequest = PutObjectRequest.builder()
            .bucket(s3Prop.defaultBucket)
            .key("temp/$s3Key")
            .build()

        val putObjectPreSignRequest = PutObjectPresignRequest.builder()
            .signatureDuration(Duration.ofMinutes(5))
            .putObjectRequest(putObjectRequest)
            .build()

        val preSignedPutObjectRequest: PresignedPutObjectRequest =
            presigner.presignPutObject(putObjectPreSignRequest)

       return mapOf("key" to s3Key, "url" to preSignedPutObjectRequest.url().toString())
    }

    fun uploadFromTmp(s3Key: String, destKey: String, bucketName: String?=s3Prop.defaultBucket): Boolean {

        val copyObjectRequest = CopyObjectRequest.builder()
            .sourceBucket(s3Prop.defaultBucket)
            .sourceKey("temp/$s3Key")
            .destinationBucket(bucketName)
            .destinationKey(destKey)
            .acl(ObjectCannedACL.PRIVATE)
            .build()

        return try {
            s3.copyObject(copyObjectRequest)
            true
        } catch (ex: S3Exception) {
            log.error(ex.message, ex)
            false
        }
    }
    fun updateProfilePicture(key: String?, destFolder: String?, oldKey: String? = null): String? {
        if (destFolder == null) {
            return oldKey
        }
        // delete existing file if present
        oldKey?.let { deleteFileByS3Key(it) }
        key?.let { uploadFromTmp(it, destFolder) }
        return destFolder
    }
}
