package com.centuroglobal.shared.data.enums

enum class BlueprintStatus {
    ACTIVE,
    INACTIVE
}

enum class BlueprintActionStatus {
    SAVE,
    PUBLISH,
    DISCARD,
    ACTIVE,
    INACTIVE,

    // UPLOAD PDF
    UPLOAD_PDF_STEP_0,
    UPLOAD_PDF_STEP_1,
    UPLOAD_PDF_STEP_2,
    UPLOAD_PDF_STEP_3,
    UPLOAD_PDF_STEP_4,
    UPLOAD_PDF_STEP_5,
    UPLOAD_PDF_STEP_6,
    UPLOAD_PDF_STEP_7,
    UPLOAD_PDF_STEP_8,
    UPLOAD_PDF_STEP_9,
    UPLOAD_PDF_STEP_10,

    // DELETE PDF
    DELETE_PDF_STEP_0,
    DELETE_PDF_STEP_1,
    DELETE_PDF_STEP_2,
    DELETE_PDF_STEP_3,
    DELETE_PDF_STEP_4,
    DELETE_PDF_STEP_5,
    DELETE_PDF_STEP_6,
    DELETE_PDF_STEP_7,
    DELETE_PDF_STEP_8,
    DELETE_PDF_STEP_9,
    DELETE_PDF_STEP_10,

    // UPDATE Experts
    UPDATE_EXPERT_STEP_0,
    UPDATE_EXPERT_STEP_1,
    UPDATE_EXPERT_STEP_2,
    UPDATE_EXPERT_STEP_3,
    UPDATE_EXPERT_STEP_4,
    UPDATE_EXPERT_STEP_5,
    UPDATE_EXPERT_STEP_6,
    UPDATE_EXPERT_STEP_7,
    UPDATE_EXPERT_STEP_8,
    UPDATE_EXPERT_STEP_9,
    UPDATE_EXPERT_STEP_10,
}