package com.centuroglobal.shared.data.enums

enum class EventStatus {
    PUBLISHED, UNPUBLISHED, ONGOING, CANCELLED, PAST, UPCOMING, DELETED;

    companion object {
        fun getActualStatus(eventStatus: EventStatus): EventStatus {
            return when (eventStatus) {
                ONGOING -> PUBLISHED
                UPCOMING -> PUBLISHED
                PAST -> PUBLISHED
                else -> eventStatus
            }
        }
    }
}

enum class EventSpeakerType {
    INTERNAL, EXTERNAL
}

enum class EventInviteeStatus {
    INVITEE, ATTENDEE
}

enum class EventInviteeType {
    INTERNAL, EXTERNAL
}