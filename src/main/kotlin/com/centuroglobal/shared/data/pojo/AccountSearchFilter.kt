package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus

data class AccountSearchFilter(
    val search: String? = null,
    val status: AccountStatus? = null
) {
    object Builder {
        fun build(search: String?, status: AccountStatus?) =
            AccountSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                status = status
            )
    }
}