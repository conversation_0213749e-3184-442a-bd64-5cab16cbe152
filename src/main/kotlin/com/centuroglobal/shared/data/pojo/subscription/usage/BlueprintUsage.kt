package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.BlueprintUsageEntity
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class BlueprintUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.distinctBy { mapper.readTree(it.additionalData).get("countryCode").textValue() }.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<BlueprintUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val user = corporateUserRepository.findById(it.userId!!).get()
            BlueprintUsageEntity(
                accessedBy = "${user.firstName} ${user.lastName}",
                country = data.get("countryCode").textValue(),
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                userId = it.userId!!,
                bandName = user.band?.name?:"",
                sessionId = it.sessionId
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            BlueprintUsageResponse.ModelMapper.from(
                it,
                corporateUserRepository.findById(it.userId).get()
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            BlueprintUsageResponse.ModelMapper.from(
                it as BlueprintUsageEntity
            )
        }
    }

}