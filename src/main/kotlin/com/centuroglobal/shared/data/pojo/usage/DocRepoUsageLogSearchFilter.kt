package com.centuroglobal.shared.data.pojo.usage

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class DocRepoUsageLogSearchFilter(
    val docType: String?,
    val sizeFrom: Long?,
    val sizeTo: Long?,
    val country: String? = null,
    val corporateId: Long? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val isPartner: Boolean? = null,
    val partnerId: Long? = null
) {
    object Builder {

        fun build(docType: String?, sizeFrom: Long?, sizeTo: Long?,
                  country: String?, corporateId: Long?, from: Long?, to: Long?, isPartner: Boolean?, partnerId: Long?) =
            DocRepoUsageLogSearchFilter(
                docType = if (docType.isNullOrBlank()) null else docType,
                country = if (country.isNullOrBlank()) null else country,
                sizeFrom = sizeFrom,
                sizeTo = sizeTo,
                corporateId = corporateId,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                isPartner = isPartner,
                partnerId = partnerId
            )
    }
}