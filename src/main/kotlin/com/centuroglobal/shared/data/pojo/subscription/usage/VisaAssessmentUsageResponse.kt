package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.VisaAssessmentUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class VisaAssessmentUsageResponse(

    val issueCountry: String,

    val destinationCountry: String,

    val accessedBy: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float

): AbstractUsageResponse() {

    object ModelMapper {


        fun from(usageEntity: VisaAssessmentUsageEntity): VisaAssessmentUsageResponse {

            return VisaAssessmentUsageResponse(
                issueCountry = usageEntity.issueCountry,
                destinationCountry = usageEntity.destinationCountry,
                accessedBy = usageEntity.accessedBy,
                band = usageEntity.bandName,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }
    }
}