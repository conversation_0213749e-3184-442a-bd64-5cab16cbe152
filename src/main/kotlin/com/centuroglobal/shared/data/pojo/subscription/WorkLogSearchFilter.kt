package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.enums.ReferenceType
import java.time.LocalDate
import java.time.Month
import java.time.YearMonth

data class WorkLogSearchFilter(
    val corporateId: Long?,
    val referenceId: Long? = null,
    val referenceType: ReferenceType? = null,
    val from: LocalDate? = null,
    val to: LocalDate? = null,
    val loggedBy: Long? = null

) {
    object Builder {

        fun build(
            corporateId: Long?,
            referenceId: Long?,
            referenceType: ReferenceType?,
            month: Int?,
            year: Int?,
            loggedBy: Long?
        ) =
            WorkLogSearchFilter(
                corporateId = corporateId,
                referenceId = referenceId,
                referenceType = referenceType,
                loggedBy = loggedBy,
                from = if (month == null || year == null) null else YearMonth.of(year, Month.of(month)).atDay(1),
                to = if (month == null || year == null) null else YearMonth.of(year, Month.of(month)).atEndOfMonth()
            )
    }
}