package com.centuroglobal.shared.data.pojo.query

import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class QuerySearchFilter(
    val search: String? = null,
    val accountId: Long? = null,
    val user: Long? = null,
    val responses: String? = null,
    val categories: String? = null,
    val countryCode: String? = null,
    val status: List<QueryStatus>? = null,
    val corporateId: Long?,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    override var partnerId: Long?,
    var cgRequested: Boolean? = null,
    var isPartnerQuery: Boolean? = null,
    override var managedBy: PartnerCaseType? = null


): AbstractSearchFilter() {
    object Builder {

        fun build(
            search: String?,
            accountId: String?,
            user: String?,
            responses: String?,
            categories: String?,
            countryCode: String?,
            status: List<QueryStatus>?,
            corporateId: Long?,
            from: Long?,
            to: Long?,
            partnerId: Long?,
            cgRequested: Boolean?,
            isPartnerQuery: Boolean?
        ) =
            QuerySearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                accountId = if (accountId.isNullOrBlank()) null else accountId.toLong(),
                user = if (user.isNullOrBlank()) null else user.toLong(),
                responses = if (responses.isNullOrBlank()) null else "%${responses}%",
                categories = if(categories.isNullOrBlank()) null else categories,
                countryCode = if(countryCode.isNullOrBlank()) null else countryCode,
                status = status,
                corporateId = corporateId,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                partnerId = partnerId,
                cgRequested = cgRequested,
                isPartnerQuery = isPartnerQuery

            )
    }
}