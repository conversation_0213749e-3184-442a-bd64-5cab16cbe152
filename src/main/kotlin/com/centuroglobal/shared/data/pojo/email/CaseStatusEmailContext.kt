package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class CaseStatusEmailContext(
    val country: String,
    val applicantName: String,
    val statusUpdate: String,
    val caseName: String,
    val caseId: Long?,
    val caseUrl: String,
    val s3ServerUrl: String,
) {
    object ModelMapper {
        fun toContext(userContext: CaseStatusEmailContext): Context {
            val ctx = Context()
            ctx.setVariable("COUNTRY", userContext.country)
            ctx.setVariable("APPLICANT_NAME", userContext.applicantName)
            ctx.setVariable("STATUS_UPDATE", userContext.statusUpdate)
            ctx.setVariable("CASE_CATEGORY", userContext.caseName)
            ctx.setVariable("CASE_ID", userContext.caseId)
            ctx.setVariable("CASE_URL", userContext.caseUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}
