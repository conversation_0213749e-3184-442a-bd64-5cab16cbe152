package com.centuroglobal.shared.data.pojo.referral


import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern

data class ClientReferralSearchCriteria(

    @field:Parameter(
        name = "search",
        description = "ADMIN ONLY - Searches referral title, poster name and company name."
    )
    val search: String? = null,

    @field:Parameter(
        name = "countryCode",
        description = "Country Code"
    )
    @field:Pattern(regexp = "^(|[A-Za-z]{2})$")
    val countryCode: String? = null,

    @field:Parameter(
        name = "expertiseId",
        description = "Expertise Id"
    )
    val expertiseId: String? = null,

    @field:Parameter(
        name = "from",
        description = "Earliest date a referral can be created in milliseconds"
    )
    val from: Long? = null,

    @field:Parameter(
        name = "to",
        description = "Latest date a referral can be created in milliseconds"
    )
    val to: Long? = null,

    @field:Parameter(
        name = "status",
        description = "Referral status. For non-admin, it is defaulted to ACTIVE only. For Admin it is defaulted to ACTIVE or UNRESOLVED or RESOLVED.",
        schema = Schema(allowableValues = ["ACTIVE", "UNRESOLVED", "RESOLVED"])
    )
    @field:Pattern(regexp = "^(|ACTIVE|UNRESOLVED|RESOLVED)$")
    val status: String? = null,

    @field:Parameter(
        name = "pageIndex",
        description = "Pagination page index",
        schema = Schema(allowableValues = ["range[0, infinity]"], defaultValue = "0")
    )
    @field:Min(0)
    val pageIndex: Int = 0,

    @field:Parameter(
        name = "pageSize",
        description = "Pagination page size",
        schema = Schema(allowableValues = ["range[5, infinity]"], defaultValue = "20")
    )
    @field:Min(5)
    val pageSize: Int = 20,

    @field:Parameter(
        name = "sort",
        description = "Sort order",
        schema = Schema(allowableValues = ["ASC", "DESC"], defaultValue = "DESC")
    )
    @field:Pattern(regexp = "^(|ASC|DESC)$")
    val sort: String = "DESC",

    @field:Parameter(
        name = "sortBy",
        description = "ADMIN ONLY - Column to sort by.",
        schema = Schema(allowableValues = ["createdDate", "responseCount"], defaultValue = "createdDate")
    )
    @field:Pattern(regexp = "^(|createdDate|responseCount)$")
    val sortBy: String = "createdDate"
)

