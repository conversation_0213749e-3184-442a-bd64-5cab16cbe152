package com.centuroglobal.shared.data.pojo.task.request

import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.task.dto.TaskAssigneeDto
import com.centuroglobal.shared.data.pojo.task.dto.TaskReminderDto
import jakarta.validation.constraints.NotBlank

data class CreateTaskRequest(

    @field:NotBlank
    val name: String,

    val description: String,

    var referenceId: Long?,

    var referenceType: ReferenceType,

    var visibility: TaskVisibility?,

    val dueDate: Long?,

    val completedDate: Long?,

    var priority: String?,

    var status: TaskStatus?,

    var taskAssignee: List<TaskAssigneeDto>,

    var taskReminder: List<TaskReminderDto>,

    var instruction: String?,

    var usefulLinks: String?
)
