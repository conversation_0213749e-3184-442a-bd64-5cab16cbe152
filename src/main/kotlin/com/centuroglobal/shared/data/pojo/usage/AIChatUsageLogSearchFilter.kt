package com.centuroglobal.shared.data.pojo.usage

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class AIChatUsageLogSearchFilter(
    val question: String? = null,
    val corporateId: Long? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val isPartner: Boolean? = null,
    val partnerId: Long? = null
) {
    object Builder {

        fun build(question: String?, corporateId: Long?, from: Long?, to: Long?, isPartner: Boolean?, partnerId: Long?) =
            AIChatUsageLogSearchFilter(
                question = if (question.isNullOrBlank()) null else "%$question%",
                corporateId = corporateId,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                isPartner = isPartner,
                partnerId = partnerId
            )
    }
}