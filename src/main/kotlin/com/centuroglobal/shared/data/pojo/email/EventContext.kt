package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class EventContext(
    val userName: String,
    val eventTitle: String,
    val eventsUrl: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: EventContext): Context {
            val ctx = Context()
            ctx.setVariable("DISPLAY_NAME", userContext.userName)
            ctx.setVariable("EVENT_NAME", userContext.eventTitle)
            ctx.setVariable("EVENT_URL", userContext.eventsUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}
