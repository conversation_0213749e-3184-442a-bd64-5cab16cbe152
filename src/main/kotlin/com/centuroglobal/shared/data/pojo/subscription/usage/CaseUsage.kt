package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.CaseUsageEntity
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class CaseUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<CaseUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val user = corporateUserRepository.findById(it.userId!!).get()
            val caseCategory = data.get("caseCategory")


            CaseUsageEntity(
                caseType = if (caseCategory == null) "" else caseCategory.textValue(),
                initiatedBy = "${user.firstName} ${user.lastName}",
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                userId = it.userId!!,
                bandName = user.band?.name?:""
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            CaseUsageResponse.ModelMapper.from(it)
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            CaseUsageResponse.ModelMapper.from(
                it as CaseUsageEntity
            )
        }
    }

}