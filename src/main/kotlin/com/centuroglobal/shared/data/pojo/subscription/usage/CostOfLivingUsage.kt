package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.CostOfLivingUsageEntity
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository
import java.time.LocalDateTime

class CostOfLivingUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        val countries = mutableListOf<String>()
        countries.addAll(userActions.filter { mapper.readTree(it.additionalData).get("sourceCountryCode") != null }
            .map { mapper.readTree(it.additionalData).get("sourceCountryCode").textValue() })

        countries.addAll(userActions.filter { mapper.readTree(it.additionalData).get("targetCountryCode") != null }
            .map { mapper.readTree(it.additionalData).get("targetCountryCode").textValue() })

        return countries.filter { it.isNotBlank() }.distinct().size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<CostOfLivingUsageEntity> {

        val costOfLivingUsages = mutableListOf<CostOfLivingUsageEntity>()
        userActions.forEach {
            val data = mapper.readTree(it.additionalData)
            val user = corporateUserRepository.findById(it.userId!!).get()
            val sourceCountry = data.get("sourceCountryCode")
            val targetCountry = data.get("targetCountryCode")

            if (sourceCountry != null && sourceCountry.textValue().isNotBlank()) {
                costOfLivingUsages.add(
                    createCostOfLivingUsageEntity(
                        sourceCountry.textValue(),
                        user,
                        it.startTime,
                        it.userId!!,
                        id
                    )
                )
            }
            if (targetCountry != null && targetCountry.textValue().isNotBlank()) {
                costOfLivingUsages.add(
                    createCostOfLivingUsageEntity(
                        targetCountry.textValue(),
                        user,
                        it.startTime,
                        it.userId!!,
                        id
                    )
                )
            }
        }
        return costOfLivingUsages
    }

    private fun createCostOfLivingUsageEntity(
        countryCode: String,
        user: CorporateUserEntity,
        startTime: LocalDateTime?,
        userId: Long,
        subscriptionUsageDetailsId: Long?
    ): CostOfLivingUsageEntity {
        return CostOfLivingUsageEntity(
            country = countryCode,
            accessedBy = "${user.firstName} ${user.lastName}",
            charges = 0F,
            createdAt = startTime,
            subscriptionUsageDetailsId = subscriptionUsageDetailsId,
            userId = userId,
            bandName = user.band?.name?:"",
            sessionId = null
        )
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            CostOfLivingUsageResponse.ModelMapper.from(
                it,
                corporateUserRepository.findById(it.userId).get()
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
           CostOfLivingUsageResponse.ModelMapper.from(
                it as CostOfLivingUsageEntity
            )
        }
    }

}