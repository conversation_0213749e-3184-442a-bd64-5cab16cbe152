package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.CorporateDocUploadUsageEntity
import com.centuroglobal.shared.data.payload.CorporateDocumentRequest
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository
import com.centuroglobal.shared.util.TimeUtil

class CorporateDocUploadUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<CorporateDocUploadUsageEntity> {

        return userActions.map {
            val user = corporateUserRepository.findById(it.userId!!).get()

           val documentRequest = getPayload(it.additionalData, CorporateDocumentRequest::class)

            CorporateDocUploadUsageEntity(
                name = "${user.firstName} ${user.lastName}",
                country = user.countryCode!!,
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                userId = it.userId!!,
                bandName = user.band?.name?:"",
                documentName = documentRequest.docName,
                documentType = documentRequest.fileType,
                expiry = documentRequest.expiryDate?.let { it1 -> TimeUtil.fromInstantMillis(it1) },
                fileSize = documentRequest.fileSize
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            CorporateDocUploadUsageResponse.ModelMapper.from(it)
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
           CorporateDocUploadUsageResponse.ModelMapper.from(
                it as CorporateDocUploadUsageEntity
            )
        }
    }

}