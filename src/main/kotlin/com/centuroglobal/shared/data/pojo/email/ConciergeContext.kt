package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class ConciergeContext(
    val firstName: String,
    val lastName: String,
    val company: String?,
    val email: String,
    val query: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: ConciergeContext): Context {
            val ctx = Context()
            ctx.setVariable("FIRST_NAME", userContext.firstName)
            ctx.setVariable("LAST_NAME", userContext.lastName)
            ctx.setVariable("COMPANY", userContext.company)
            ctx.setVariable("EMAIL", userContext.email)
            ctx.setVariable("QUERY", userContext.query)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)

            return ctx
        }
    }
}
