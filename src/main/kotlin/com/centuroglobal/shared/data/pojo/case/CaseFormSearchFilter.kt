package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter

data class CaseFormSearchFilter(
    val search: String? = null,
    val country: String? = null,
    val status: CaseFormStatus? = null,
    val category: String? = null,
    var visibility: TaskVisibility? = null,
    val updatedBy: Long? = null,
    override var partnerId: Long?,
    val isPartner: Boolean? = null

): AbstractSearchFilter() {
    object Builder {

        fun build(
            search: String?,
            category: String?,
            country: String?,
            status: CaseFormStatus?,
            visibility: TaskVisibility?,
            updatedBy: Long?,
            partnerId: Long?,
            isPartner: Boolean?
        ) =
            CaseFormSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                category = if(category.isNullOrBlank()) null else category,
                country = if(country.isNullOrBlank()) null else country,
                status = status,
                visibility = visibility,
                updatedBy = updatedBy,
                partnerId = partnerId,
                isPartner = isPartner
            )
    }
}