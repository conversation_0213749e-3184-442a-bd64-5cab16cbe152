package com.centuroglobal.shared.data.pojo.client

import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.pojo.Client
import com.centuroglobal.shared.data.pojo.PagedResult
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page

data class ClientListing constructor(

    @Schema()
    val data: PagedResult<Client>,

    @Schema()
    val stats: ClientStats?

) {
    object ModelMapper {
        fun from(pagedClientView: Page<ClientView>, stats: ClientStats?) =
            ClientListing(
                data = PagedResult.ModelMapper.from(pagedClientView, Client.ModelMapper::from),
                stats = stats
            )
    }
}
