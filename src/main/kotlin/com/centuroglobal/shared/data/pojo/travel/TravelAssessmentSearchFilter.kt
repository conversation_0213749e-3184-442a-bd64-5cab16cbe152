package com.centuroglobal.shared.data.pojo.travel

import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class TravelAssessmentSearchFilter(
    val originCountry: String? = null,
    var destinationCountry: String? = null,
    val fromDate: LocalDateTime? = null,
    val toDate: LocalDateTime? = null,
    val assessmentType: String? = null,
    val currentUser: Long? = null,
    val applicant: Long? = null,
    val corporate: Long? = null,
    val roles: List<String>? = null,
    val purpose: String? = null

) : AbstractSearchFilter() {
    object Builder {

        fun build(
            originCountry: String?,
            destinationCountry: String?,
            fromDate: Long?,
            toDate: Long?,
            assessmentType: String?,
            currentUser: Long?,
            applicant: Long?,
            corporate: Long?,
            role: Role?,
            purpose: List<String>?
        ) =
            TravelAssessmentSearchFilter(
                originCountry = if (originCountry.isNullOrBlank()) null else originCountry,
                destinationCountry = if(destinationCountry.isNullOrBlank()) null else destinationCountry,
                fromDate = fromDate?.let { TimeUtil.fromInstantMillis(it) },
                toDate = toDate?.let { TimeUtil.fromInstantMillis(it) },
                assessmentType = assessmentType,
                currentUser = currentUser,
                applicant = applicant,
                corporate = corporate,
                roles = getAllowedRoles(assessmentType, role),
                purpose = if (purpose.isNullOrEmpty()) null else "%${purpose.joinToString(",")}%"
            )

        private fun getAllowedRoles(assessmentType: String?, role: Role?): List<String>? {
            var roles: List<String>? = null
            if (assessmentType!=null && assessmentType == "TEAM_MEMBER") {
                roles = if (role == Role.ROLE_SUPER_ADMIN) {
                    listOf(role.name, Role.ROLE_ADMIN.name)
                } else {
                    listOf(role!!.name)
                }
            }
            return roles
        }
    }
}