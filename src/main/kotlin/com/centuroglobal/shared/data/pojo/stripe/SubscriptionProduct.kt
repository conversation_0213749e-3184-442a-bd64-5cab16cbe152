package com.centuroglobal.shared.data.pojo.stripe

import com.stripe.model.Price
import com.stripe.model.Product
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.util.*

data class SubscriptionProduct(

    @Schema(description =  "Product id.")
    val productId: String,

    @Schema(description =  "Product name.")
    val productName: String,

    @Schema(description =  "Price id.")
    val priceId: String,

    @Schema()
    val amount: BigDecimal,

    @Schema()
    val currencyCode: String,

    @Schema()
    val recurringInterval: SubscriptionInterval
) {
    object ModelMapper {
        fun from(stripePrice: Price, stripeProduct: Product) =
            SubscriptionProduct(
                priceId = stripePrice.id,
                amount = stripePrice.unitAmountDecimal.divide(BigDecimal(100)),
                currencyCode = stripePrice.currency.uppercase(Locale.getDefault()),
                productId = stripePrice.product,
                productName = stripeProduct.name,
                recurringInterval = SubscriptionInterval(
                    stripePrice.recurring!!.interval,
                    stripePrice.recurring!!.intervalCount
                )
            )
    }
}

data class SubscriptionInterval(
    @Schema(
        description = "Interval type.",
        allowableValues = ["day", "week", "month", "year"]
    )
    val type: String,

    @Schema(
        description = "Interval count. If type is month and count is 2, this means that the subscription cycle is every 2 months."
    )
    val count: Long
)