package com.centuroglobal.shared.data.pojo.lead

import com.centuroglobal.shared.data.entity.LeadEntity
import com.centuroglobal.shared.data.entity.view.ExpertiseView
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.CountryRegion
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class LeadDetail(

    val id: String?,

    @Schema()
    val title: String,

    @Schema()
    val description: String,

    @Schema()
    val expertiseId: List<Int>,

    @Schema()
    val expertiseName: List<String>,

    @Schema()
    val countryCode: String,

    @Schema()
    val countryName: String,

    @Schema()
    val regionId: Int?,

    @Schema()
    val regionName: String?,

    @Schema(
        allowableValues = ["ACTIVE", "RESOLVED", "UNRESOLVED", "DELETED"]
    )
    val status: LeadStatus,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long,

    @Schema()
    val responses: List<LeadResponse>,

    @Schema()
    val creator: Creator? = null

) {
    object ModelMapper {
        fun fromOwnLead(
            entity: LeadEntity,
            expertises: List<ExpertiseView>,
            country: Country,
            region: CountryRegion?,
            retrieveExpertSummaryFunction: (m: Long) -> ExpertProfileSummary,
            creator: Creator? = null
        ) =
            LeadDetail(
                id = "${entity.leadType}-${entity.leadTypeId}-${entity.id}",
                title = entity.title,
                description = entity.description,
                expertiseId = expertises.map { it.id },
                expertiseName = expertises.map { it.name },
                countryCode = country.code,
                countryName = country.name,
                regionId = region?.regionId,
                regionName = region?.name,
                status = entity.status,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!),
                responses = entity.responses.map {
                    LeadResponse.ModelMapper.fromWithExpertProfile(
                        it,
                        retrieveExpertSummaryFunction
                    )
                },
                creator = creator
            )

        fun fromLeadForExpert(
            entity: LeadEntity,
            expertises: List<ExpertiseView>,
            country: Country,
            region: CountryRegion?,
            expertId: Long,
            creator: Creator? = null
        ) =
            LeadDetail(
                id = "${entity.leadType}-${entity.leadTypeId}-${entity.id}",
                title = entity.title,
                description = entity.description,
                expertiseId = expertises.map { it.id },
                expertiseName = expertises.map { it.name },
                countryCode = country.code,
                countryName = country.name,
                regionId = region?.regionId,
                regionName = region?.name,
                status = entity.status,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!),
                responses = entity.responses
                    .filter { it.expertId == expertId }
                    .map { LeadResponse.ModelMapper.from(it) },
                creator = creator
            )
    }
}

data class Creator(
    val userId: Long,
    val displayName: String,
    val profilePictureFullUrl: String?,
    val jobTitle: String,
    val companyName: String,
    val countryName: String,
    val regionName: String? = null
)