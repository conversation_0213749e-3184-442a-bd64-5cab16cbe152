package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus

data class PartnerUserSearchFilter(
    val search: String? = null,
    val country: String? = null,
    val corporate: Long? =null,
    val status: AccountStatus? = null

) {
    object Builder {
        fun build(search: String?,country: String?,corporate: Long?, status: AccountStatus?) =
            PartnerUserSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                country = if (country.isNullOrBlank()) null else country,
                corporate= corporate,
                status = status
            )
    }
}