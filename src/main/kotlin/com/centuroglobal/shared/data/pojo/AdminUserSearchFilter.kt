package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role

data class AdminUserSearchFilter(
    val search: String? = null,
    val role: Role? = null,
    val status: AccountStatus? = null,
    val responsibility: String? = null
) {
    object Builder {
        fun build(search: String?, role: String?, status: String?, responsibility: String?) =
            AdminUserSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                role = if (role.isNullOrBlank()) null else Role.valueOf(role),
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                responsibility = if (responsibility.isNullOrBlank()) null else responsibility
            )
    }
}
