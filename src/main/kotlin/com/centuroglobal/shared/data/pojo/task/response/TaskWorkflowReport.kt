package com.centuroglobal.shared.data.pojo.task.response

import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.util.TimeUtil

data class TaskWorkflowReport(
    val id: Long?,
    val displayOrder: Long?,
    val taskName: String,
    val assignedTo: String?,
    val assignedToRole: String,
    val plannedStartDate: Long?,
    val expectedTimeline: Long,
    val plannedDueDate: Long?,
    val expectedDueDate: Long?,
    val actualStartDate: Long?,
    val actualCompletionDate: Long?,
    val taskStatus: TaskStatus?,
    val updatedBy: String?,
    val deviation: Long?
) {

    object ModelMapper {

        fun from(entity: TaskTemplateEntity, updatedBy: String?, deviation: Long?): TaskWorkflowReport {

            val assignee = entity.task!!.assignee?.firstOrNull()?.assigneeId

            return TaskWorkflowReport(
                id = entity.id,
                displayOrder = entity.displayOrder,
                taskName = entity.task!!.name,
                assignedTo = "${assignee?.firstName} ${assignee?.lastName}",
                assignedToRole = entity.assignedTo,
                plannedStartDate = entity.task!!.plannedStartDate?.let { TimeUtil.toEpochMillis(it) },
                expectedTimeline = entity.expectedTimeline,
                plannedDueDate = entity.task!!.dueDate?.let { TimeUtil.toEpochMillis(it) },
                expectedDueDate = entity.task!!.expectedDueDate?.let { TimeUtil.toEpochMillis(it) },
                actualStartDate = entity.task!!.startDate?.let { TimeUtil.toEpochMillis(it) },
                actualCompletionDate = entity.task!!.completedDate?.let { TimeUtil.toEpochMillis(it) },
                taskStatus = entity.task!!.status,
                updatedBy = if (entity.task!!.status != TaskStatus.NOT_STARTED) updatedBy else null,
                deviation = deviation
            )
        }
    }
}
