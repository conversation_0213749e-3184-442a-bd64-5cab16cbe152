package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.subscription.usage.CaseUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class CaseUsageResponse(

    val caseType: String,

    val initiatedBy: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float

): AbstractUsageResponse() {

    object ModelMapper {

        fun from(usageEntity: CaseUsageEntity, createdBy: CorporateUserEntity): CaseUsageResponse {

            return CaseUsageResponse(
                caseType = usageEntity.caseType,
                initiatedBy = "${createdBy.firstName} ${createdBy.lastName}",
                band = createdBy.band?.name,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }

        fun from(usageEntity: CaseUsageEntity): CaseUsageResponse {

            return CaseUsageResponse(
                caseType = usageEntity.caseType,
                initiatedBy = usageEntity.initiatedBy,
                band = usageEntity.bandName,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }
    }
}