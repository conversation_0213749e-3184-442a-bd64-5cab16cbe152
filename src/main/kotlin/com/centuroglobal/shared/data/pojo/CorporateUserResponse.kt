package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateUserResponse(
    val email: String,
    val firstName: String,
    val lastName: String,
    val jobTitle: String,
    val corporateId: Long,
    val keepMeInformed: Boolean = false,
    val bandId: Long,
    val accounts: List<Long>,
    val managerUserIds: List<Long>?,
    val countryCode: String,
    val profilePhotoUrl: String? = null,
    val corporateName: String,
    val isPrimary: Boolean = false,
    val dialCode: String? = null,
    val contactNo: String? = null,
    val notificationSettings: List<NotificationSettingResponse>,
    val corporateCountryCode: String,
    val educationQualification: String?,
    val salary: String?,
    val relevantExperience: String?
) {
    object ModelMapper {
        fun from(corporateUserEntity: CorporateUserEntity, profilePhotoUrl: String?) =
            CorporateUserResponse(
                email = corporateUserEntity.email,
                firstName = corporateUserEntity.firstName,
                lastName = corporateUserEntity.lastName,
                jobTitle = corporateUserEntity.jobTitle,
                corporateId = corporateUserEntity.corporate.id!!,
                keepMeInformed = corporateUserEntity.keepMeInformed,
                bandId = corporateUserEntity.band!!.id!!,
                accounts = corporateUserEntity.accounts!!.map { it.id!! },
                managerUserIds = corporateUserEntity.managers.map{ it.managerId},
                countryCode = corporateUserEntity.countryCode!!,
                profilePhotoUrl = profilePhotoUrl,
                corporateName = corporateUserEntity.corporate.name,
                isPrimary = corporateUserEntity.userType == "PRIMARY",
                dialCode = corporateUserEntity.dialCode,
                contactNo = corporateUserEntity.contactNo,
                notificationSettings = corporateUserEntity.notificationPreferences.map { NotificationSettingResponse(it.notificationKey.name, it.value) },
                corporateCountryCode = corporateUserEntity.corporate.countryCode,
                educationQualification = corporateUserEntity.educationQualification,
                salary = corporateUserEntity.salary,
                relevantExperience = corporateUserEntity.relevantExperience
            )
    }
}

data class NotificationSettingResponse(
    val key: String,
    val value: Boolean
)