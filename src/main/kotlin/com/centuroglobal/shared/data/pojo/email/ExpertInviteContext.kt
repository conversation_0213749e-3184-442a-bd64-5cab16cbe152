package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class ExpertInviteContext(
    val displayName: String,
    val verifyUrl: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: ExpertInviteContext): Context {
            val ctx = Context()
            ctx.setVariable("DISPLAY_NAME", userContext.displayName)
            ctx.setVariable("VERIFY_URL", userContext.verifyUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)

            return ctx
        }
    }
}