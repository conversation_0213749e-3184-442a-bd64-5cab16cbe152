package com.centuroglobal.shared.data.pojo

import org.springframework.data.domain.Page

data class PagedResult<T>(
    val rows: List<T>,
    val totalElements: Long,
    val currentPage: Int,
    val totalPages: Int

) {
    object ModelMapper {
        fun <T, R> from(pageResult: Page<R>, fn: (m: R) -> T): PagedResult<T> =
            PagedResult(
                rows = pageResult.map(fn).toList(),
                totalElements = pageResult.totalElements,
                currentPage = pageResult.number,
                totalPages = pageResult.totalPages
            )

        fun <T, R> from(pageResult: Page<R>, list: List<T>): PagedResult<T> =
            PagedResult(
                rows = list,
                totalElements = pageResult.totalElements,
                currentPage = pageResult.number,
                totalPages = pageResult.totalPages
            )
    }
}
