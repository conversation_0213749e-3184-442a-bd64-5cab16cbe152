package com.centuroglobal.shared.data.pojo.circle

import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType

data class CreateUpdateCircleRequest(
    val name: String,
    val about: String,
    val circleAccessType: CircleType,
    val status: CircleStatus,
    val countryCode: List<String>?,
    val expertiseIds: List<Int>?,
    val members: List<Long>?
)
