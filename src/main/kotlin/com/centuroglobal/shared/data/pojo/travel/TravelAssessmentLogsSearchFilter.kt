package com.centuroglobal.shared.data.pojo.travel

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class TravelAssessmentLogsSearchFilter(
    val destination: String? = null,
    val fromDate: LocalDateTime? = null,
    val toDate: LocalDateTime? = null,
    val corporateId: Long? = null,
    val isPartner: Boolean = false,
    val visaType: String?,
    val partner: Long?

) {
    object Builder {

        fun build(
            country: String?,
            from: Long?,
            to: Long?,
            corporateId: Long?,
            isPartner: Boolean,
            visaType: String?,
            partner: Long?
        ) =
            TravelAssessmentLogsSearchFilter(
                destination = if(country.isNullOrBlank()) null else country,
                fromDate = from?.let { TimeUtil.fromInstantMillis(it) },
                toDate = to?.let { TimeUtil.fromInstantMillis(it) },
                corporateId = corporateId,
                isPartner = isPartner,
                visaType = if (visaType.isNullOrBlank()) null else visaType,
                partner = partner
            )
    }
}