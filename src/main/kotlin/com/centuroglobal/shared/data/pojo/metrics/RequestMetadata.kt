package com.centuroglobal.shared.data.pojo.metrics

import com.centuroglobal.shared.data.entity.AccessLogEntity
import com.centuroglobal.shared.data.enums.GrantType
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import org.springframework.web.method.HandlerMethod
import java.util.*
import jakarta.servlet.http.HttpServletRequest

private const val X_XID = "x-xid"
private const val X_CLIENT_ID = "x-client-id"
private const val X_USER_ID = "x-user-id"
private const val X_FWD_FOR = "X-Forwarded-For"
private const val X_REAL_IP = "X-Real-IP"
private const val USER_AGENT = "user-agent"

data class RequestMetadata(
    val method: String,
    val uri: String,
    val controller: String,
    val action: String,
    val xid: String,
    var userId: Long?,
    val userAgent: String,
    val clientId: String,
    val clientIp: String,
    val receivedAt: Long,
    var completedAt: Long? = null,
    var responseStatus: Int? = null
) {
    fun getResponseTime(): Int? {
        return if (completedAt != null) {
            (completedAt!! - receivedAt).toInt()
        } else {
            null
        }
    }

    object ModelMapper {
        fun toAccessLogEntity(req: RequestMetadata) =
            AccessLogEntity(
                method = req.method,
                uri = req.uri,
                controller = req.controller,
                action = req.action,
                xid = req.xid,
                userId = req.userId,
                userAgent = req.userAgent,
                clientId = req.clientId,
                clientIp = req.clientIp,
                receivedAt = TimeUtil.fromInstantMillis(req.receivedAt),
                completedAt = if (req.completedAt != null) TimeUtil.fromInstantMillis(req.completedAt!!) else null,
                responseTime = req.getResponseTime(),
                responseStatus = req.responseStatus
            )

        fun fromRequest(request: HttpServletRequest, handler: HandlerMethod, authenticatedUser: AuthenticatedUser?) =
            RequestMetadata(
                request.method,
                extractURI(request),
                handler.beanType.simpleName ?: "",
                handler.method.name ?: "",
                extractXid(request),
                extractUserId(request, authenticatedUser),
                extractUserAgent(request),
                extractClientId(request),
                extractClientIP(request),
                System.currentTimeMillis()
            )

        fun fromAuthRequest(request: HttpServletRequest, grantType: GrantType?) =
            RequestMetadata(
                request.method,
                extractURI(request),
                "AuthenticationController",
                grantType?.action ?: "",
                extractXid(request),
                extractUserId(request),
                extractUserAgent(request),
                extractClientId(request),
                extractClientIP(request),
                System.currentTimeMillis()
            )

        fun fromAuthRequest(metadata: RequestMetadata, grantType: GrantType?) =
            RequestMetadata(
                metadata.method,
                metadata.uri,
                metadata.controller,
                grantType?.action ?: "",
                metadata.xid,
                metadata.userId,
                metadata.userAgent,
                metadata.clientId,
                metadata.clientIp,
                metadata.receivedAt
            )

        fun fromJwtAuthRequest(request: HttpServletRequest) =
            RequestMetadata(
                request.method,
                extractURI(request),
                "",
                "",
                extractXid(request),
                extractUserId(request),
                extractUserAgent(request),
                extractClientId(request),
                extractClientIP(request),
                System.currentTimeMillis()
            )

        private fun extractUserId(request: HttpServletRequest, authenticatedUser: AuthenticatedUser? = null): Long? {
            return authenticatedUser?.userId ?: try {
                request.getHeader(X_USER_ID)?.toLong()
            } catch (e: Exception) {
                null
            }
        }

        private fun extractClientId(request: HttpServletRequest): String {
            return request.getHeader(X_CLIENT_ID) ?: ""
        }

        private fun extractUserAgent(request: HttpServletRequest): String {
            return request.getHeader(USER_AGENT) ?: ""
        }

        private fun extractXid(request: HttpServletRequest): String {
            return request.getHeader(X_XID) ?: UUID.randomUUID().toString()
        }

        private fun extractURI(request: HttpServletRequest): String {
            return request.requestURI ?: ""
        }

        private fun extractClientIP(request: HttpServletRequest): String {
            var clientIP: String = request.getHeader(X_REAL_IP) ?: (request.getHeader(X_FWD_FOR) ?: "")
            clientIP = if (clientIP.isBlank()) {
                request.remoteAddr
            } else {
                clientIP.split(",")[0]
            }
            return clientIP
        }
    }
}