package com.centuroglobal.shared.data.pojo.task.request

import jakarta.validation.constraints.NotBlank

data class CreateTaskTemplateRequest(

    @field:NotBlank
    val taskName: String,

    val description: String?,

    val expectedTimeline: Long,

    var expectedTimelineUnit: String? = "DAYS", 

    var priority: String? = null,

    var visibility: String?,

    val instruction: String?,

    val assignedTo: String,

    val caseStatus: String,

    val caseMilestone: String,

    val usefulLinks: String?,

    val progress: String,

    var displayOrder: Long? = null
)
