package com.centuroglobal.shared.data.pojo.subscription

data class SubscriptionPlansRequest(

    val name: String,

    val currency: String,

    val price: Float,

    var isActive: Boolean = true,

    val modules: List<SubscriptionDetailsRequest>
)

data class SubscriptionDetailsRequest (

    val name: String,

    val code: String,

    val threshold: Long?,

    val unit: String,

    val overageRate: Float = 0F,

    val trackingDuration: String,

    val isUnlimited: Boolean = false
)