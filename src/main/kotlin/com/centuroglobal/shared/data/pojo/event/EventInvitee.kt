package com.centuroglobal.shared.data.pojo.event

import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.EventSpeakerType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType

data class EventInvitee(
    var type: EventSpeakerType,
    var internalMemberRole: Role?,
    var internalMemberId: Long?,
    var isHost: <PERSON>olean,
    var profile: ClientView?
) {
    object ModelMapper {
        fun from(clientView: ClientView): EventInvitee {
            return EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                profile = clientView,
                internalMemberId = clientView.userId,
                internalMemberRole = when (clientView.userType) {
                    UserType.EXPERT -> Role.ROLE_EXPERT
                    UserType.CORPORATE -> Role.ROLE_CORPORATE
                    UserType.BACKOFFICE -> Role.ROLE_ADMIN
                    UserType.PARTNER -> Role.ROLE_PARTNER
                },
                isHost = false
            )
        }
    }
}
