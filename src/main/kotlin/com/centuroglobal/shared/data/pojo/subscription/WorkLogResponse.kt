package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.entity.subscription.WorkLogEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil

data class WorkLogResponse(

    val id: Long?,

    val corporateId: Long,

    val corporateName: String,

    val referenceType: ReferenceType,

    val referenceId: Long?,

    val eventDate: Long,

    val timeSpent: Long,

    val description: String,

    val loggedBy: UserProfile,

    val isDeleted: Boolean,

    val isDeleteAllowed: Boolean
) {
    object ModelMapper {
        fun from(entity: WorkLogEntity, createdBy: UserProfile, isDeleteAllowed: Boolean): WorkLogResponse {

            return WorkLogResponse(
                id = entity.id,
                corporateId = entity.corporate.id!!,
                corporateName = entity.corporate.name,
                referenceType = entity.referenceType,
                referenceId = entity.referenceId,
                eventDate = TimeUtil.toEpochMillis(entity.eventDate),
                timeSpent = entity.timeSpent,
                description = entity.description,
                isDeleted = entity.isDeleted,
                isDeleteAllowed = isDeleteAllowed,
                loggedBy = createdBy
            )
        }
    }
}
