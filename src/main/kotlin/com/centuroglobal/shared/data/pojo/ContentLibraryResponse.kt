package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ContentLibraryEntity
import com.centuroglobal.shared.util.TimeUtil

data class ContentLibraryResponse (

    val id: Long?,

    val countryCode: String,

    val identifier: String,

    val title: String,

    val data: String,

    val updatedBy: String,

    val updatedOn: Long,

    val metadata: List<ContentLibraryMetadata>
){
    object ModelMapper {
        fun from(entity: ContentLibraryEntity, updatedBy: String): ContentLibraryResponse {
            return ContentLibraryResponse(
                entity.id,
                entity.countryCode,
                entity.identifier,
                entity.title,
                entity.data,
                updatedBy,
                TimeUtil.toEpochMillis(entity.lastUpdatedDate),
                entity.metadata.map { ContentLibraryMetadata(it.mKey, it.mValue, it.label) }
            )
        }
    }
}
