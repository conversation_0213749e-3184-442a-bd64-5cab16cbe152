package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.enums.PartnerCaseType


data class CaseSearchFilter(
    val caseId: Long? = null,
    val status: String? = null,
    val category: String? = null,
    val country: String? = null,
    val companyName: String? = null,
    val search: String?=null,
    val actionFor: List<String>?=null,
    var archive: Boolean = false,
    var isPriorityCase: Boolean? =null,
    var accountManagerId: Long?=null,
    var caseOwner: Long?=null,
    var minPercent: Int? = null,
    var maxPercent: Int? = null,
    var accountId: Long? = null,
    var corporateId: Long? = null,
    var partnerId: Long?= null,
    var cgRequested: Boolean? = null,
    var isPartnerCases: Boolean? = null,
    var managedBy: PartnerCaseType? = null
) {
    object Builder {
        fun build(
            caseId: String?,
            status: String?,
            category: String?,
            country: String?,
            companyName: String?,
            search: String?,
            actionFor: String?,
            archive: Boolean,
            isPriorityCase: Boolean?,
            accountManagerId: String?,
            caseOwner: String?,
            minPercent: Int? = null,
            maxPercent: Int? = null,
            accountId: Long? = null,
            corporateId: String? = null,
            partnerId: Long?=null,
            cgRequested: Boolean?,
            isPartnerCases: Boolean?
        ) =
            CaseSearchFilter(
                caseId = if (caseId.isNullOrBlank()) null else caseId.toLong(),
                status = if (status.isNullOrBlank()) null else status,
                category = if (category.isNullOrBlank()) null else category,
                country = if (country.isNullOrBlank()) null else country,
                companyName = if (companyName.isNullOrBlank()) null else "%$companyName%",
                search = if (search.isNullOrBlank()) null else "%$search%",
                actionFor = if(actionFor.isNullOrEmpty()) null else actionFor.split(","),
                archive = archive,
                isPriorityCase = isPriorityCase,
                accountManagerId = if(accountManagerId.isNullOrBlank()) null else accountManagerId.toLong(),
                caseOwner = if(caseOwner.isNullOrBlank()) null else caseOwner.toLong(),
                accountId = accountId,
                minPercent = minPercent,
                maxPercent = maxPercent,
                corporateId = if(corporateId.isNullOrBlank()) null else corporateId.toLong(),
                partnerId = partnerId,
                isPartnerCases = isPartnerCases,
                cgRequested = cgRequested
            )
    }
}