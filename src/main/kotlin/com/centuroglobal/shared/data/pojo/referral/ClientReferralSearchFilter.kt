package com.centuroglobal.shared.data.pojo.referral

import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class ClientReferralSearchFilter(
    val search: String? = null,
    val countryCode: String? = null,
    val expertiseId: Int? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val status: LeadStatus? = null
) {
    object Builder {
        fun build(searchCriteria: ClientReferralSearchCriteria) =
            ClientReferralSearchFilter(
                search = if (searchCriteria.search.isNullOrBlank()) null else "%${searchCriteria.search}%",
                countryCode = if (searchCriteria.countryCode.isNullOrBlank()) null else searchCriteria.countryCode,
                expertiseId = if (searchCriteria.expertiseId.isNullOrBlank()) null else searchCriteria.expertiseId.toInt(),
                from = if (searchCriteria.from != null) TimeUtil.fromInstantMillis(searchCriteria.from) else null,
                to = if (searchCriteria.to != null) TimeUtil.fromInstantMillis(searchCriteria.to) else null,
                status = if (searchCriteria.status.isNullOrBlank()) null else LeadStatus.valueOf(searchCriteria.status)
            )
    }
}
