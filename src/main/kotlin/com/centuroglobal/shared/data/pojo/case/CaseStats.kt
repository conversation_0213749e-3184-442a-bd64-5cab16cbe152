package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import io.swagger.v3.oas.annotations.media.Schema

data class CaseStats constructor(

    @Schema()
    val total: Long,

    @Schema()
    val underReview: Long,
    @Schema()
    val inProgress: Long,
    val notStarted: Long,
    val cancelled: Long,
    val caseComplete: Long,
    val awaitingInformation:Long,
    val verifyingDocuments:Long,
    val submittingApplication:Long,
    val awaitingGovtResponse:Long,
    val finalisingCase:Long,

) {
    object ModelMapper {
        fun from(entity: Unit): Nothing = throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
    }
}
