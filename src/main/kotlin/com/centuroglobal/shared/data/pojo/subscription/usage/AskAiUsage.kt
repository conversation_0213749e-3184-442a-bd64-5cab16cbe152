package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.AskAiUsageEntity
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class AskAiUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<AskAiUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val user = corporateUserRepository.findById(it.userId!!).get()
            AskAiUsageEntity(
                name = "${user.firstName} ${user.lastName}",
                country = user.countryCode!!,
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                userId = it.userId!!,
                bandName = user.band?.name?:""
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            AskAiUsageResponse.ModelMapper.from(
                it,
                corporateUserRepository.findById(it.userId).get()
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            AskAiUsageResponse.ModelMapper.from(
                it as AskAiUsageEntity
            )
        }
    }

}