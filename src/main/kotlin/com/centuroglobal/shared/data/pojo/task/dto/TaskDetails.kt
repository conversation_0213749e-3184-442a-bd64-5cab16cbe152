package com.centuroglobal.shared.data.pojo.task.dto

import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.UserProfile
import java.time.LocalDateTime

data class TaskDetails(

    val id: Long,

    val name: String,

    val description: String,

    var createdBy: UserProfile?,

    var referenceType: ReferenceType,

    var referenceId: Long?,

    var visibility: TaskVisibility?,

    val dueDate: Long?,

    val overDueDays: Long?,

    val completedDate: Long?,

    var priority: String?,

    var status: TaskStatus?,

    var category: List<String>,

    val assignee: MutableList<TaskAssigneeDto>?,

    val createdAt: Long,

    val reminders: List<TaskReminderDto>?,

    val usefulLinks: String?,

    val instruction: String?,

    val progress: String?,

    val caseMilestone: String?,

    val plannedStartDate: Long?,

    val startDate: Long?,

    val expectedDueDate: Long?,

    val isActive: Boolean = false
    )
