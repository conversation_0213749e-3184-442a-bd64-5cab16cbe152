package com.centuroglobal.shared.data.pojo.lead

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.exception.ApplicationException

data class LeadInfo(
    val leadType: LeadType,
    val leadTypeId: Long,
    val leadId: Long
) {
    object Mapper {
        fun from(leadId: String): LeadInfo {
            val leadSplit = leadId.split("-")
            if (leadSplit.size != 3) throw ApplicationException(ErrorCode.LEAD_INVALID_ID)
            return LeadInfo(
                leadType = LeadType.valueOf(leadSplit[0]),
                leadTypeId = leadSplit[1].toLong(),
                leadId = leadSplit[2].toLong()
            )
        }
    }
}
