package com.centuroglobal.shared.data.pojo.blueprint

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import io.swagger.v3.oas.annotations.media.Schema

data class BluePrintCountryStats constructor(

    @Schema()
    val countries: Int,

    @Schema()
    val active: Int,

    @Schema()
    val inactive: Int

) {
    object ModelMapper {
        fun from(entity: Unit): Nothing = throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
    }
}
