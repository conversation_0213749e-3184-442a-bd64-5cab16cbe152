package com.centuroglobal.shared.data.pojo.usage

import com.centuroglobal.shared.data.entity.playbook.PlaybookSessionEntity
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.util.TimeUtil
import java.time.temporal.ChronoUnit

data class PlaybookUsageLogResponse(

    val country: String?,

    val industry: String?,

    val corporateName: String?,

    val accessedBy: CorporateUserProfile,

    val accessedOn: Long,

    val purpose: String?,

    val aiQuestions: Long = 0,

    val timeSpent: Long?
) {
    object ModelMapper {

        fun from(entity: PlaybookSessionEntity, accessedBy: CorporateUserProfile): PlaybookUsageLogResponse {
            return PlaybookUsageLogResponse(
                country = entity.playbook?.country,
                industry = entity.playbook?.industry,
                corporateName = entity.corporateUser?.corporate?.name,
                accessedBy = accessedBy,
                accessedOn = TimeUtil.toEpochMillis(entity.createdDate),
                purpose = entity.type,
                aiQuestions = entity.playbookChats.size.toLong(),
                timeSpent = entity.endTime?.let { ChronoUnit.SECONDS.between(entity.startTime, entity.endTime) } ?: 0,
            )
        }
    }
}