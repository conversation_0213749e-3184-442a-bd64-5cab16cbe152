package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class CaseFeesApprovalReminderEmailContext(
    val caseId: Long?,
    val requestedDate: String,
    val caseUrl: String,
    val publicUrl: String,
    val s3ServerUrl: String,
) {
    object ModelMapper {
        fun toContext(userContext:CaseFeesApprovalReminderEmailContext ): Context {
            val ctx = Context()
            ctx.setVariable("CASE_ID", userContext.caseId)
            ctx.setVariable("REQUESTED_DATE", userContext.requestedDate)
            ctx.setVariable("CASE_URL", userContext.caseUrl)
            ctx.setVariable("PUBLIC_URL", userContext.publicUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}