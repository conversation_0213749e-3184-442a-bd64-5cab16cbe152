package com.centuroglobal.shared.data.pojo.referral

import com.centuroglobal.shared.data.entity.ClientReferralEntity
import com.centuroglobal.shared.data.enums.LeadStatus

data class ClientReferralDetails(
    var id: Long?,

    var title: String,

    var clientName: String,

    var clientCountryCode: String,

    var clientEmail: String?,

    var clientCompanyName: String?,

    var clientContactNumber: String?,

    var description: String,

    var dealCurrency: String?,

    var dealValue: String?,

    var createdBy: String? = null,

    var status: LeadStatus,

    var experts: List<String> = mutableListOf(),

    var readBy: List<String> = mutableListOf()
) {
    object ModelMapper {
        fun from(clientReferralEntity: ClientReferralEntity): ClientReferralDetails {
            return ClientReferralDetails(
                id = clientReferralEntity.id,
                title = clientReferralEntity.title,
                clientName = clientReferralEntity.clientName,
                clientCountryCode = clientReferralEntity.clientCountryCode,
                clientEmail = clientReferralEntity.clientEmail,
                clientCompanyName = clientReferralEntity.clientCompanyName,
                clientContactNumber = clientReferralEntity.clientContactNumber,
                description = clientReferralEntity.description,
                dealCurrency = clientReferralEntity.dealCurrency,
                dealValue = clientReferralEntity.dealValue,
                status = clientReferralEntity.status
            )
        }
    }
}
