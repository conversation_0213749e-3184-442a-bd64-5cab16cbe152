package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
@JsonIgnoreProperties(ignoreUnknown = true)
data class ExpertUserDetails(

    val id: Long?,
    @Schema()
    val email: String,
    @Schema()
    val name: String,

    @Schema()
    val status: String,

    @Schema()
    val createdDate: Long,

    @Schema()
    val joinedDate: Long?,

    @Schema()
    val lastLogonDate: Long?,

    @Schema()
    val companyName: String,

    @Schema()
    val countryCode: String,

    @Schema()
    val expertType: String?,

    val associations: List<String>? = null


) {
    object ModelMapper {
        fun from(expertUserEntity: ExpertUserEntity): ExpertUserDetails {
            return ExpertUserDetails(
                id = expertUserEntity.id,
                email = expertUserEntity.email,
                name = "${expertUserEntity.firstName} ${expertUserEntity.lastName}",
                status = expertUserEntity.status.toString(),
                createdDate = TimeUtil.toEpochMillis(expertUserEntity.createdDate),
                lastLogonDate = expertUserEntity.lastLoginDate?.let { TimeUtil.toEpochMillis(it) },
                companyName = expertUserEntity.companyProfile!!.name,
                countryCode = expertUserEntity.countryCode!!,
                expertType = expertUserEntity.expertType,
                joinedDate = expertUserEntity.joinedDate?.let { TimeUtil.toEpochMillis(it) },
                associations = expertUserEntity.companyProfile!!.associatedPartners.map { it.name }
            )
        }
    }
}

