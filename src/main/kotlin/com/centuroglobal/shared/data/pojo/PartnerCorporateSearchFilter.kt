package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class PartnerCorporateSearchFilter(
    val name: String? = null,
    val country: String? = null,
    val accountId: Long? =null,
    val status: CorporateStatus? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val isPartnerCompany: Boolean? = null

) {
    object Builder {
        fun build(name: String?,country: String?, accountId: Long?,
                  status: CorporateStatus?,from: Long?, to: Long?, isPartnerCompany: Boolean? = null) =
            PartnerCorporateSearchFilter(
                name = if (name.isNullOrBlank()) null else "%$name%",
                country = if (country.isNullOrBlank()) null else country,
                accountId = accountId,
                status = status,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                isPartnerCompany = isPartnerCompany
            )
    }
}