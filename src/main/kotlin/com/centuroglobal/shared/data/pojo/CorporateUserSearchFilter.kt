package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus

data class CorporateUserSearchFilter(
    val search: String? = null,
    val accountId: Long? = null,
    val status: AccountStatus? = null,
    val bandId: Long? = null,
    val countryCode: String? = null
) {
    object Builder {
        fun build(search: String?, accountId: Long?, status: String?, bandId: Long?, countryCode: String?) =
            CorporateUserSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                accountId = accountId,
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                bandId = bandId,
                countryCode = if(countryCode.isNullOrBlank()) null else countryCode
            )
    }
}