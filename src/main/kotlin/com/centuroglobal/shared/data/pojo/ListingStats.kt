package com.centuroglobal.shared.data.pojo

data class ListingStats(

    val total: Long,
    val active: Long,
    val suspended: Long,
    val pending: Long
) {

    object ModelMapper {

        fun from(
            total: Long,
            active: Long,
            suspended: Long,
            pending: Long
        ):  ListingStats {

            return  ListingStats(
                total,
                active,
                suspended,
                pending
            )
        }
    }
}