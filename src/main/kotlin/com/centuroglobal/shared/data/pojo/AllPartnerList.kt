package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.PartnerType
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema

data class AllPartnerList constructor(

    @Schema()
    val id: Long?,

    @Schema()
    val name: String,

    @Schema()
    @JsonProperty("createFrom")
    var createdFrom: PartnerType,

    @Schema()
    var contractFromDate: Long,

    @Schema()
    var contractToDate: Long,

    @Schema()
    var casesManagedBy: PartnerCaseType,

    @Schema()
    var queriesManagedBy: PartnerCaseType,

    @Schema()
    var companyLogo: String? = null,

    @Schema()
    var primaryColor: String? = null,
    @Schema()
    var secondaryColor: String? = null,

    @Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION"])
    val status: CorporateStatus,

    @Schema()
    val createReferenceId:Long?,

    @Schema()
    val country: String,

    val features: List<String>?,

    val corporateFeatures: List<String>,

    //root user details from here

    @Schema()
    val rootUserId: Long?,

    @Schema()
    val rootUserEmail: String,

    @Schema()
    val rootUserFirstName: String,
    @Schema()
    val rootUserLastName: String,

    @Schema()
    val rootUserJobTitle: String,

    @Schema()
    val rootUserStatus : AccountStatus,

    @Schema()
    val rootUserCreatedDate: Long,

    @Schema()
    val rootUserLastLoginDate: Long?,

    val rootUserProfilePictureFullUrl: String? = null,

    val rootUserContactNumber: String? = null,

    val onboardingDocs: List<OnboardingDocs>? = null,

    val aiMessageCount: Long = 0

)
{
    object ModelMapper {
        fun from(entity: PartnerEntity, s3Service: AwsS3Service, onboardingDocs: List<OnboardingDocs>):AllPartnerList? {
            val partnerUser = (entity.partnerUsers.filter { it.id == entity.rootUserId })[0]
            return AllPartnerList(
                id= entity.id,
                name = entity.name,
                createdFrom= entity.createdFrom,
                contractFromDate = TimeUtil.toEpochMillis(entity.contractFromDate),
                contractToDate = TimeUtil.toEpochMillis(entity.contractToDate),
                casesManagedBy = entity.casesManaged,
                queriesManagedBy = entity.queriesManaged,
                companyLogo = entity.companyLogo?.let { s3Service.getProfilePicUrl(it) },
                primaryColor = entity.themePrimaryColor,
                secondaryColor = entity.themeSecondaryColor,
                status = entity.status,
                createReferenceId =  entity.referenceId,
                country = entity.country!!,
                onboardingDocs = onboardingDocs,
                corporateFeatures = entity.corporateAccess.split(",").distinct(),

                //root user details

                rootUserId= partnerUser.id,
                rootUserEmail = partnerUser.email,
                rootUserFirstName =partnerUser.firstName,
                rootUserLastName = partnerUser.lastName,
                rootUserJobTitle = partnerUser.partnerJobTitle!!,
                rootUserStatus = partnerUser.status,
                rootUserCreatedDate = TimeUtil.toEpochMillis(partnerUser.createdDate),
                rootUserLastLoginDate = partnerUser.lastLoginDate?.let { TimeUtil.toEpochMillis(it) },
                features = entity.band?.bandAccesses?.mapNotNull { it.access?.featureKey ?: it.visibility?.featureKey }?.distinct(),
                rootUserProfilePictureFullUrl = partnerUser.profilePhotoUrl?.let { s3Service.getProfilePicUrl(it) },
                rootUserContactNumber = partnerUser.contactNo,
                aiMessageCount = partnerUser.questionsQuota
            )


        }
    }
}

