package com.centuroglobal.shared.data.pojo.usage

data class PlaybookUsageLogSearchFilter(
    val country: String? = null,
    val corporateId: Long? = null,
    val from: Long? = null,
    val to: Long? = null
) {
    object Builder {

        fun build(country: String?, corporateId: Long?, from: Long?, to: Long?) =
            PlaybookUsageLogSearchFilter(
                country = if (country.isNullOrBlank()) null else country,
                corporateId = corporateId,
                from = from,
                to = to
            )
    }
}