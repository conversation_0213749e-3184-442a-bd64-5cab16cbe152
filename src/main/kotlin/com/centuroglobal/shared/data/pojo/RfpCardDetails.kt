package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.RfpEntity
import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.util.TimeUtil

data class RfpCardDetails(

    val heading: String?,
    val description: String?,
    val raisedBy: CorporateUserProfile,
    val services: List<RfpService>?,
    val submittedOn: Long?,
    val id: Long,
    val documents: Int = 0,
    val proposal: Int = 0,
    val responses: Long? = null,
    val status: String,
    val assignedTo: List<UserProfile>? = null,
    val isProposalApproved: Boolean,
    val unreadMessages: Long? = null,
    val resolvedDate: Long? = null,
    val cgRequestedStatus: String? = null,
    val partnerName: String? = null,
    val lastUpdatedDate: Long? = null,
    val lastAssignedDate: Long?=null
){

    object ModelMapper {

        fun from(rfpEntity: RfpEntity, createdBy: CorporateUserProfile, assignedTo: List<UserProfile>?, responses: Long?,
                 unreadMsgs: Long? = null,proposals: List<ProposalEntity>, lastAssignedDate: Long? ): RfpCardDetails {

            return RfpCardDetails(
                heading = rfpEntity.heading,
                description = rfpEntity.description,
                raisedBy = createdBy,
                submittedOn = TimeUtil.toEpochMillis(rfpEntity.createdDate),
                id = rfpEntity.id!!,
                documents = rfpEntity.documents?.size?:0,
                proposal = proposals.size,
                services = rfpEntity.serviceDetails?.map { RfpService(it.serviceName, it.countries?.map { it.country }) },
                assignedTo = assignedTo,
                status = rfpEntity.status.name,
                responses = responses,
                isProposalApproved = if(proposals.isEmpty()) false else proposals.all { it.isApproved },
                unreadMessages = unreadMsgs,
                resolvedDate = rfpEntity.resolvedDate?.let { TimeUtil.toEpochMillis(it) },
                cgRequestedStatus = rfpEntity.cgRequestedStatus?.name,
                partnerName = rfpEntity.partner?.name,
                lastUpdatedDate = TimeUtil.toEpochMillis(rfpEntity.lastUpdatedDate),
                lastAssignedDate = lastAssignedDate
            )
        }
    }
}
class RfpService(
    var serviceName: String,

    var countries: List<String>?
) {

}