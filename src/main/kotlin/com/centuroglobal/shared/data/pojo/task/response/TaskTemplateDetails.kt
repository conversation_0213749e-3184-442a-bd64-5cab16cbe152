package com.centuroglobal.shared.data.pojo.task.response

import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity

data class TaskTemplateDetails(
    val id: Long,
    val taskName: String,
    val description: String?,
    val assignedTo: String,
    val expectedTimeline: Long,
    val caseStatus: String,
    val caseMilestone: String,
    val instruction: String?,
    var priority: String?,
    val visibility: String?,
    val usefulLinks: String?,
    val displayOrder: Long?,
    val progress: String
) {

    object ModelMapper {

        fun from(entity: TaskTemplateEntity): TaskTemplateDetails {

            return TaskTemplateDetails(
                id = entity.id!!,
                taskName = entity.name,
                description = entity.description,
                assignedTo = entity.assignedTo,
                expectedTimeline = entity.expectedTimeline,
                instruction = entity.instruction,
                caseStatus = entity.caseStatus,
                caseMilestone = entity.caseMilestone,
                displayOrder = entity.displayOrder,
                priority = entity.priority,
                usefulLinks = entity.usefulLinks,
                visibility = entity.visibility,
                progress = entity.progress
            )
        }
    }
}
