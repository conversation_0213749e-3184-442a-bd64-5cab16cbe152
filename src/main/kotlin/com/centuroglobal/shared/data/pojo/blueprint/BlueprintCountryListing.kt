package com.centuroglobal.shared.data.pojo.blueprint

import com.centuroglobal.shared.data.entity.view.BlueprintCountryView
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintCountry
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page

data class BlueprintCountryListing constructor(

    @Schema()
    val data: PagedResult<BlueprintCountry>,

    @Schema()
    val stats: BluePrintCountryStats

) {
    object ModelMapper {
        fun from(pagedClientView: Page<BlueprintCountryView>, stats: BluePrintCountryStats) =
            BlueprintCountryListing(
                data = PagedResult.ModelMapper.from(pagedClientView, BlueprintCountry.ModelMapper::from),
                stats = stats
            )
    }
}
