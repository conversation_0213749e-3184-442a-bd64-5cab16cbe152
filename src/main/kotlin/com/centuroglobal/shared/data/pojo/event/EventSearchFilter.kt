package com.centuroglobal.shared.data.pojo.event

import com.centuroglobal.shared.data.enums.EventStatus
import com.centuroglobal.shared.util.TimeUtil
import java.util.*

data class EventSearchFilter(
    val search: String? = null,
    val from: Date? = null,
    val to: Date? = null,
    val status: EventStatus? = null
) {
    object Builder {
        fun build(search: String?, from: Long?, to: Long?, status: String?) =
            EventSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                from = if (from != null) TimeUtil.fromInstantToDate(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstantToDate(to / 1000) else null,
                status = if (status.isNullOrBlank()) null else status.let { EventStatus.valueOf(it) }
            )
    }
}