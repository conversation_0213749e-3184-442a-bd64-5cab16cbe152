package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.subscription.usage.ExpertSupportUsageEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.util.TimeUtil

data class ExpertSupportUsageResponse(

    val logType: ReferenceType,

    val logId: Long,

    val submittedBy: String,

    val band: String?,

    val createdAt: Long?,

    val supportTime: Long

): AbstractUsageResponse() {

    object ModelMapper {

        fun from(entity: ExpertSupportUsageEntity, createdBy: CorporateUserEntity): ExpertSupportUsageResponse {

            return ExpertSupportUsageResponse(
                logType = entity.logType,
                logId = entity.logId,
                band = createdBy.band?.name,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                submittedBy = entity.submittedBy,
                supportTime = entity.supportTime
            )
        }

        fun from(entity: ExpertSupportUsageEntity): ExpertSupportUsageResponse {

            return ExpertSupportUsageResponse(
                logType = entity.logType,
                logId = entity.logId,
                band = entity.bandName,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                submittedBy = entity.submittedBy,
                supportTime = entity.supportTime
            )
        }
    }
}