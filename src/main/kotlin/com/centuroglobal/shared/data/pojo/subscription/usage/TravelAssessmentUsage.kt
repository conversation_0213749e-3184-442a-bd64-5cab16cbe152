package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.TravelAssessmentUsageEntity
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class TravelAssessmentUsage(
    private val userActions: List<UserActionEntity>,
    private val usageRepository: UsageRepository,
    private val loginAccountRepository: LoginAccountRepository
) : AbstractUsage() {

    private val countryAttributeName = "destinationCountry"

    override fun getUsage(): Long {
        return userActions.distinctBy { mapper.readTree(it.additionalData).get(countryAttributeName).textValue() }.size.toLong()

    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<TravelAssessmentUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val accessedBy = loginAccountRepository.getFirstNameLastNameById(it.createdBy!!)
            TravelAssessmentUsageEntity(
                accessedBy = accessedBy,
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                destinationCountry = data.get(countryAttributeName).textValue(),
                userId = it.userId!!,
                sessionId = it.sessionId,
                type = ""
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {

            val applicant = loginAccountRepository.getFirstNameLastNameById(it.userId)

            TravelAssessmentUsageResponse.ModelMapper.from(
                it, applicant
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {

           val applicant = loginAccountRepository.getFirstNameLastNameById((it as TravelAssessmentUsageEntity).userId)

           TravelAssessmentUsageResponse.ModelMapper.from(
               it, applicant
            )
        }
    }

}