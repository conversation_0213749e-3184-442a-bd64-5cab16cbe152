package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.BlueprintEntity
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.commons.codec.binary.Base64

data class Blueprint(

    val countryCode: String,

    @Schema()
    val countryName: String,

    @Schema()
    var status: BlueprintStatus,

    @Schema()
    val lastPublishedDate: Long? = null,

    @Schema()
    val steps: List<Step>,

    val sessionId: String? = null
) {
    object ModelMapper {
        fun from(
            entity: BlueprintEntity,
            country: Country,
            pfdUploadedStatuses: Map<StepName, Boolean>,
            stepExpert: Map<StepName, List<ExpertProfileSummary>>,
            isAdmin: Boolean = false,
            isFree: Boolean = false,
            sessionId: String? = null
        ) =
            Blueprint(
                countryCode = country.code,
                countryName = country.name,
                status = entity.status,
                lastPublishedDate = if (entity.lastPublishedDate != null) TimeUtil.toEpochMillis(entity.lastPublishedDate!!) else null,
                steps = entity.steps
                    .sortedBy { it.stepName.order }
                    .map {
                        Step(
                            id = it.stepName.name,
                            prefix = it.stepName.prefix,
                            name = it.stepName.displayName,
                            content = Base64.encodeBase64String(it.content.toByteArray(Charsets.UTF_8)),
                            contentDraft = if (isAdmin) Base64.encodeBase64String(
                                it.contentDraft.toByteArray(Charsets.UTF_8)
                            ) else null,
                            ganttChartSetting = if (it.stepName.allowChart) GanttChartSetting(
                                startAt = it.startAt,
                                duration = it.duration,
                                showChart = it.showChart
                            ) else null,
                            pdfUploaded = pfdUploadedStatuses[it.stepName] ?: false,
                            experts = if (isFree || stepExpert[it.stepName] == null) emptyList() else stepExpert[it.stepName]!!,
                            order = it.stepName.order,
                            menuName = it.stepName.menuName,
                            isDisplay = it.stepName.isDisplay,
                            caseInitial = it.stepName.caseInitial,
                            description = it.stepName.description
                        )
                    },
                sessionId = sessionId
            )

        fun fromTeaser(
            entity: BlueprintEntity,
            country: Country
        ) =
            Blueprint(
                countryCode = country.code,
                countryName = country.name,
                status = entity.status,
                lastPublishedDate = if (entity.lastPublishedDate != null) TimeUtil.toEpochMillis(entity.lastPublishedDate!!) else null,
                steps = entity.steps
                    .sortedBy { it.stepName.order }
                    .map {
                        Step(
                            id = it.stepName.name,
                            prefix = it.stepName.prefix,
                            name = it.stepName.displayName,
                            content = if (it.stepName.isTeaser) "" else Base64.encodeBase64String(
                                it.content.toByteArray(
                                    Charsets.UTF_8
                                )
                            ),
                            contentDraft = null,
                            ganttChartSetting = if (it.stepName.allowChart) GanttChartSetting(
                                startAt = it.startAt,
                                duration = it.duration,
                                showChart = it.showChart
                            ) else null,
                            pdfUploaded = false,
                            experts = emptyList(),
                            teaser = it.stepName.isTeaser,
                            order = it.stepName.order,
                            menuName = it.stepName.menuName,
                            isDisplay = it.stepName.isDisplay,
                            caseInitial = it.stepName.caseInitial,
                            description = it.stepName.description
                        )
                    }
            )
    }
}

data class Step(

    val id: String,

    @Schema()
    val prefix: String,

    @Schema()
    val name: String,

    @Schema()
    val content: String,

    @Schema(
        description = "Draft content. This is only available for admins. Otherwise null."
    )
    val contentDraft: String?,

    @Schema()
    val ganttChartSetting: GanttChartSetting?,

    @Schema()
    val experts: List<ExpertProfileSummary>,

    @Schema()
    val pdfUploaded: Boolean,

    @Schema()
    val teaser: Boolean = false,

    val order: Int?,

    val menuName: String?,

    val isDisplay: Boolean = true,
    val caseInitial: String?,
    val description: String?
)

data class StepRequest(
    val stepId: String,

    @Schema(
        description = "Edited content to be saved. This overwrites draft content, not live or published content."
    )
    val content: String,

    @Schema()
    val ganttChartSetting: GanttChartSetting?
)

data class StepExpertRequest(
    val expertIds: List<Long> = emptyList()
)

data class GanttChartSetting(

    val showChart: Boolean,

    @Schema()
    val startAt: Int?,

    @Schema()
    val duration: Int?
)

data class CaseCategory(
    val id: Long? = null,
    val stepId: String,
    val title: String,
    val caseInitial: String?,
    val description: String?
)
