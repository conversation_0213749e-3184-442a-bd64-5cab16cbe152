package com.centuroglobal.shared.data.pojo.circle

import com.centuroglobal.shared.data.entity.view.CircleView
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.PagedResult
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page

data class CircleListing constructor(

    @Schema()
    val data: PagedResult<Circle>,

    @Schema()
    val stats: CircleStats?

) {
    object ModelMapper {
        fun from(pagedClientView: Page<CircleView>, stats: CircleStats?) =
            CircleListing(
                data = PagedResult.ModelMapper.from(pagedClientView, Circle.ModelMapper::from),
                stats = stats
            )
    }
}
