package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class CorporateUserDetailsFilter(
    val search : String? = null,
    val countryCode: String? = null,
    val corporateId: Long? = null,
    val subscription: SubscriptionType? = null,
    val status : AccountStatus? = null,
    val bandName : Long? = null,
    val createdFrom: LocalDateTime? = null,
    val createdTo: LocalDateTime? = null,
    val partnerId: Long? = null,
    val isPartnerCompany: Boolean? = null,
    val joinedFrom: LocalDateTime? = null,
    val joinedTo: LocalDateTime? = null
) {
    object Builder {
        fun build(search : String?, countryCode: String?, corporateId: Long?, subscription: String?, status : String?, bandName : String?,
                  createdFrom: Long?, createdTo: Long?,
                  partnerId: Long?, isPartnerCompany: Boolean?, joinedFrom: Long?, joinedTo: Long?) =
            CorporateUserDetailsFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                countryCode = if (countryCode.isNullOrBlank()) null else countryCode,
                corporateId  = corporateId,
                subscription = if (subscription.isNullOrBlank()) null else SubscriptionType.valueOf(subscription),
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                bandName= if (bandName.isNullOrBlank()) null else bandName.toLong(),
                createdFrom = createdFrom?.let { TimeUtil.fromInstantMillis(it) },
                createdTo= createdTo?.let { TimeUtil.fromInstantMillis(it) },
                partnerId = partnerId,
                isPartnerCompany= isPartnerCompany,
                joinedFrom = joinedFrom?.let { TimeUtil.fromInstantMillis(it) },
                joinedTo = joinedTo?.let { TimeUtil.fromInstantMillis(it) }
            )
    }
}