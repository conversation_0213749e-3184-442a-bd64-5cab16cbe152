package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import java.time.ZoneId

data class ExpertProfile(
    val id: Long = 0,
    val email: String,
    val firstName: String,
    val lastName: String,
    val jobTitle: String,
    val displayName: String = "",
    val countryCode: String,
    val countryRegionId: Int?,
    val expertiseId: List<Int>? = mutableListOf(),
    val bio: String = "",
    val profilePictureFullUrl: String = "",
    val infoVideoUrl: String?,
    val contactNumber: String = "",
    val contactEmail: String = "",
    val contactWebsite: String = "",
    val status: AccountStatus = AccountStatus.PENDING_VERIFICATION,
    val expertType: String?,
    val viewContract: Boolean = false,
    val companyProfile: ExpertCompanyProfile,
    val associations: List<String>? = null
) {
    object ModelMapper {
        fun from(entity: ExpertUserEntity, profilePictureFullUrl: String, companyLogoFullUrl: String) =
            ExpertProfile(
                id = entity.id!!,
                email = entity.email,
                firstName = entity.firstName,
                lastName = entity.lastName,
                jobTitle = entity.jobTitle,
                displayName = entity.displayName,
                countryCode = entity.countryCode ?: "",
                countryRegionId = entity.countryRegionId,
                expertiseId = entity.expertises.map { it.id },
                bio = entity.bio ?: "",
                profilePictureFullUrl = profilePictureFullUrl,
                infoVideoUrl = entity.infoVideoUrl,
                contactNumber = entity.contactNumber,
                contactEmail = entity.contactEmail,
                contactWebsite = entity.contactWebsite,
                status = entity.status,
                expertType = entity.expertType.toString(),
                viewContract = entity.viewContract,
                companyProfile = ExpertCompanyProfile.ModelMapper.from(entity.companyProfile!!, companyLogoFullUrl),
                associations = entity.companyProfile!!.associatedPartners.map { it.name }
            )
    }
}

data class ExpertCompanyProfile(
    val name: String,
    val companyNumber: String?,
    val companyAddress: String?,
    val aboutBusiness: String?,
    val effectiveDate: Long,
    val effectiveEndDate: Long,
    val feesCurrency: String?,
    val feesAmount: String?,
    val specialTerms: String?,
    val membershipStatus: String?,
    val logoFullUrl: String?,
    var summary: String = "",
    val size: CompanySize?,
    val sizeName: String?,
    val territory: String?,
    val renewContract: Boolean = false,
    var services: String?,
    val partnerId: Long? = null,
    val contract: String? = null,
    val profileImage: String? = null,
    val aiMessageCount: Long = 0,
    val associatedPartners: List<Long>? = null
) {
    object ModelMapper {
        fun from(entity: ExpertCompanyProfileEntity, logoFullUrl: String?) =
            ExpertCompanyProfile(
                name = entity.name,
                companyNumber = entity.companyNumber,
                companyAddress = entity.companyAddress,
                aboutBusiness = entity.aboutBusiness,
                effectiveDate = entity.effectiveDate.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000,
                effectiveEndDate = entity.effectiveEndDate.atZone(ZoneId.systemDefault())
                    .toInstant().epochSecond * 1000,
                feesCurrency = entity.feesCurrency,
                feesAmount = entity.feesAmount,
                specialTerms = entity.specialTerms,
                membershipStatus = entity.membershipStatus,
                size = entity.size,
                sizeName = entity.size?.displayName,
                summary = entity.summary ?: "",
                logoFullUrl = logoFullUrl,
                territory = entity.territory,
                renewContract = entity.renewContract,
                services = entity.services,
                aiMessageCount = entity.questionsQuota?:0,
                associatedPartners = entity.associatedPartners.map { it.id!! }
            )
    }
}

data class ExpertProfileSummary(
    val id: Long,
    val displayName: String,
    val jobTitle: String,
    val contactEmail: String?,
    val contactNumber: String?,
    val contactWebsite: String?,
    val bio: String,
    val profilePictureFullUrl: String,
    val countryName: String,
    val countryCode: String,
    val regionName: String? = null,
    val expertiseName: List<String>? = null,
    val companyProfile: ExpertCompanyProfile,
    val infoVideoUrl: String?,
    var isCurrentCircleJoined: Boolean? = false
) {
    object ModelMapper {
        private fun truncateText(maxLength: Int? = null, text: String): String {
            return if (maxLength != null && text.length > maxLength) text.substring(0, maxLength) + "..." else text
        }

        fun from(
            profile: ExpertProfile,
            country: Country,
            expertiseName: List<String>? = null,
            maxBioLength: Int? = null,
            hasAdminAccess: Boolean = true
        ): ExpertProfileSummary {
            val companyProfile = profile.companyProfile
            companyProfile.summary = truncateText(maxBioLength, profile.companyProfile.summary)

            return ExpertProfileSummary(
                id = profile.id,
                displayName = profile.displayName,
                jobTitle = profile.jobTitle,
                contactEmail = if (hasAdminAccess) profile.contactEmail else null,
                contactNumber = if (hasAdminAccess) profile.contactNumber else null,
                contactWebsite = if (hasAdminAccess) profile.contactWebsite else null,
                bio = truncateText(maxBioLength, profile.bio),
                profilePictureFullUrl = profile.profilePictureFullUrl,
                countryName = country.name,
                countryCode = country.code,
                regionName = country.regions?.find { it.regionId == profile.countryRegionId }?.name,
                expertiseName = expertiseName,
                companyProfile = companyProfile,
                infoVideoUrl = profile.infoVideoUrl
            )
        }
    }
}
