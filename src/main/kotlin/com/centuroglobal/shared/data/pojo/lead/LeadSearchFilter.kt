package com.centuroglobal.shared.data.pojo.lead

import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class LeadSearchFilter(
    val search: String? = null,
    val countryCode: String? = null,
    val expertiseId: Int? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val status: LeadStatus? = null,
    val expertiseList: List<Int>? = null,
    val expertCountryCode: String? = null,
    val expertCreatedDate: LocalDateTime? = null
) {
    object Builder {
        fun build(searchCriteria: LeadsSearchCriteria) =
            LeadSearchFilter(
                search = if (searchCriteria.searchWord.isNullOrBlank()) null else "%${searchCriteria.searchWord}%",
                countryCode = if (searchCriteria.countryCode.isNullOrBlank()) null else searchCriteria.countryCode,
                expertiseId = if (searchCriteria.expertiseId.isNullOrBlank()) null else searchCriteria.expertiseId.toInt(),
                from = if (searchCriteria.from != null) TimeUtil.fromInstantMillis(searchCriteria.from) else null,
                to = if (searchCriteria.to != null) TimeUtil.fromInstantMillis(searchCriteria.to) else null,
                status = if (searchCriteria.status.isNullOrBlank()) null else LeadStatus.valueOf(searchCriteria.status)
            )

        fun leadBuild(searchCriteria: LeadsSearchCriteria, expertUser: ExpertUserEntity?) =
            LeadSearchFilter(
                search = if (searchCriteria.searchWord.isNullOrBlank()) null else "%${searchCriteria.searchWord}%",
                countryCode = if (searchCriteria.countryCode.isNullOrBlank()) null else searchCriteria.countryCode,
                expertiseId = if (searchCriteria.expertiseId.isNullOrBlank()) null else searchCriteria.expertiseId.toInt(),
                from = if (searchCriteria.from != null) TimeUtil.fromInstantMillis(searchCriteria.from) else null,
                to = if (searchCriteria.to != null) TimeUtil.fromInstantMillis(searchCriteria.to) else null,
                status = if (searchCriteria.status.isNullOrBlank()) null else LeadStatus.valueOf(searchCriteria.status),
                expertiseList = if (expertUser?.expertises.isNullOrEmpty()) null else expertUser?.expertises?.map { it.id }?.toList(),
                expertCountryCode = if (expertUser?.countryCode.isNullOrBlank()) null else expertUser?.countryCode,
                expertCreatedDate = if (expertUser?.createdDate == null) null else expertUser?.createdDate,
            )
    }
}