package com.centuroglobal.shared.data.pojo.circle

import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class CircleSearchFilter(
    var status: CircleStatus? = null,
    var circleType: CircleType? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val search: String? = null,
    var countryCode: String? = null,
    var expertise: Int? = null
) {
    object Builder {
        fun build(
            status: String?,
            circleType: String?,
            from: Long?,
            to: Long?,
            search: String?,
            countryCode: String?,
            expertiseId: String?
        ) =
            CircleSearchFilter(
                status = if (status.isNullOrBlank()) null else CircleStatus.valueOf(status),
                circleType = if (circleType.isNullOrBlank()) null else CircleType.valueOf(circleType),
                from = if (from != null) TimeUtil.fromInstant(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstant(to / 1000) else null,
                search = if (search.isNullOrBlank()) null else "%$search%",
                countryCode = if (countryCode.isNullOrBlank()) null else "%$countryCode%",
                expertise = if (expertiseId.isNullOrBlank()) null else expertiseId.toInt()
            )
    }
}
