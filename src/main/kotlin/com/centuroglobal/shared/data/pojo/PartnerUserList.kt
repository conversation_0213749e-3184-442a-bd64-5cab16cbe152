package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class PartnerUserList(

    @Schema
    val name: String,

    @Schema
    val email: String,

    @Schema
    val country: String?,

    @Schema(description = "Created date in epoch milliseconds.")
    val createdOn: Long,

    @Schema(description = "Created date in epoch milliseconds.")
    val lastLogOn: Long?,

    @Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION"])
    val status: AccountStatus,

    val userId: Long,

    val partnerId: Long?,

    val partnerName: String?


)
{
    object ModelMapper {
        fun from(entity: LoginAccountEntity): PartnerUserList {
            return PartnerUserList(
                name = "${entity.firstName} ${entity.lastName}",
                email = entity.email,
                country = entity.countryCode,
                status = entity.status,
                createdOn = TimeUtil.toEpochMillis(entity.createdDate),
                lastLogOn = entity.lastLoginDate?.let { TimeUtil.toEpochMillis(it) },
                userId = entity.id!!,
                partnerId = entity.partner?.id,
                partnerName = entity.partner?.name
            )
        }
    }
}

