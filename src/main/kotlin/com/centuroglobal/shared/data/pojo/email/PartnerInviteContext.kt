package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class PartnerInviteContext(
    val firstName: String,
    val displayName: String,
    val verifyUrl: String,
    val s3ServerUrl: String,
    val partnerName: String?,
    val partnerAdminName: String? = null
) {
    object ModelMapper {
        fun toContext(userContext: PartnerInviteContext): Context {
            val ctx = Context()
            ctx.setVariable("FIRST_NAME", userContext.firstName)
            ctx.setVariable("DISPLAY_NAME", userContext.displayName)
            ctx.setVariable("ORGANISATION_NAME", userContext.partnerName)
            ctx.setVariable("VERIFY_URL", userContext.verifyUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            ctx.setVariable("BG_COLOR", "#ca6b86")
            ctx.setVariable("PARTNER_COMPANY_NAME", userContext.partnerName)
            ctx.setVariable("PARTNER_NAME", userContext.partnerName)
            ctx.setVariable("PARTNER_ADMIN_NAME", userContext.partnerAdminName)

            return ctx
        }
    }
}