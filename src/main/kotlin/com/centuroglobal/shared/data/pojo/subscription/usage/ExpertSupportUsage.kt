package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.ExpertSupportUsageEntity
import com.centuroglobal.shared.data.pojo.subscription.WorkLogRequest
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class ExpertSupportUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.sumOf {
            getPayload(it.additionalData).timeSpent
        }
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<ExpertSupportUsageEntity> {

        //group by query type and id as there can be more than one work log for same query
        val groupByTypeAndId = userActions.groupBy {
            val data = getPayload(it.additionalData)
            Pair(data.referenceType, data.referenceId!!)
        }

        return groupByTypeAndId.map { pairListEntry ->

            val totalTimeSpent = pairListEntry.value.sumOf {
                val data = getPayload(it.additionalData)
                data.timeSpent
            }
            val user = corporateUserRepository.findById(pairListEntry.value[0].userId!!).get()

            ExpertSupportUsageEntity(
                charges = 0F,
                createdAt = pairListEntry.value[0].endTime ?: pairListEntry.value[0].startTime,
                subscriptionUsageDetailsId = id,
                userId = user.id!!,
                bandName = user.band?.name?:"",
                logId = pairListEntry.key.second,
                logType = pairListEntry.key.first,
                submittedBy = "${user.firstName} ${user.lastName}",
                supportTime = totalTimeSpent
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            ExpertSupportUsageResponse.ModelMapper.from(it)
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            ExpertSupportUsageResponse.ModelMapper.from(
                it as ExpertSupportUsageEntity
            )
        }
    }

    private fun getPayload(additionalData: String?): WorkLogRequest {
        return getPayload(additionalData, WorkLogRequest::class)
    }

}