package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.view.CaseViewForResidenceCountry
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.data.pojo.UserProfile
import java.time.ZoneId

data class CaseViewForDownload @JvmOverloads constructor(
    var caseId: Long? = null,

    val initiatedDate: Long? = null,

    val startDate: Long? = null,

    var country: String? = null,

    var companyName: String? = null,

    var initiatedFor: String? = null,

    var email: String? = null,

    var category: String? = null,

    var initiatedBy: ClientView? = null,

    var status: String = CaseStatus.NOT_STARTED.name,

    var statusUpdate: String? = null,

    var actionFor: String? = null,

    var isPriorityCase: Boolean = false,

    var accountManager: UserProfile? = null,

    var archive: Boolean,

    var notifyCaseOwner: Boolean,

    var notes: String ?= null,

    var lastUpdatedDate: Long ?= null,

    var fromCountry: String?=null,

    var percentCompletion: Int = 0,

    var managers: List<String>,

    var aliasName: String? = null,

    var partnerName: String? = null
) {
    object ModelMapper {
        fun from(case: CaseViewForResidenceCountry, accountManager: UserProfile?): CaseViewForDownload {
            return CaseViewForDownload(
                caseId = case.id,
                lastUpdatedDate = case.lastUpdatedDate?.let { it.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000 } ?: null,
                initiatedDate = case.initiatedDate.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000,
                startDate = case.startDate?.let{it.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000}?:null,
                country = case.country,
                companyName = case.companyName,
                initiatedFor = case.initiatedFor,
                email = case.email,
                category = case.category?.subCategoryId,
                status = case.status,
                initiatedBy = case.createdBy,
                isPriorityCase = case.isPriorityCase,
                statusUpdate = case.statusUpdate,
                actionFor = case.actionFor,
                accountManager = accountManager,
                archive = case.archive,
                notes = case.notes,
                notifyCaseOwner =case.notifyCaseOwner,
                fromCountry = case.fromCountry?: case.residenceCountry, // in case of dependent_visa we will get residence_country
                percentCompletion = case.percentCompletion,
                managers = case.managers.map { it.firstName+" "+it.lastName }.toList(),
                aliasName = case.aliasName,
                partnerName = case.partnerName
                )
        }
    }
}