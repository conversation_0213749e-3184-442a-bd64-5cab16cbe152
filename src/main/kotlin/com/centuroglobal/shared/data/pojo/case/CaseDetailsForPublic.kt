package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.payload.case.CaseFeesRequest
import java.time.ZoneId

data class CaseDetailsForPublic @JvmOverloads constructor(
    var caseId: Long? = null,

    val initiatedDate: Long? = null,

    var country: String? = null,

    var companyName: String? = null,

    var initiatedFor: String? = null,

    var category: String? = null,

    var caseFees: CaseFeesRequest?=null,

    var isTokenExpired: Boolean
) {
    object ModelMapper {
        fun from(case: CaseEntity): CaseDetailsForPublic {
            return CaseDetailsForPublic(
                caseId = case.id,
                initiatedDate = case.initiatedDate!!.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000,
                country = case.country,
                companyName = case.personalDetails?.companyName,
                initiatedFor = case.initiatedFor,
                category = case.category?.subCategoryId,
                caseFees =case.caseFees?.let { CaseFeesRequest.ModelMapper.from(it) },
                isTokenExpired = false
            )
        }
    }
}