package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.TravelAssessmentUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class TravelAssessmentUsageResponse(

    val destinationCountry: String,

    val accessedBy: String,

    val createdAt: Long?,

    val charges: Float,

    val applicant: String?

): AbstractUsageResponse() {

    object ModelMapper {

        fun from(usageEntity: TravelAssessmentUsageEntity, applicant: String?): TravelAssessmentUsageResponse {

            return TravelAssessmentUsageResponse(
                destinationCountry = usageEntity.destinationCountry,
                accessedBy = usageEntity.accessedBy,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F,
                applicant = applicant
            )
        }
    }
}