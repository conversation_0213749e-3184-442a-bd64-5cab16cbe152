package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

data class Client(

    val id: Long?,

    @Schema()
    val email: String,

    @Schema()
    val fullName: String,

    @Schema(
        
        allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION"]
    )
    val status: AccountStatus,

    @Schema()
    val company: String,

    @Schema()
    val subscription: Boolean? = null,

    @Schema()
    val userType: UserType,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long,

    @Schema()
    var referredBy: String?,

    @Schema()
    val referral: Int? = null,

    @Schema()
    val userId: Long? = null,

    var countryCode: String?,

    var expertiseIds: String? = null,

    var isLinkedin: Boolean,

    var country: String? = null,

    var expertise: String? = null,

    var subscriptionType: String? = null,

    var expertType: String? = null,

    var corporateType: String? = null,

    var secondaryUserCount: Long? = 0,

    var bandName: String ?= null,

    var lastLoginTime: Long? = null,

    var lastTermsViewDate: Long?=null
) {
    object ModelMapper {
        fun from(entity: ClientView) =
            Client(
                id = entity.id,
                email = entity.email,
                fullName = entity.fullName,
                status = entity.status,
                company = entity.company ?: "",
                userType = entity.userType,
                subscription = entity.subscription,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate),
                referredBy = entity.referredBy,
                referral = entity.referral,
                userId = entity.userId,
                countryCode = entity.countryCode,
                expertiseIds = entity.expertiseIds,
                isLinkedin = entity.isLinkedin,
                subscriptionType = entity.subscriptionType,
                expertType = entity.expertType,
                corporateType = entity.corporateType,
                secondaryUserCount = entity.secondaryUserCount,
                bandName = entity.bandName,
                lastLoginTime = entity.lastLoginDate?.let { TimeUtil.toEpochMillis(it) },
                lastTermsViewDate = entity.lastTermsViewDate?.let{TimeUtil.toEpochMillis(it)}
            )

        fun fromExpertUser(secondaryExpertUser: ExpertUserEntity): Client {
            val firstName = secondaryExpertUser.firstName.trim()
            val lastName = secondaryExpertUser.lastName.trim()
            val date =
                if (secondaryExpertUser.createdDate != null) secondaryExpertUser.createdDate!! else LocalDateTime.now()
                    .truncatedTo(ChronoUnit.MILLIS)
            return Client(
                id = secondaryExpertUser.id,
                email = secondaryExpertUser.email,
                fullName = "$firstName $lastName".trim(),
                status = secondaryExpertUser.status,
                company = secondaryExpertUser.companyProfile?.name ?: "",
                createdDate = TimeUtil.toEpochMillis(date),
                referredBy = secondaryExpertUser.referredBy.toString(),
                countryCode = secondaryExpertUser.countryCode,
                isLinkedin = secondaryExpertUser.isLinkedin,
                subscriptionType = secondaryExpertUser.subscriptionType?.name,
                expertType = secondaryExpertUser.expertType,
                userType = secondaryExpertUser.getUserType()
            )
        }
    }
}
