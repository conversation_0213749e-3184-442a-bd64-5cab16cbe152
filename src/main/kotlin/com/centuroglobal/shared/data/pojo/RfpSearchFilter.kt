package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime


data class RfpSearchFilter(
    val search: String? = null,
    val accountId: String?= null,
    val user: String?= null,
    val responses: String?= null,
    val categories: String?= null,
    val countryCode: String?= null,
    val status: List<RfpStatus>? = null,
    val corporateId: Long?,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    override var partnerId: Long? = null,
    var cgRequested: Boolean? = null,
    var isPartnerRfp: Boolean? = null,
    override var managedBy: PartnerCaseType? = null

    ): AbstractSearchFilter() {
    object Builder {
        fun build(search: String?,
                  accountId: String?,
                  user: String?,
                  responses: String?,
                  categories: String?,
                  countryCode: String?,
                  status: List<RfpStatus>?,
                  corporateId: Long?,
                  from: Long?,
                  to: Long?,
                  partnerId: Long?,
                  cgRequested: Boolean?,
                  isPartnerRfp: Boolean?
                  ) =
            RfpSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                accountId = if (accountId.isNullOrBlank()) null else accountId,
                user = if (user.isNullOrBlank()) null else user,
                responses = if (responses.isNullOrBlank()) null else responses,
                categories = if (categories.isNullOrBlank()) null else categories,
                countryCode = if (countryCode.isNullOrBlank()) null else countryCode,
                status = status,
                corporateId = corporateId,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                partnerId = partnerId,
                cgRequested = cgRequested,
                isPartnerRfp = isPartnerRfp
            )
    }
}