package com.centuroglobal.shared.data.pojo.blueprint

import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class BlueprintCountrySearchFilter(
    val status: BlueprintStatus?,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val search: String? = null
) {
    object Builder {
        fun build(status: String?, to: Long?, from: Long?, search: String?): BlueprintCountrySearchFilter =
            BlueprintCountrySearchFilter(
                status = if (status.isNullOrBlank()) null else BlueprintStatus.valueOf(status),
                from = if (from != null) TimeUtil.fromInstant(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstant(to / 1000) else null,
                search = if (search.isNullOrBlank()) null else "$search"
            )
    }
}