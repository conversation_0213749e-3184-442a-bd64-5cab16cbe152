package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class CorporateUser constructor(

    val id: Long?,

    @Schema()
    val email: String,

    @Schema()
    val firstName: String,

    @Schema()
    val lastName: String,

    @Schema()
    val jobTitle: String,

    @Schema()
    val corporateId: Long? = null,

    @Schema(
        allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED", "DRAFT"]
    )
    val status: AccountStatus,

    @Schema(
        allowableValues = ["ROLE_CORPORATE_SUBSCRIBER","ROLE_CORPORATE"]
    )
    val role: Role,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long
) {
    object ModelMapper {
        fun from(entity: CorporateUserEntity) =
            CorporateUser(
                id = entity.id,
                email = entity.email,
                firstName = entity.firstName,
                lastName = entity.lastName,
                jobTitle = entity.jobTitle,
                corporateId = entity.corporate.id,
                status = entity.status,
                role = entity.role,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!)
            )
    }
}