package com.centuroglobal.shared.data.pojo

data class ExpertSearchFilter(
    val search: String? = null,
    val countryCode: String? = null,
    val expertiseId: Int? = null,
    var expertiseIds: List<Int>? = null,
    var countryCodes: List<String>? = null
) {
    object Builder {
        fun build(search: String?, countryCode: String?, expertiseId: String?) =
            ExpertSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                countryCode = if (countryCode.isNullOrBlank()) null else countryCode,
                expertiseId = if (expertiseId.isNullOrBlank()) null else expertiseId.toInt()
            )
    }
}
