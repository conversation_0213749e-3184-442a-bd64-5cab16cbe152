package com.centuroglobal.shared.data.pojo.chat

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.GroupChatParticipantEntity
import com.centuroglobal.shared.service.aws.AwsS3Service

data class GroupChatParticipantResponse(
    val id: Long,

    val email: String,

    val firstName: String,

    val lastName: String,

    val role: String,

    val countryCode: String? = null,

    val profilePictureFullUrl: String? = null,

    var jobTitle : String?,

    var companyDetails: Map<String, Any?>?,

    var isActive: Boolean
){

    object ModelMapper {

        fun from(participantEntity: GroupChatParticipantEntity, corporateUserEntity: CorporateUserEntity, awsS3Service: AwsS3Service): GroupChatParticipantResponse {
            val response = from(participantEntity, awsS3Service)
            response.jobTitle = corporateUserEntity.jobTitle
            response.companyDetails = getCompanyDetailsMap(corporateUserEntity.corporate.name,
                corporateUserEntity.profilePhotoUrl?.let { awsS3Service.getS3Url(it) })
            return response
        }

        fun from(participantEntity: GroupChatParticipantEntity, expertUserEntity: ExpertUserEntity, awsS3Service: AwsS3Service): GroupChatParticipantResponse {
            val response = from(participantEntity, awsS3Service)
            response.jobTitle = expertUserEntity.jobTitle
            val companyName = expertUserEntity.companyProfile?.name
            response.companyDetails = getCompanyDetailsMap(companyName,
                expertUserEntity.profilePhotoUrl?.let { awsS3Service.getS3Url(it) })
            return response
        }


        fun from(participantEntity: GroupChatParticipantEntity, awsS3Service: AwsS3Service): GroupChatParticipantResponse {
            return GroupChatParticipantResponse(
                id = participantEntity.user.id!!,
                email = participantEntity.user.email,
                firstName = participantEntity.user.firstName,
                lastName = participantEntity.user.lastName,
                role = participantEntity.user.role.name,
                countryCode = participantEntity.user.countryCode,
                profilePictureFullUrl = participantEntity.user.profilePhotoUrl?.let { awsS3Service.getS3Url(it) },
                isActive = participantEntity.isActive,
                jobTitle = null,
                companyDetails = getCompanyDetailsMap("CENTURO GLOBAL", null)
            )
        }
        private fun getCompanyDetailsMap(companyName: String?, profileUrl: String?): Map<String, Any?> {
            return mapOf("name" to companyName, "profilePictureFullUrl" to profileUrl)
        }
    }
}

data class  GroupChatDetails(
    var participants: List<GroupChatParticipantResponse>?,
    var isMember: Boolean? = false,
)