package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnore

data class SubscriptionPlansResponse(

    val id: Long?,

    val name: String,

    val currency: String,

    @JsonIgnore
    val companyId: Long,

    @JsonIgnore
    val companyType: String,

    val price: Float,

    val isActive: Boolean,

    val modules: List<SubscriptionDetails>,

    val createdBy: String,

    val updatedBy: String,

    val createdAt: Long,

    val updatedAt: Long,

    var subscriptionStartDate: Long? = null,

    var subscriptionEndDate: Long? = null
){
    object ModelMapper {
        fun from(plan: SubscriptionPlanEntity, createdBy: String, updateBy: String): SubscriptionPlansResponse {

            return SubscriptionPlansResponse(
                id = plan.id,
                name = plan.name,
                currency = plan.currency,
                companyId = plan.companyId,
                companyType = plan.companyType,
                modules = plan.modules.map { SubscriptionDetails.ModelMapper.from(it) },
                price = plan.price,
                isActive = plan.isActive,
                createdBy = createdBy,
                updatedBy = updateBy,
                createdAt = TimeUtil.toEpochMillis(plan.createdDate),
                updatedAt = TimeUtil.toEpochMillis(plan.lastUpdatedDate),
                subscriptionStartDate = plan.startDate?.let { TimeUtil.toEpochMillis(it) },
                subscriptionEndDate = plan.endDate?.let { TimeUtil.toEpochMillis(it) }
            )
        }
    }
}