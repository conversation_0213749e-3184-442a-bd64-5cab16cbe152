package com.centuroglobal.shared.data.pojo.blueprint

import com.centuroglobal.shared.data.entity.view.BlueprintCountryView
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.util.TimeUtil

data class BlueprintCountry(
    val countryCode: String,

    var status: BlueprintStatus,

    var updatedBy: String?,

    var createdDate: Long,

    var lastUpdatedAt: Long,

    var isNoData: Boolean,

    var countryName: String? = null
) {
    object ModelMapper {
        fun from(blueprintCountryView: BlueprintCountryView) =
            BlueprintCountry(
                countryCode = blueprintCountryView.countryCode,
                status = blueprintCountryView.status,
                updatedBy = blueprintCountryView.updatedBy,
                isNoData = blueprintCountryView.isNoData,
                createdDate = TimeUtil.toEpochMillis(blueprintCountryView.createdDate),
                lastUpdatedAt = TimeUtil.toEpochMillis(blueprintCountryView.lastUpdatedAt),
                countryName = blueprintCountryView.countryName
            )
    }
}
