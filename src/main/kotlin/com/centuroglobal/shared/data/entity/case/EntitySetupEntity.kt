package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.*

@Entity(name = "entity_setup")
data class EntitySetupEntity(

    var caseCountry: String,

    var entityType: String,

    var setupCompanyName: String,

    var businessActivities: String? = null,

    var hqCountry: String? = null,

    var hqState: String? = null,

    var hqCity: String? = null,

    var hqZipcode: String? = null,

    var hqAddress: String? = null,

    var foreignOwned: Boolean = false,

    var requireAddress: Boolean = false,

    var supportTaxRegistration: Boolean = false,
    
    var otherInformation: String? = null,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "fullName", column = Column(name = "cs_full_Name")),
        AttributeOverride(name = "dob", column = Column(name = "cs_dob")),
        AttributeOverride(name = "occupation", column = Column(name = "cs_occupation")),
        AttributeOverride(name = "nationality", column = Column(name = "cs_nationality")),
        AttributeOverride(name = "residentialAddress", column = Column(name = "cs_residential_address")),
        AttributeOverride(name = "serviceAddress", column = Column(name = "cs_service_address")),
    )
    val companySecretary: CompanySecretary,

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, mappedBy = "entitySetup", fetch = FetchType.EAGER)
    var directors: MutableList<DirectorEntity> = mutableListOf(),

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, mappedBy = "entitySetup", fetch = FetchType.EAGER)
    var shareholders: MutableList<ShareholderEntity> = mutableListOf()

) : CaseEntity()