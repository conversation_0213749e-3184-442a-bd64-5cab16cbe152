package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "blueprint")
data class BlueprintEntity @JvmOverloads constructor(

    @Id
    val countryCode: String,

    @Column(nullable = false, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var status: BlueprintStatus,

    @OneToMany(
        fetch = FetchType.LAZY,
        cascade = [CascadeType.ALL],
        orphanRemoval = true
    )
    @JoinColumn(name = "blueprint_country_code")
    val steps: MutableList<BlueprintStepEntity> = mutableListOf(),

    override var lastUpdatedBy: Long,

    var lastPublishedDate: LocalDateTime? = null,

    var lastPublishedBy: Long? = null

) : AuditByBaseEntity(lastUpdatedBy) {

    fun setSteps(steps: MutableList<BlueprintStepEntity>) {
        steps.forEach {
            updateStep(it)
        }
    }

    private fun updateStep(step: BlueprintStepEntity) {
        val currentBlueprintStep: BlueprintStepEntity? = steps.find { it.stepName == step.stepName }
        if (currentBlueprintStep == null) {
            step.blueprint = this
            this.steps.add(step)
        } else {
            if (step.content.isNotBlank()) {
                currentBlueprintStep.content = step.content
            }
            currentBlueprintStep.contentDraft = step.contentDraft
            currentBlueprintStep.showChart = step.showChart
            currentBlueprintStep.duration = step.duration
            currentBlueprintStep.startAt = step.startAt
            currentBlueprintStep.lastUpdatedBy = step.lastUpdatedBy
        }
    }
}
