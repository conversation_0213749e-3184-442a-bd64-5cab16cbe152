package com.centuroglobal.shared.data.entity.case

import jakarta.persistence.Embeddable

@Embeddable
data class PostedWorkerApplicant(

    var firstName: String,

    var lastName: String,

    var residenceCountry: String,

    var nationality: String,

    var highestQualification: String,

    var jobTitle: String? = null,

    var contactNo: String? = null,

    var emailAddress: String,

    var shareApplicantInfo: Boolean?=null
)