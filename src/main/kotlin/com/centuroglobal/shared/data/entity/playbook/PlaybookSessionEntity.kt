package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "playbook_sessions")
data class PlaybookSessionEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne
    @JoinColumn(name = "playbook_id")
    var playbook: PlaybookEntity?,

    var startTime: LocalDateTime,

    var endTime: LocalDateTime? = null,

    var questionCount: Long,

    var sessionId: String,

    var threadId: String? = null,

    var type: String,

    @OneToMany(mappedBy = "playbookSession")
    var playbookChats: MutableList<PlaybookChatEntity> = mutableListOf(),

    @OneToOne
    @JoinColumn(name = "createdBy", updatable = false, insertable = false)
    var corporateUser: CorporateUserEntity? = null

): AuditUserBaseEntity()