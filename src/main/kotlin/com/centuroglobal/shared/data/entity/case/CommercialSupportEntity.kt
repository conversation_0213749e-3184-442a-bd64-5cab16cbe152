package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "commercial_support")
data class CommercialSupportEntity(

    var caseCountry: String,
    var legalAdvice: String? = null,
    var currentContracts: String? = null,
    var purchaseContract: String? = null,
    var commercialNeeds: String? = null

) : CaseEntity()