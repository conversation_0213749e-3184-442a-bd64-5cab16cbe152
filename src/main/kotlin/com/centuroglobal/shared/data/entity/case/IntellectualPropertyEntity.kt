package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "intellectual_property")
data class IntellectualPropertyEntity(

    var caseCountry: String,
    var ipSupport: String? = null,
    var interestedIp: String? = null,
    var protectWordLogo: String? = null,
    var trademarkName: String? = null,
    var copyrightNature: String? = null,
    var copyrightDetails: String? = null,
    var patentDescription: String? = null,
    var designDescription: String? = null,
    var ipAssignSupport: Boolean = false,
    var ipDisputeSupport: Boolean = false,
    var trademarkLogoLink: String?=null
) : CaseEntity()