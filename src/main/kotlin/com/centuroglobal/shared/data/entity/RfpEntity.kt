package com.centuroglobal.shared.data.entity


import com.centuroglobal.shared.data.enums.RfpStatus
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime
import com.centuroglobal.shared.data.enums.CGRequestStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType

@Entity(name = "rfp")
data class RfpEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var heading: String? = null,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "rfp_id")
    var serviceDetails: MutableList<RfpServiceEntity>? = null,

    var description: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: RfpStatus,

    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL], orphanRemoval = true)
    @JoinColumn(name = "rfp_id")
    var documents: MutableList<RfpDocumentEntity>? = null,

    var resolvedDate: LocalDateTime? = null,

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "rfp_assignee",
        joinColumns = [JoinColumn(name = "rfp_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "user_id", referencedColumnName = "id")]
    )
    var assignedTo: MutableList<LoginAccountEntity> = mutableListOf(),

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "created_by")
    var createdBy: CorporateUserEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_id")
    var partner: PartnerEntity?= null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var cgRequestedStatus: CGRequestStatus? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var managedBy: PartnerCaseType = PartnerCaseType.CG

) : AuditBaseEntity()
