package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.PartnerType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "corporate_user")
@DiscriminatorValue("CORPORATE")
data class CorporateUserEntity @JvmOverloads constructor(

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "corporate_id", nullable = false)
    var corporate: CorporateEntity,

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "corporate_user_accounts",
        joinColumns = [JoinColumn(name = "user_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "account_id", referencedColumnName = "id")]
    )


    var accounts: Set<AccountEntity>? = null,

    var jobTitle: String,

    var keepMeInformed: Boolean = false,

    @OneToMany(mappedBy = "corporateUser", fetch = FetchType.EAGER, cascade = [CascadeType.ALL], orphanRemoval = true)
    var managers: List<CorporateUserManagerEntity> = mutableListOf(),

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "band_id")
    var band: BandsEntity?,

    @JsonIgnore
    @OneToMany(mappedBy = "corporateUser", fetch = FetchType.EAGER, cascade = [CascadeType.ALL], orphanRemoval = true)
    var notificationPreferences: MutableList<NotificationPreferencesEntity> = mutableListOf(),

    @Column(name = "corporate_user_type")
    var userType: String? = null,

    var educationQualification: String? = null,

    var salary: String? = null,

    var relevantExperience: String? = null

    ) : LoginAccountEntity() {
    constructor() : this(
        CorporateEntity(0,"", "", CorporateStatus.ACTIVE, false,
        LocalDateTime.now(), 0, null, mutableListOf(), 0, PartnerEntity(
                0, "", PartnerType.NEW,LocalDateTime.now(), LocalDateTime.now(), PartnerCaseType.CG, PartnerCaseType.CG,
                    "", "","", 0,
                CorporateStatus.ACTIVE, mutableListOf(),band = null, referenceId = null,country = null, corporateAccess = "")), null, "",
        false, mutableListOf(), null, mutableListOf() )
}
