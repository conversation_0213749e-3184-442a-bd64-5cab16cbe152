package com.centuroglobal.shared.data.entity.case

import jakarta.persistence.Embeddable

@Embeddable
data class TaxApplicant(

    var firstName:String,

    var lastName:String,

    var citizenCountry: String,

    var residenceCountry:String,

    var jobTitle: String?,

    var contactNo: String?=null,

    var emailAddress: String?=null,

    var applicantState: String?,

    var shareApplicantInfo: Boolean?=null
)