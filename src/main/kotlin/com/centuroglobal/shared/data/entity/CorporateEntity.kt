package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.CorporateStatus
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "corporate")
data class CorporateEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(unique = true, nullable = false)
    var name: String,

    @Column(nullable = false)
    var countryCode: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: CorporateStatus,

    @Column(name = "subscription_active", nullable = false)
    var subscriptionActive: Boolean,

    var subscriptionExpiryDate: LocalDateTime? = null,

    var rootUserId: Long,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL], mappedBy = "corporate")
    var accountList: List<AccountEntity>? = null,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL], mappedBy =  "corporate")
    var users: List<CorporateUserEntity> = mutableListOf(),

    override var lastUpdatedBy: Long,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_id")
    var partner: PartnerEntity? = null,

    @Column(name = "theme_primary_color", nullable = false)
    var primaryColor : String? = null,

    @Column(name = "theme_secondary_color", nullable = false)
    var secondaryColor:String? = null,

    var companyLogoId:String? = null,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL], mappedBy =  "corporate", orphanRemoval = true)
    var team: MutableList<CorporateTeamEntity> = mutableListOf(),

    var questionsQuota: Long? = null,

    var subscriptionStartDate: LocalDateTime? = null,

    var subscriptionEndDate: LocalDateTime? = null,

    var isTeamEmail: Boolean = false

) : AuditByBaseEntity(lastUpdatedBy)