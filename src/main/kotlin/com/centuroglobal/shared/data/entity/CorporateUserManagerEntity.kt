package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "corporate_user_manager")
data class CorporateUserManagerEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.MERGE])
    @JoinColumn(name = "user_id")
    var corporateUser: CorporateUserEntity,

    var managerId: Long
)