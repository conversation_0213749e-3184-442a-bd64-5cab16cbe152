package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime
/*
@Entity(name = "playbook_share")
data class PlaybookShareEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var playbook: PlaybookEntity,

    var user: LoginAccountEntity

): AuditUserBaseEntity()*/