package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.case.CaseContactInformation
import com.centuroglobal.shared.data.entity.case.CaseFees
import com.centuroglobal.shared.data.entity.case.CaseMilestonesEntity
import com.centuroglobal.shared.data.entity.case.CaseStatusHistory
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.entity.view.UserView
import com.centuroglobal.shared.data.enums.CGRequestStatus
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.TrackingType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import org.hibernate.annotations.LazyCollection
import org.hibernate.annotations.LazyCollectionOption
import java.time.LocalDateTime


@Entity(name = "cases")
@Table(name = "cases")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "case_type")
abstract class CaseEntity : AuditBaseEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

    var initiatedDate: LocalDateTime? = null

    var startDate: LocalDateTime? = null

    var aliasName: String? = null

    var country: String? = null

    var initiatedFor: String? = null

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id")
    lateinit var category: CaseCategoryEntity

    lateinit var parentCategoryId: String

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", nullable = false)
    var createdBy: ClientView? = null

    @LazyCollection(LazyCollectionOption.FALSE)
    @ManyToMany(cascade  =
    [   CascadeType.DETACH,
        CascadeType.MERGE,
        CascadeType.REFRESH,
        CascadeType.PERSIST
    ])
    @JoinTable(
        name = "case_assignee",
        joinColumns = [JoinColumn(name = "case_id")],
        inverseJoinColumns = [JoinColumn(name = "expert_id")]
    )
    var assignee: MutableList<UserView> = mutableListOf()

    @LazyCollection(LazyCollectionOption.FALSE)
    @ManyToMany(cascade  =
    [   CascadeType.DETACH,
        CascadeType.MERGE,
        CascadeType.REFRESH,
        CascadeType.PERSIST
    ])
    @JoinTable(
        name = "case_managers",
        joinColumns = [JoinColumn(name = "case_id")],
        inverseJoinColumns = [JoinColumn(name = "user_id")]
    )
    var managers: MutableList<UserView> = mutableListOf()

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_manager", nullable = true)
    var accountManager: LoginAccountEntity? = null

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assign_company", nullable = true)
    var assignCompany: ExpertCompanyProfileEntity? = null

    var status: String = CaseStatus.PENDING_VERIFICATION.name

    @JsonIgnore
    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL])
    @JoinColumn(name = "case_id")
    var documents: List<CaseDocumentsEntity>? = null

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "case_id", updatable = false)
    var statusHistory: List<CaseStatusHistory>? = null

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "case_id", updatable = false)
    var milestones: List<CaseMilestonesEntity>? = null

    var statusUpdate: String? = null

    var notifyPrimaryExpert: Boolean = false

    var isPriorityCase: Boolean = false

    var email: String? = null

    var moreInformation: String? = null

    var estimatedTimeline: String? = null

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "firstName", column = Column(name = "contact_first_name")),
        AttributeOverride(name = "lastName", column = Column(name = "contact_last_name")),
        AttributeOverride(name = "title", column = Column(name = "contact_title")),
        AttributeOverride(name = "email", column = Column(name = "contact_email")),
        AttributeOverride(name = "contactNumber", column = Column(name = "contact_number")),
        AttributeOverride(name = "country", column = Column(name = "contact_country")),
        AttributeOverride(name = "occupation", column = Column(name = "contact_occupation")),
        AttributeOverride(name = "companyName", column = Column(name = "company_name")),
    )
    var personalDetails: CaseContactInformation? = null

    var actionFor: String?=null

    var archive: Boolean = false

    @JsonIgnore
    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL])
    @JoinColumn(name = "case_id")
    var notes: List<CaseNotesEntity>?=null

    var notifyCaseOwner: Boolean = true

    var notifyApplicant: Boolean = false

    var uuid: String? = null

    var feeToken: String? = null

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "comments", column = Column(name = "case_fees_comments")),
        AttributeOverride(name = "approverEmails", column = Column(name = "case_fees_approver_emails")),
        AttributeOverride(name = "needApproval", column = Column(name = "case_fees_need_approval")),
        AttributeOverride(name = "isApproved", column = Column(name = "case_fees_is_approved")),
        AttributeOverride(name = "feesCreationDate", column = Column(name = "case_fees_creation_date")),
    )
    var caseFees: CaseFees? = null

    var percentCompletion: Int = 0

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id")
    var account: AccountEntity? = null

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "linked_cases", joinColumns = [JoinColumn(name = "case_id")], inverseJoinColumns = [JoinColumn(name = "linked_case_id")])
    @JsonIgnore
    val linkedCases: MutableList<CaseEntity> = mutableListOf()

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "linked_cases", joinColumns = [JoinColumn(name = "linked_case_id")], inverseJoinColumns = [JoinColumn(name = "case_id")])
    @JsonIgnore
    val linkedByCases: MutableList<CaseEntity> = mutableListOf()

    @JsonIgnore
    var dealId: Long? = null

    var partnerId: Long?= null

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var cgRequestedStatus: CGRequestStatus? = null

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var accountManagerUserType: PartnerCaseType? = null

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var trackingType: TrackingType? = TrackingType.NOTRACK

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var managedBy: PartnerCaseType = PartnerCaseType.CG

    var sendUpdateReminder: Boolean = false

    var assessmentId: Long? = null

    var isLegacy: Boolean = false

    @ManyToOne
    @JoinColumn(name = "case_form_id")
    @JsonIgnore
    var caseForm: CaseFormEntity? = null

    @PrePersist
    fun onCaseCreate() {
        initiatedDate = createdDate
    }

}