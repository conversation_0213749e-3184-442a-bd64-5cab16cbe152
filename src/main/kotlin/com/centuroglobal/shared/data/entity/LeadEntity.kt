package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.view.ExpertiseView
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.exception.ApplicationException
import jakarta.persistence.*

@Entity(name = "lead_request")
data class LeadEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var title: String,

    @Column(columnDefinition = "text", nullable = false)
    var description: String,

    @Column(nullable = false)
    var expertiseId: Int? = null,

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "lead_expertise",
        joinColumns = [JoinColumn(name = "lead_id")],
        inverseJoinColumns = [JoinColumn(name = "expertise_id")]
    )
    var expertises: List<ExpertiseView> = mutableListOf(),

    @Column(nullable = false)
    var countryCode: String,

    var regionId: Int?,

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var status: LeadStatus,

    var responseCount: Int = 0,

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var leadType: LeadType,

    @Column(nullable = true)
    var leadTypeId: Long? = null,

    @Column(nullable = false)
    var createdBy: Long,

    override var lastUpdatedBy: Long,

    @OneToMany(
        fetch = FetchType.LAZY,
        cascade = [CascadeType.ALL],
        orphanRemoval = true
    )
    @JoinColumn(name = "lead_id")
    var responses: MutableList<LeadResponseEntity> = mutableListOf()

) : AuditByBaseEntity(lastUpdatedBy) {

    fun addResponse(response: LeadResponseEntity) {
        response.lead = this
        this.responses.add(response)
        this.responseCount = this.responses.size
    }

    fun updateResponse(expertId: Long, description: String, userId: Long) {
        val existingResponse = this.responses.find { it.expertId == expertId }
            ?: throw ApplicationException(ErrorCode.LEAD_RESPONSE_UPDATE_NO_EXIST)
        existingResponse.description = description
        existingResponse.lastUpdatedBy = userId
    }
}
