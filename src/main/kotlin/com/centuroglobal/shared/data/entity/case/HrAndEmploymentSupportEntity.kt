package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "hr_and_employment_support")
data class HrAndEmploymentSupportEntity(

    var caseCountry: String,

    var entityInJurisdiction: Boolean = false,

    var contractsSupport: Boolean = false,

    var policySupport: Boolean = false,

    var liabilityInsurance: Boolean = false,

    var employmentSupport: Boolean = false

) : CaseEntity()