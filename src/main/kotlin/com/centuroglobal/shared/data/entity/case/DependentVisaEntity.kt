package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "dependent_visa")
data class DependentVisaEntity (

    var firstName: String,

    var middleName: String?,

    var lastName: String,

    var relationWithPrimary: String,

    var nationality: String,

    var residenceCountry: String?,

    var contactNo: String?,

    var emailAddress: String?,

    var shareApplicantInfo: Boolean?,

    var applicantStayType: String?,

    var applicantStay: Long?,

    var travelPurpose: String?,

    var childAge: Long?,

    var travelCountry: String,

    var visaIssueDate: Long? = null,

    var visaExpiryDate: Long? = null,

    var visaType: String ? = null,

    var visaRenewalDate: Long?=null

): CaseEntity()