package com.centuroglobal.shared.data.entity.stripe

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import com.centuroglobal.shared.data.enums.stripe.StripeTransactionStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "stripe_subscription")
data class StripeSubscriptionEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stripe_account_id")
    val stripeAccount: StripeAccountEntity,

    @Column
    var paymentMethodId: String?,

    @Column(updatable = false)
    val subscriptionId: String? = null,

    @Column(updatable = false)
    val productId: String? = null,

    @Column
    var priceId: String? = null,

    @Column
    var subscriptionActive: Boolean,

    @Column(scale = 2)
    var subscriptionAmount: BigDecimal,

    @Column
    var currentPeriodStartDate: LocalDateTime,

    @Column
    var currentPeriodEndDate: LocalDateTime,

    @Column
    var latestInvoiceId: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var latestInvoiceStatus: StripeTransactionStatus,

    @Column(name = "cancel_at_period_end")
    var cancelAtPeriodEnd: Boolean? = false,

    @Column
    var cancelAt: LocalDateTime? = null,

    @Column
    var cancelledAt: LocalDateTime? = null

) : AuditBaseEntity()
