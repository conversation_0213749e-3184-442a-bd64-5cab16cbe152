package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "usage_corporate_doc_upload")
data class CorporateDocUploadUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var name: String,

    var country: String,

    var userId: Long,

    var bandName: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    var expiry: LocalDateTime?,

    var documentType: String?,

    var documentName: String?,

    var fileSize: Long?,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()