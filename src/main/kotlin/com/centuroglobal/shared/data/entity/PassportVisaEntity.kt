package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.*
import jakarta.persistence.Id

@Entity(name = "passport_visa")
data class PassportVisaEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long?=null,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity ,

    var birthPlace: String? = null,

    var birthDate: String? = null,

    var issuePlace: String? = null,

    var issueDate: String? = null,

    var nationality: String? = null,

    var gender: String? = null,

    var expiryDate: String? = null,

    var docType: String? = null,

    var visaType: String? = null,

    var s3key: String? = null,

    var clientDocId: Long? = 0
): AuditUserBaseEntity()