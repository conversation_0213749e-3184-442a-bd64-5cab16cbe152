package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.attributeConverter.StringListAttributeConverter
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.PartnerType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "partner")
data class PartnerEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(unique = true, nullable = false)
    var name: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var createdFrom: PartnerType,

    @Column(nullable = false)
    var contractFromDate: LocalDateTime,

    @Column(nullable = false)
    var contractToDate:LocalDateTime,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var casesManaged: PartnerCaseType,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var queriesManaged: PartnerCaseType,

    @Column
    var companyLogo: String? = null,

    var themePrimaryColor: String? = null,

    var themeSecondaryColor: String? = null,

    var rootUserId: Long? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: CorporateStatus,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL], mappedBy =  "partner")
    var corporates: List<CorporateEntity> = mutableListOf(),

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL], mappedBy =  "partner")
    var partnerUsers: MutableList<LoginAccountEntity> = mutableListOf(),

    var country : String?,
    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "band_id")
    var band: BandsEntity?,

    var referenceId:Long?,

    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "partner_experts",
        joinColumns = [JoinColumn(name = "partner_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "ecp_id", referencedColumnName = "id")]
    )
    var associatedCompanies: MutableList<ExpertCompanyProfileEntity> = mutableListOf(),

    var corporateAccess: String

): AuditUserBaseEntity()