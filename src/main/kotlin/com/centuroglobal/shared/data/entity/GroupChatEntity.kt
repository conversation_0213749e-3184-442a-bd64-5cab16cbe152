package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.ChatType
import jakarta.persistence.*

@Entity(name = "group_chat")
data class GroupChatEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var chatType: ChatType,

    var referenceId: Long,

    @OneToMany(mappedBy = "groupChat", cascade = [CascadeType.REMOVE])
    var participants: List<GroupChatParticipantEntity> = mutableListOf()

): AuditUserBaseEntity()