package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "referral_code")
data class ReferralCodeEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var referralCode: String,

    var expiry: LocalDateTime?,

    var rootUserId: Long,

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)
