package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.ValidationState
import com.centuroglobal.shared.data.enums.ValidationType
import com.centuroglobal.shared.data.entity.AuditBaseEntity
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "validation_token")
data class ValidationTokenEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(updatable = false)
    val userId: Long,

    @Column(updatable = true, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var type: ValidationType,

    @Column(updatable = false)
    val code: String,

    @Column(updatable = true, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var state: ValidationState,

    @Column(nullable = false)
    var expiryDate: LocalDateTime

) : AuditBaseEntity()