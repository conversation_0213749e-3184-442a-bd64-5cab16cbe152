package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.OneToMany

@Entity(name = "multiple_immigration_visa")
data class MultipleVisaEntity(

    var visaType: String? = null,
    var caseCountry: String,
    var numberVisaPermit: Long? = null,

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, mappedBy = "visaEntity", fetch = FetchType.EAGER)
    var traveller: MutableList<TravellerEntity> = mutableListOf(),

    var visaIssueDate: Long?=null,
    var visaExpiryDate: Long?=null,
    var visaRenewalDate: Long?=null

) : CaseEntity()