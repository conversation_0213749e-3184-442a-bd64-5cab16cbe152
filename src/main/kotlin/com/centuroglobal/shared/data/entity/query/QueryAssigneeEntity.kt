package com.centuroglobal.shared.data.entity.query

import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.PartnerCaseType
import jakarta.persistence.*
import java.time.LocalDateTime


@Entity(name = "query_assignee")
@Table(name = "query_assignee")
data class QueryAssigneeEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "query_id")
    var query: QueryEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var userType: PartnerCaseType?,

    var assignedDate: LocalDateTime? = null

    ) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as QueryAssigneeEntity

        return user == other.user
    }

    override fun hashCode(): Int {
        return user.hashCode()
    }
}
