package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "usage_ask_ai")
data class AskAiUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var name: String,

    var country: String,

    var userId: Long,

    var bandName: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()