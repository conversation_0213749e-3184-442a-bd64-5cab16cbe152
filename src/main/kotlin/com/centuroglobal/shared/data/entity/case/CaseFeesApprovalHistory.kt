package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "case_fees_approval_history")
data class CaseFeesApprovalHistory @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "comments", column = Column(name = "case_fees_comments")),
        AttributeOverride(name = "approverEmails", column = Column(name = "case_fees_approver_emails")),
        AttributeOverride(name = "needApproval", column = Column(name = "case_fees_need_approval")),
        AttributeOverride(name = "isApproved", column = Column(name = "case_fees_is_approved")),
    )
    var caseFees: CaseFees? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "case_id", nullable = false)
    @JsonIgnore
    var case: CaseEntity

) : AuditBaseEntity()