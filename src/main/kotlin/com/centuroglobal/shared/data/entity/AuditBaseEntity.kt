package com.centuroglobal.shared.data.entity

import jakarta.persistence.Column
import jakarta.persistence.MappedSuperclass
import jakarta.persistence.PrePersist
import jakarta.persistence.PreUpdate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@MappedSuperclass
abstract class AuditByBaseEntity (open var lastUpdatedBy: Long) : AuditBaseEntity()

@MappedSuperclass
abstract class AuditBaseEntity {

    @Column(updatable = false)
    var createdDate: LocalDateTime=LocalDateTime.now()

    var lastUpdatedDate: LocalDateTime=createdDate

    @PrePersist
    fun onCreate() {
        createdDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        lastUpdatedDate = createdDate
    }

    @PreUpdate
    fun onUpdate() {
        lastUpdatedDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
    }
}
