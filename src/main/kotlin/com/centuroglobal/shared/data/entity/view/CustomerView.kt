package com.centuroglobal.shared.data.entity.view

import jakarta.persistence.Entity
import jakarta.persistence.Id
import org.hibernate.annotations.Subselect

@Entity
@Subselect(
    """
        SELECT a.* from 
        (
          SELECT la.id,concat_ws(' ',la.first_name,la.last_name) as display_name,
       la.profile_photo_url as profile_photo_url,
       IF(la.user_type = 'EXPERT',eu.job_title,cu.job_title) as job_title,
       IF(la.user_type = 'EXPERT',eu.country_code,c.country_code) as country_code,
       cr.name as country_region,
       IF(la.user_type = 'EXPERT',ecp.name,c.name) as company_name
       from login_account la
                  LEFT JOIN expert_user eu ON eu.id = la.id
                  LEFT JOIN expert_company_profile ecp ON ecp.id = eu.company_profile_id
                  LEFT JOIN corporate_user cu ON cu.id = la.id
                  LEFT JOIN corporate c ON c.id = cu.corporate_id AND c.root_user_id = cu.id
                  LEFT JOIN country_region cr ON cr.id = eu.country_region_id
where la.user_type in ('EXPERT','CORPORATE')
        ) as a
    """
)
data class CustomerView(
    @Id
    val id: Long,

    val displayName: String,

    val profilePhotoUrl: String?,

    val jobTitle: String,

    val countryCode: String,

    val countryRegion: String?,

    val companyName: String

)
