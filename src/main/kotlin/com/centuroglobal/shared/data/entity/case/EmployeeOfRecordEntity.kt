package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.OneToMany
import java.time.LocalDateTime

@Entity(name = "employee_of_record")
data class EmployeeOfRecordEntity (

    var destinationCountry: String?,

    var entityInDestinationCountry: String?,

    var hiringDate: LocalDateTime,

    var rightToWork: String,

    var signContract: String,

    var employmentTypes: String,

    var internationalTravel: String,

    var labourSafety: String,

    var certificationRequired: String,

    var governmentContract: String,

    @OneToMany(
        cascade = [CascadeType.ALL],
        orphanRemoval = true,
        mappedBy = "employeeOfRecordEntity",
        fetch = FetchType.EAGER
    )
    var applicants: MutableList<EmployeeRecordApplicantEntity> = mutableListOf()


): CaseEntity()