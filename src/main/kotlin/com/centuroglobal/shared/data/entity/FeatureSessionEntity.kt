package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "feature_sessions")
data class FeatureSessionEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var referenceId: String,

    var startTime: LocalDateTime,

    var endTime: LocalDateTime? = null,

    var sessionId: String,

    var referenceType: String,

    var type: String? = null

): AuditUserBaseEntity()