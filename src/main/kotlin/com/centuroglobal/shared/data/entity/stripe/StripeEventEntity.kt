package com.centuroglobal.shared.data.entity.stripe

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import com.centuroglobal.shared.data.enums.stripe.StripeEventStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventType
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "stripe_event")
data class StripeEventEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(updatable = false)
    val eventId: String,

    @Column(updatable = false)
    val customerId: String,

    @Column(updatable = false, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    val type: StripeEventType,

    @Column(columnDefinition = "text", updatable = false)
    val response: String,

    @Column(updatable = true, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var status: StripeEventStatus,

    @Column(updatable = false)
    val stripeLoggedDate: LocalDateTime? = null,

    @Column(updatable = false)
    val apiVersion: String? = null

) : AuditBaseEntity()
