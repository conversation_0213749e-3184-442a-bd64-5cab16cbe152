package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.data.enums.UserType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime


@Entity(name = "login_account")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "user_type")
abstract class LoginAccountEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    open var id: Long? = null,

    open var email: String,

    open var firstName: String,

    open var lastName: String,

    open var password: String,

    open var tempPassword: String? = null,

    open var oneTimePassword: String? = null,

    open var passwordCreationDate: LocalDateTime? = null,

    open var refreshToken: String? = null,

    open var loginFailCount: Int? = 0,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var status: AccountStatus,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var role: Role,

    override var lastUpdatedBy: Long,

    open var tour: Boolean,

    open var profilePhotoUrl: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var subscriptionType: SubscriptionType? = SubscriptionType.FREE,

    open var onboard: Boolean = false,

    open var isLinkedin: Boolean = false,

    open var referredBy: Long? = null,

    open var tncView: Boolean = false,

    open var lastTermsViewDate: LocalDateTime? = null,

    open var lastLoginDate: LocalDateTime? = null,

    open var countryCode: String? =null,

    open var dialCode: String? = null,

    open var contactNo: String? = null,

    open var questionsQuota: Long,

    open var loginToken: String? = null,

    @Column(name = "login_token_expiry", nullable = true)
    open var loginTokenExpire: LocalDateTime? = null,

  //  open var userType: String? =null,

    @OneToMany(fetch = FetchType.EAGER,cascade = [CascadeType.ALL], mappedBy = "user")
    //@JoinColumn(name = "id")
    var userRoles: MutableList<UserRoleEntity> = mutableListOf(),

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "partner_id")
    var partner: PartnerEntity? = null,

    var partnerJobTitle: String? = null,

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_band_id")
    var partnerBand: BandsEntity? = null,

    var firstLoginDate: LocalDateTime? = null,

    var joinedDate: LocalDateTime? = null,

    var showOnboardingDashboard: Boolean = true,

    var threadId: String? = null

) : AuditByBaseEntity(lastUpdatedBy) {

    constructor(): this(0, "","","","","","",
        LocalDateTime.now(),"",0,AccountStatus.ACTIVE,Role.ROLE_CORPORATE,0,
        false, null,null,false,false,0,false,
        null, null,"",
        null,null, 0, null,null)

    @PrePersist
    fun onAccountCreate() {
        passwordCreationDate = createdDate
    }

    @Transient
    fun getUserType(): UserType {
        val userType = this.javaClass.getAnnotation(DiscriminatorValue::class.java)
        return UserType.valueOf(userType.value)
    }
}