package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*


@Entity(name = "case_category")
@Table(name = "case_category")
data class CaseCategoryEntity @JvmOverloads constructor(

    @Id
    var id: Long? = null,

    @Column(nullable = false)
    var subCategoryId: String,

    @Column(nullable = false)
    var subCategoryName: String,

    @Column(nullable = false)
    var parentCategoryId: String,

    @Column(nullable = false)
    var parentCategoryName: String
    
) : AuditBaseEntity()
