//package com.centuroglobal.shared.data.entity
//
//import jakarta.persistence.*
//
//@Entity(name = "case_category")
//@Table(name = "case_category")
//data class CaseCategory @JvmOverloads constructor(
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    var id: Int? = null,
//    val stepId: String,
//    val prefix: String,
//    val displayName: String,
//    val allowChart: Boolean,
//    val order: Int,
//    val isTeaser: Boolean,
//    val menuName: String,
//    val isDisplay: Boolean = true,
//    val caseCode: String,
//    val caseTitle: String,
//    val description: String
//){
//}