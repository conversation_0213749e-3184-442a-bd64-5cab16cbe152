package com.centuroglobal.shared.data.entity.subscription

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "work_log")
data class WorkLogEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "corporate_id")
    var corporate: CorporateEntity,

    var description: String,

    var referenceId: Long?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var referenceType: ReferenceType,

    var eventDate: LocalDateTime,

    var timeSpent: Long,

    var isDeleted: Boolean = false

) : AuditUserBaseEntity()