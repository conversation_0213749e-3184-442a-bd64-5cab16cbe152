package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id

@Entity(name = "immigration_requirement")
data class ImmigrationRequirementEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var sourceCountry: String?,
    var destCountry: String?,
    var businessVisa: String?,
    var workVisa: String?,
    var eVisa: String? = null,
    var validDays: Long? = null
)