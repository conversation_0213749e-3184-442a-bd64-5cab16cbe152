package com.centuroglobal.shared.data.entity.view


import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.CaseCategoryEntity
import com.centuroglobal.shared.data.entity.CaseFormEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime

@Entity
@Subselect(
    value = """
    select c.*, p.name as partner_name from cases c left join partner p on c.partner_id=p.id
    """
)
data class CaseView(
    @Id
    var id: Long? = null,

    val initiatedDate: LocalDateTime,

    val startDate: LocalDateTime? = null,

    var country: String? = null,

    var companyName: String? = null,

    var initiatedFor: String? = null,

    var email: String? = null,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id")
    var category: CaseCategoryEntity? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", nullable = false)
    var createdBy: ClientView? = null,

    @ManyToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL])
    @JoinTable(
        name = "case_assignee",
        joinColumns = [JoinColumn(name = "case_id")],
        inverseJoinColumns = [JoinColumn(name = "expert_id")]
    )
    var assignee: MutableList<UserView>? = mutableListOf(),

    var status: String = CaseStatus.NOT_STARTED.name,

    var statusUpdate: String? = null,

    var isPriorityCase: Boolean = false,

    var parentCategoryId: String,

    var actionFor: String? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_manager", nullable = true)
    var accountManager: LoginAccountEntity? = null,

    var archive: Boolean,

    var notifyCaseOwner: Boolean,

    var notes: String?=null,

    var lastUpdatedDate: LocalDateTime? = null,

    var percentCompletion: Int = 0,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id")
    var account: AccountEntity? = null,

    var aliasName: String? =null,

    var partnerId: Long?= null,

    var cgRequestedStatus: String? = null,

    var partnerName: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var managedBy: PartnerCaseType? = null,

    @ManyToOne
    @JoinColumn(name = "case_form_id")
    @JsonIgnore
    var caseForm: CaseFormEntity? = null
)