package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import jakarta.persistence.*

@Entity(name = "step")
data class BlueprintStepEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blueprint_country_code", nullable = false)
    var blueprint: BlueprintEntity?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    val stepName: StepName,

    @Column(columnDefinition = "mediumtext")
    var content: String,

    @Column(columnDefinition = "mediumtext")
    var contentDraft: String,

    override var lastUpdatedBy: Long,

    @Column(nullable = false)
    var showChart: Boolean,

    var startAt: Int? = null,

    var duration: Int? = null

) : AuditByBaseEntity(lastUpdatedBy)
