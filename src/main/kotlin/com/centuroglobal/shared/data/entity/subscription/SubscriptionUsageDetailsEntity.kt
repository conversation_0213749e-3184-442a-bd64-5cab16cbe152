package com.centuroglobal.shared.data.entity.subscription

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "subscription_usage_details")
data class SubscriptionUsageDetailsEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    var code: String,

    var threshold: Long,

    var unit: String,

    var overageRate: Float,

    var trackingDuration: String,

    var isUnlimited: Boolean = false,

    var overage: Long,

    var overageCharge: Float = 0F,

    var date: LocalDateTime,

    var totalUsage: Long?,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "subscription_id")
    var plan: SubscriptionUsageEntity

) : AuditUserBaseEntity()