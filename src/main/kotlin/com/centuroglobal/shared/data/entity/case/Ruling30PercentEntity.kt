package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "ruling_30_percent")
data class Ruling30PercentEntity(

    var destinationCountry:String,

    var firstName: String,

    var lastName: String,

    var citizenCountry: String,

    var residenceCountry: String,

    var emailAddress: String,

    var bsn: String?,

    var jobTitle: String,

    var jobStartDate: Long

) : CaseEntity()