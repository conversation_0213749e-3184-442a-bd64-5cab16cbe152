package com.centuroglobal.shared.data.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "group_chat_participants")
data class GroupChatParticipantEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "chat_id")
    var groupChat: GroupChatEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity,

    var isActive: Boolean = true,

    var lastSeen: LocalDateTime? = null,

    var isReal: Boolean = true
)