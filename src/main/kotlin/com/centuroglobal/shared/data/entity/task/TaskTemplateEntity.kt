package com.centuroglobal.shared.data.entity.task

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.TaskEntity
import jakarta.persistence.*

@Entity(name = "task_template")
data class TaskTemplateEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    var visibility: String,

    var expectedTimeline: Long,

    var expectedTimelineUnit: String,

    var progress: String,

    var caseStatus: String,

    var caseMilestone: String,

    var description: String?,

    var assignedTo: String,

    var instruction: String,

    var usefulLinks: String?,

    var priority: String?,

    var displayOrder: Long?,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_id")
    var workflow: TaskWorkflowEntity? = null,

    @OneToOne(cascade = [CascadeType.REMOVE])
    @JoinColumn(name = "task_id")
    var task: TaskEntity? = null

) : AuditUserBaseEntity()
