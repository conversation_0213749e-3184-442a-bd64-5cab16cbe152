package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "case_status_master")
@Table(name = "case_status_master")
data class CaseStatusMasterEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var subCategory: String,

    var status: String,

    var statusDisplayText: String,

    var percentage: Long,

    val actionFor: String,

    val dealStatusId: Long?,

    var showStatus: Boolean
)