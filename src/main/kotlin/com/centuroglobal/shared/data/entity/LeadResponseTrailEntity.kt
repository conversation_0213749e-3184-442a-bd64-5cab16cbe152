package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.LeadResponseEntity
import jakarta.persistence.*

@Entity(name = "lead_response_trail")
data class LeadResponseTrailEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lead_response_id", nullable = false)
    val leadResponse: LeadResponseEntity,

    @Column(columnDefinition = "text")
    var message: String?,

    val rootUserId: Long,

    val assetUrl: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    val respondedBy: LeadType,

    var isSeen: Boolean = false,

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)
