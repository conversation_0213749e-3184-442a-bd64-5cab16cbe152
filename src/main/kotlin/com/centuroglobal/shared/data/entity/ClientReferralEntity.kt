package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import jakarta.persistence.*

@Entity(name = "client_referral")
data class ClientReferralEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var title: String,

    var clientName: String,

    var clientCountryCode: String,

    var clientEmail: String?,

    var clientCompanyName: String?,

    var clientContactNumber: String?,

    @Column(columnDefinition = "TEXT")
    var description: String,

    var dealCurrency: String?,

    var dealValue: String?,

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var status: LeadStatus = LeadStatus.ACTIVE,

    @ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinTable(
        name = "client_referral_experts",
        joinColumns = [JoinColumn(name = "referral_id")],
        inverseJoinColumns = [JoinColumn(name = "expert_id")]
    )
    var experts: MutableList<ExpertUserEntity> = mutableListOf(),

    @ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinTable(
        name = "client_referral_read",
        joinColumns = [JoinColumn(name = "referral_id")],
        inverseJoinColumns = [JoinColumn(name = "expert_id")]
    )
    var readBy: MutableList<ExpertUserEntity> = mutableListOf(),

    var createdBy: Long,

    override var lastUpdatedBy: Long

) : AuditByBaseEntity(lastUpdatedBy)