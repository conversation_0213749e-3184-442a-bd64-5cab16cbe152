package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.LeadNotificationState
import com.centuroglobal.shared.data.enums.LeadNotificationType
import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*

@Entity(name = "lead_notification")
data class LeadNotificationEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(updatable = false, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    val type: LeadNotificationType,

    @Column(updatable = true, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var state: LeadNotificationState,

    @Column(updatable = false)
    val leadId: Long,

    @Column(updatable = false)
    val userId: Long

) : AuditBaseEntity()
