package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "tax_support")
data class TaxSupportEntity(

    var taxSupport: String? = null,
    var caseCountry: String,
    var estimatedRevenue: String? = null,
    var payroll: String? = null,
    var employeeBenefits: String? = null,
    var benefitsTaxable: String? = null

) : CaseEntity()