package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.attributeConverter.StringListAttributeConverter
import com.centuroglobal.shared.data.entity.CircleMemberEntity
import com.centuroglobal.shared.data.entity.CircleRequestEntity
import com.centuroglobal.shared.data.entity.CircleResponseTrailEntity
import com.centuroglobal.shared.data.entity.view.ExpertiseView
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.fasterxml.jackson.annotation.JsonIgnore
import org.hibernate.annotations.SQLDelete
import org.hibernate.annotations.Where
import jakarta.persistence.*

@Entity(name = "circle")
@Table(name = "circle")
@SQLDelete(sql = "UPDATE circle SET is_active = false WHERE id = ?")
@Where(clause = "is_active = true")
data class CircleEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var name: String,

    @Column(columnDefinition = "text", nullable = false)
    var about: String,

    var bannerPhotoKey: String? = null,

    @Column(nullable = false)
    @Convert(converter = StringListAttributeConverter::class)
    var countryCodes: List<String>? = mutableListOf(),

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var status: CircleStatus,

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var circleType: CircleType,

    @JsonIgnore
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "circle_expertise",
        joinColumns = [JoinColumn(name = "circle_id")],
        inverseJoinColumns = [JoinColumn(name = "expertise_id")]
    )
    var expertises: List<ExpertiseView> = mutableListOf(),

    @Column(nullable = false)
    var createdBy: Long,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "circle", cascade = [CascadeType.ALL])
    var members: List<CircleMemberEntity> = mutableListOf(),

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "circle")
    var requests: List<CircleRequestEntity> = mutableListOf(),

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "circle")
    var circleResponseTrails: List<CircleResponseTrailEntity> = mutableListOf(),

    @Transient
    var unReadMessageCount: Int = 0,

    @Transient
    var lastMessageDateTime: Long = 0,

    override var lastUpdatedBy: Long

) : AuditByBaseEntity(lastUpdatedBy)
