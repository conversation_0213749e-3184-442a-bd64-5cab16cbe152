package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "data_protection")
data class DataProtectionEntity(

    var caseCountry: String,

    var dataProtection: Boolean = false,

    var dataOfficer: Boolean = false,

    var dataController: <PERSON>olean = false,

    var dataCompliance: Boolean = false,
    
    var dataPolicies: Boolean = false

) : CaseEntity()