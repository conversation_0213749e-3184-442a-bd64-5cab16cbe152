package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "bank_account")
data class BankAccountEntity(

    var caseCountry: String,
    var entityType: String,
    var trading: Boolean = false,
    var businessActivity: String? = null,
    var fullTime: Long? = null,
    var partTime: Long? = null,
    var nonContractWorkers: Long? = null,
    var expectedTurnover: String? = null,
    var taxStatus: String? = null,
    var incomeCome: String? = null

) : CaseEntity()