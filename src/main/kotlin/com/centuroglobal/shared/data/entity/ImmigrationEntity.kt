package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.ImmigrationRequest
import java.time.LocalDate
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "immigration")
@DiscriminatorValue("immigration")
data class ImmigrationEntity(

    @Column(nullable = false)
    var applicantNationality: String,

    @Column(nullable = false)
    var applyingFromCountry: String,

    @Column(nullable = false)
    var travellingToCountry: String,

    @Column(nullable = false)
    var purposeOfTrip: String,

    var dateOfEntry: LocalDate? = null,

    var noOfTimesEnterIntoTheCountry: String? = null,

    var expectedVisaPeriod: String? = null,

    var fullNameOfTraveller: String? = null,

    var companyName: String? = null,

    var companyAddress: String? = null,

    var contactName: String? = null,

    var contactEmail: String? = null,

    var anyDependants: String? = null,

    var noOfDependants: String? = null,

    var otherDetails: String? = null,

    @Column(name = "is_covid_vaccinated")
    var covidVaccinated: String? = null,

    var vaccineName: String? = null,

    var otherPurpose: String? = null,

    var otherVaccine: String? = null,

    var detailAbout: String? = null

) : CaseEntity() {

    object ModelMapper {
        fun from(request: ImmigrationRequest): ImmigrationEntity {
            return ImmigrationEntity(
                applicantNationality = request.applicantNationality,
                applyingFromCountry = request.applyingFromCountry,
                travellingToCountry = request.travellingToCountry,
                purposeOfTrip = request.purposeOfTrip,
                dateOfEntry = request.dateOfEntry,
                noOfTimesEnterIntoTheCountry = request.noOfTimesEnterIntoTheCountry,
                expectedVisaPeriod = request.expectedVisaPeriod,
                fullNameOfTraveller = request.fullNameOfTraveller,
                companyName = request.companyName,
                companyAddress = request.companyAddress,
                contactName = request.contactName,
                contactEmail = request.contactEmail,
                anyDependants = request.anyDependants,
                noOfDependants = request.noOfDependants,
                otherDetails = request.otherDetails,
                covidVaccinated = request.covidVaccinated,
                vaccineName = request.vaccineName,
                otherPurpose = request.otherPurpose,
                otherVaccine = request.otherVaccine,
                detailAbout = request.detailAbout
            )
        }
    }
}

