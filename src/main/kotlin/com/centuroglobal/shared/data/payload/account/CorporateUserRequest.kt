package com.centuroglobal.shared.data.payload.account

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateUserRequest(
    val email: String,
    val firstName: String?,
    val lastName: String?,
    val jobTitle: String?,
    val corporateId: Long?,
    val referralCode: String?,
    val keepMeInformed: Boolean = false,
    val bandId: Long?,
    val accounts: List<Long>,
    val managerUserIds: List<Long>?,
    val countryCode: String,
    val dialCode: String ?= null,
    val contactNo: String ?= null,
    val isDraft: Boolean = false
)