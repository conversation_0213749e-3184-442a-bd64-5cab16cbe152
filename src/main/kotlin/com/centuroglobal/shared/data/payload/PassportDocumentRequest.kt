package com.centuroglobal.shared.data.payload

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty

data class PassportDocumentRequest (
     val birthDate: String,
     val birthPlace: String,
     val nationality: String,
     val issueDate: String,
     val issuePlace: String,
     val expiryDate: String,
     val gender: String,
     val docFiles: MutableList<UserDocumentFileRequest>?,
     val userId: Long
)
