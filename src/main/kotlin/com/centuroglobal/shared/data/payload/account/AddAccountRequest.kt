package com.centuroglobal.shared.data.payload.account

import com.centuroglobal.shared.data.enums.AccountStatus
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class AddAccountRequest (
    var id: Long? =null,

    var name: String,

    var description: String? = null,

    var companyName: String? = null,

    var status: AccountStatus,

    var corporateId: Long? = null
)