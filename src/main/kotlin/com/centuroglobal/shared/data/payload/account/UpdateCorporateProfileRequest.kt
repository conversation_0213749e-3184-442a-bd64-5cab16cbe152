package com.centuroglobal.shared.data.payload.account

import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern

data class UpdateCorporateProfileRequest(
    @field:Valid
    val corporateInfo: UpdateCorporateInfoRequest?,

    @Schema
    @field:Valid
    val status: UpdateCorporateStatusRequest?,

    @field:Valid
    val userInfo: CorporateUserInfoRequest?
)

data class UpdateCorporateInfoRequest(
    @Schema(required = true)
    val firstName: String,

    @Schema( required = true)
    val lastName: String,

    @Schema( required = true)
    val jobTitle: String,

    @Schema( required = true)
    val corporateName: String,

    @field:Pattern(regexp = "[A-Za-z]{2}")
    @Schema( required = true)
    val countryCode: String,

    @Schema( required = false)
    val keepMeInformed: Boolean?,

    val dialCode: String?,

    val contactNo: String?,

    val corporateId: Long?,

    val isPrimary: Boolean = false,

    val aiMessageCount: Long = 0,

    val notificationSettings: List<NotificationSettingRequest>? = null,

    val profilePicS3Key: String?,

    val educationQualification: String?,

    val salary: String?,

    val relevantExperience: String?,

    val bandId: Long,

    val managerUserIds: List<Long>?,

    val accounts: List<Long>?,

    val email: String?
)

data class UpdateCorporateStatusRequest(
//    @field:Pattern(regexp = "^(ACTIVE|SUSPENDED)$")
    @Schema(
        description = "ADMIN ONLY - Ignored when sent by non-admin.",
        required = true,
        allowableValues = ["ACTIVE","SUSPENDED"]
    )
    val status: String
)

data class NotificationSettingRequest(
    @Schema( required = true)
    val key: NotificationType,

    @Schema( required = true)
    val value: Boolean
)

data class UpdateCompanyInfoRequest(
    @Schema( required = true)
    val companyName: String,

    @Schema( required = true)
    val countryCode: String
)