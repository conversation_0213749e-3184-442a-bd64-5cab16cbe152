package com.centuroglobal.data.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "app.password-policy")
data class PasswordPolicyProperties(
    val minLength: Int,
    val maxLength: Int,
    val requireLowercase: <PERSON><PERSON>an,
    val requireUppercase: <PERSON><PERSON><PERSON>,
    val requireDigit: <PERSON>olean,
    val requireSpecialChar: <PERSON><PERSON>an,
    val validationErrorMessage: String
)