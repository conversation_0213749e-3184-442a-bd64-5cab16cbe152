package com.centuroglobal.shared.data.payload.dashboard

import com.centuroglobal.shared.data.entity.view.CaseTrackingView
import com.centuroglobal.shared.data.enums.CaseDeadlineType
import com.centuroglobal.shared.util.TimeUtil

data class CaseTrackingData (
     val visa: String?,
     val visaIssueDate: Long?,
     val visaExpiryDate: Long?,
     val visaRenewalDate: Long?,
     val applicant: String,
     val caseId: Long,
     val date: Long?,
     val suggestion: String?,
     val notificationType: CaseDeadlineType,
     val documentName: String?,
     val countryOfTravel: String?,
     val country: String?,
     val companyName: String?
)
{
     object ModelMapper {
          fun from(caseTrackingView: CaseTrackingView) =
               CaseTrackingData(
                    visa = caseTrackingView.visaType,
                    visaIssueDate = caseTrackingView.visaIssueDate,
                    visaExpiryDate = caseTrackingView.visaExpiryDate,
                    visaRenewalDate = caseTrackingView.visaRenewalDate,
                    applicant = caseTrackingView.applicant,
                    caseId = caseTrackingView.referenceId,
                    date = caseTrackingView.date?.let { TimeUtil.toEpochMillis(it) },
                    suggestion = caseTrackingView.suggestion,
                    documentName = caseTrackingView.documentName,
                    notificationType = caseTrackingView.notificationType,
                    countryOfTravel = caseTrackingView.countryOfTravel,
                    country = caseTrackingView.country,
                    companyName = caseTrackingView.companyName
               )
     }
}
