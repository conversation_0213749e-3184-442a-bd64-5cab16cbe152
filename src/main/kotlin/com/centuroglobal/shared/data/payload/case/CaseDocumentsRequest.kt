package com.centuroglobal.shared.data.payload.case

import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Pattern

data class CaseDocumentsRequest(
    val caseId: Long,
    val documents: List<DocumentRequest>
)

data class DocumentRequest(

    val documentCode: String,

    @Pattern(regexp = "^(REQUESTED| RECEIVED| VERIFIED| APOSTILLED)$")
    @Schema(required = true, allowableValues = ["REQUESTED| RECEIVED| VERIFIED| APOSTILLED"])
    val status: String,

    var displayOrder: Long?,

    var requestedDate: Long?,

    var receivedDate: Long?,

    var verifiedDate: Long?,

    var apostilledDate: Long?,

    var expiryDate: Long?,

    var otherDocumentName: String?=null,

    var docSize: Int?
) {
    object ModelMapper {
        fun from(doc: CaseDocumentsEntity): DocumentRequest {
            return DocumentRequest(
                documentCode = doc.documentCode,
                status = doc.status,
                displayOrder = doc.displayOrder,
                requestedDate = doc.requestedDate?.let { TimeUtil.toEpochMillis(it) },
                receivedDate = doc.receivedDate?.let { TimeUtil.toEpochMillis(it) },
                verifiedDate = doc.verifiedDate?.let { TimeUtil.toEpochMillis(it) },
                apostilledDate = doc.apostilledDate?.let { TimeUtil.toEpochMillis(it) },
                otherDocumentName = doc.otherDocumentName,
                docSize = doc.caseDocumentFiles?.size,
                expiryDate = doc.expiryDate?.let { TimeUtil.toEpochMillis(it) }
            )
        }
        fun from(docList: List<CaseDocumentsEntity>): List<DocumentRequest> {
            return docList.map { from(it) }.toList()
        }
    }
}