package com.centuroglobal.shared.data.payload.account

import com.centuroglobal.shared.data.pojo.AdminAuthorities
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Pattern

data class CreateAdminUserRequest(

    @field:Email
    @Schema(required = true)
    val email: String,

    @Schema()
    val firstName: String?,

    @Schema()
    val lastName: String?,

    @field:Pattern(regexp = "^(ROLE_ADMIN|ROLE_SUPER_ADMIN)$")
    @Schema(
        required = true,
        allowableValues = ["ROLE_ADMIN", "ROLE_SUPER_ADMIN"]
    )
    val role: String,

    @Schema()
    var adminAuthorities: List<AdminAuthorities>,

    val aiMessageCount: Long = 0
)