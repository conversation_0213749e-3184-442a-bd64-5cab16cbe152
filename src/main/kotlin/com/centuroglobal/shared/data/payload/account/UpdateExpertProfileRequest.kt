package com.centuroglobal.shared.data.payload.account

import com.centuroglobal.shared.data.enums.CompanySize
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Pattern

data class UpdateExpertProfileRequest(
    @field:Valid
    val personalProfile: UpdateExpertPersonalInfoRequest?,

    @Schema()
    val companyProfile: UpdateExpertCompanyProfileRequest?,

    @field:Valid
    @Schema()
    val contactInfo: UpdateExpertContactInfoRequest?
)

data class UpdateExpertByAdminRequest(
    @field:Valid
    val personalProfile: UpdateExpertPersonalInfoRequest?,

    @field:Valid
    @Schema()
    val status: UpdateExpertStatusRequest?,

    @Schema()
    val companyProfile: UpdateExpertCompanyProfileRequest?,

    @field:Valid
    @Schema()
    val contactInfo: UpdateExpertContactInfoRequest?
)

data class UpdateExpertPersonalInfoRequest(

    @Schema(required = true)
    val firstName: String,

    @Schema( required = true)
    val lastName: String,

    @Schema( required = true)
    val jobTitle: String,

    @Schema( required = true)
    val displayName: String,

    @Schema( required = true)
    @field:Pattern(regexp = "[A-Za-z]{2}")
    val countryCode: String,

    @Schema(
        required = false,
        description = "Region Id. Required if country selected has regions defined."
    )
    val regionId: Int?,

    @Schema( required = true)
    val expertiseId: List<Int>,

    @Schema()
    val bio: String?,

    @Schema( required = false)
    val infoVideoUrl: String?,

    val profilePicS3Key: String?
)

data class UpdateExpertCompanyProfileRequest(
    @Schema(required = true)
    val name: String,

    @Schema()
    val summary: String?,

    @Schema()
    val territory: String?,

    @Schema( required = true)
    val size: CompanySize,

    val aiMessageCount: Long = 0,

    val associatedPartners: List<Long>? = null
)

data class UpdateExpertContactInfoRequest(

    @Schema()
    val contactNumber: String?,

    @field:Email
    @Schema(required = true)
    val contactEmail: String,

    @Schema()
    val contactWebsite: String?
)

data class UpdateExpertStatusRequest(
    @field:Pattern(regexp = "^(ACTIVE|SUSPENDED)$")
    @Schema(
        required = true,
        allowableValues = ["ACTIVE", "SUSPENDED"]
    )
    val status: String
)

data class UpdateExpertRequest(
    val personalProfile: UpdateExpertPersonalInfoRequest?,

    @Schema()
    val status: UpdateExpertStatusRequest?,

    @Schema()
    val companyProfile: UpdateExpertCompanyProfileRequest?,

    @Schema()
    val contactInfo: UpdateExpertContactInfoRequest?
) {
    object ModelMapper {
        fun fromUpdateExpertProfile(entity: UpdateExpertProfileRequest) =
            UpdateExpertRequest(
                personalProfile = entity.personalProfile,
                status = null,
                companyProfile = entity.companyProfile,
                contactInfo = entity.contactInfo
            )

        fun fromUpdateExpertByAdminRequest(entity: UpdateExpertByAdminRequest) =
            UpdateExpertRequest(
                personalProfile = entity.personalProfile,
                status = entity.status,
                companyProfile = entity.companyProfile,
                contactInfo = entity.contactInfo
            )
    }
}

