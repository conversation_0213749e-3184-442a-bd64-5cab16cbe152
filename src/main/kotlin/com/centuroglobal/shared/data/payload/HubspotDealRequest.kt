package com.centuroglobal.shared.data.payload

class HubspotDealRequest {
    var properties: DealProperties? = null
    var associations: List<Association>? = null
}

class DealProperties {
    var dealname: String? = null
    var applicant_name: String? = null
    var case_category: String? = null
    var case_id_unique: Long? = null
    var case_type: String? = null
    var dealstage: Long? = null
    var pipeline: Long? = null
    var consular_fee: String? = null
    var other_third_party_fees: String? = null
    var professional_fees: String? = null
    var translation: String? = null
    var deal_currency_code: String? = null
    var amount: Double? = null

}

class Association {
    var to: AssociationTo? = null
    var types: List<AssociationType>? = null
}

class AssociationTo {
    var id: String? = null
}

class AssociationType {
    var associationCategory: String? = null
    var associationTypeId: Long? = null
}