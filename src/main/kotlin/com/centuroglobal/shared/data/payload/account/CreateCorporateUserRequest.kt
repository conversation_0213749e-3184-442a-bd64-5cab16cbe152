package com.centuroglobal.shared.data.payload.account

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateCorporateUserRequest(
    val email: String,
    val firstName: String?,
    val lastName: String?,
    val jobTitle: String?,
    val corporateId: Long?,
    val referralCode: String?,
    val keepMeInformed: Boolean = false,
    val countryCode: String
)