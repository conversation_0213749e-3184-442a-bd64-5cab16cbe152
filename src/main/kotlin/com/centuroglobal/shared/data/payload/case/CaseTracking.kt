package com.centuroglobal.shared.data.payload.case

import com.centuroglobal.shared.data.entity.case.ReminderEntity
import com.centuroglobal.shared.data.enums.CaseDeadlineType
import com.centuroglobal.shared.data.enums.CaseTrackType
import com.centuroglobal.shared.data.enums.TrackingType
import com.centuroglobal.shared.util.TimeUtil

data class CaseTracking (

    val trackingType: TrackingType,
    val reminders: List<CaseReminder>?,
    val visaExpiry: Long?,
    val visaRenewalDeadline: Long?
)

data class CaseReminder (
    val trackType: CaseTrackType,
    val deadline: Long?,
    val deadlineUnit: String?,
    val date: Long?,
    val deadlineAgainst: String? = CaseDeadlineType.VISA_EXPIRY_REMINDER.name,
    val suggestion: String?
) {
    object ModelMapper {
        fun from(entity: ReminderEntity) =
            CaseReminder(
                trackType = entity.trackType,
                deadline = entity.deadline,
                deadlineUnit = entity.deadlineUnit?.name,
                date = entity.date?.let { TimeUtil.toEpochMillis(it) },
                deadlineAgainst = entity.deadlineAgainst!!.name,
                suggestion = entity.suggestion
            )
    }
}