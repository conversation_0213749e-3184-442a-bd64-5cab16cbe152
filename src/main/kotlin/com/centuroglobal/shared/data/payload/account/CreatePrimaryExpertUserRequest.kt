package com.centuroglobal.shared.data.payload.account

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email

data class CreatePrimaryExpertUserRequest(

    @field:Email
    @Schema(required = true)
    val email: String,

    @Schema()
    val firstName: String?,

    @Schema()
    val lastName: String?,

    @Schema()
    val jobTitle: String?,

    @Schema()
    val countryCode: String?,

    @Schema()
    val profileImage:String? = null,

    @Schema()
    val companyProfile: CompanyRequest,

    val aiMessageCount: Long = 0
)