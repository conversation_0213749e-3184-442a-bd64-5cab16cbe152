package com.centuroglobal.shared.data.payload.case

import java.time.LocalDate


data class ImmigrationRequest(

    var id: Long? = null,
    var formType: String,
    val applicantNationality: String,
    val applyingFromCountry: String,
    val travellingToCountry: String,
    val purposeOfTrip: String,
    var dateOfEntry: LocalDate? = null,
    var noOfTimesEnterIntoTheCountry: String? = null,
    var expectedVisaPeriod: String? = null,
    var fullNameOfTraveller: String? = null,
    var companyName: String? = null,
    var companyAddress: String? = null,
    var contactName: String? = null,
    var contactEmail: String? = null,
    var anyDependants: String? = null,
    var noOfDependants: String? = null,
    var otherDetails: String? = null,
    var covidVaccinated: String? = null,
    var agreeTermOfUse: Boolean? = null,
    var vaccineName: String? = null,
    var otherPurpose: String? = null,
    var otherVaccine: String? = null,
    var detailAbout: String? = null,
    var country: String? = null,
    val caseOwner: Long
)