package com.centuroglobal.shared.data.payload.account

import com.centuroglobal.shared.data.payload.account.signup.AssignedTeam
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansRequest
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Pattern

data class UpdateCorporateRequest(

    @Schema( required = true)
    val corporateName: String,

    @field:Pattern(regexp = "[A-Za-z]{2}")
    @Schema( required = true)
    val countryCode: String,

    @Schema
    val aiMessageCount: Long = 0,

    @Schema
    val referralCode: String?,

    @Schema
    val assignedTeam: List<AssignedTeam>?,

    @Schema
    val onboardingDocs: List<OnboardingDocs>?,

    @Schema
    val primaryColor : String? = null,

    @Schema
    val secondaryColor: String? = null,

    @Schema
    val companyLogoId: String? = null,

    var subscriptionPlan: String? = null,

    var customSubscription: SubscriptionPlansRequest? = null,

    var subscriptionStartDate: Long? = null,

    var subscriptionEndDate: Long? = null,

    val features: List<String>? = mutableListOf(),

    val isTeamEmail: Boolean = false

    )