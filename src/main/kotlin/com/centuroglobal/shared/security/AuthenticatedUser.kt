package com.centuroglobal.shared.security

import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.pojo.UserAccess
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import io.jsonwebtoken.Claims
import org.springframework.security.core.Authentication
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.authority.AuthorityUtils
import java.time.LocalDateTime

class AuthenticatedUser(
    var firstName: String,
    var lastName: String,
    var companyId: Long? = null,
    var userId: Long,
    var displayName: String,
    var email: String,
    var role: String,
    var userType: String,
    var status: String,
    var refreshToken: String? = null,
    private var authenticated: Boolean = false,
    var onboard: Boolean,
    var subscriptionFlag: String,
    var lastLoginDate: LocalDateTime? = null,
    var countryCode: String,
    var adminAuthorities: String,
    var tempPassword: Boolean = false,
    var accesses: List<UserAccess> = listOf(),
    var visibilities: List<UserAccess> = listOf(),
    var partnerId: Long? = null,
    var loginToken: String? = null
) : Authentication {

    constructor(claims: Claims) : this(
        claims[JwtClaim.FIRST_NAME.key] as String,
        claims[JwtClaim.LAST_NAME.key] as String,
        claims[JwtClaim.COMPANY_ID.key]?.toString()?.toLong(),
        (claims[JwtClaim.USER_ID.key].toString()).toLong(),
        claims[JwtClaim.DISPLAY_NAME.key] as String,
        claims[JwtClaim.EMAIL.key] as String,
        claims[JwtClaim.ROLE.key] as String,
        claims[JwtClaim.USER_TYPE.key] as String,
        claims[JwtClaim.STATUS.key] as String,
        null,
        false,
        claims[JwtClaim.ON_BOARDED.key] as Boolean,
        claims[JwtClaim.SUBSCRIBE_FLAG.key] as String,
        claims[JwtClaim.LAST_LOGIN_DATE.key] as LocalDateTime?,
        claims[JwtClaim.COUNTRY_CODE.key] as String,
        claims[JwtClaim.ADMIN_AUTHORITIES.key] as String,
        if (claims[JwtClaim.TEMP_PASSWORD.key] != null) {
            claims[JwtClaim.TEMP_PASSWORD.key] as Boolean
        } else {
            false
        },
        if(claims[JwtClaim.USER_ACCESS.key]!=null)
            ObjectMapper().convertValue(claims[JwtClaim.USER_ACCESS.key], object: TypeReference<MutableList<UserAccess>>(){})
        else listOf<UserAccess>(),
        if(claims[JwtClaim.USER_VISIBILITY.key]!=null)
            ObjectMapper().convertValue(claims[JwtClaim.USER_VISIBILITY.key], object: TypeReference<MutableList<UserAccess>>(){})
        else listOf<UserAccess>(),
        claims[JwtClaim.PARTNER_ID.key]?.toString()?.toLong(),
        null
    )

    override fun getAuthorities(): MutableCollection<out GrantedAuthority> {
        return AuthorityUtils.commaSeparatedStringToAuthorityList(role)
    }

    override fun setAuthenticated(authenticated: Boolean) {
        this.authenticated = authenticated
    }

    override fun getName(): String? {
        return email
    }

    override fun getCredentials(): Any? {
        return null
    }

    override fun getPrincipal(): AuthenticatedUser {
        return this
    }

    override fun isAuthenticated(): Boolean {
        return authenticated
    }

    override fun getDetails(): Any? {
        return null
    }

    fun getClaims(): Map<String, Any?> {
        return mapOf(
            JwtClaim.USER_ID.key to userId,
            JwtClaim.FIRST_NAME.key to firstName,
            JwtClaim.LAST_NAME.key to lastName,
            JwtClaim.COMPANY_ID.key to companyId,
            JwtClaim.EMAIL.key to email,
            JwtClaim.DISPLAY_NAME.key to displayName,
            JwtClaim.ROLE.key to role,
            JwtClaim.USER_TYPE.key to userType,
            JwtClaim.STATUS.key to status,
            JwtClaim.ON_BOARDED.key to onboard,
            JwtClaim.SUBSCRIBE_FLAG.key to subscriptionFlag,
            JwtClaim.COUNTRY_CODE.key to countryCode,
            JwtClaim.ADMIN_AUTHORITIES.key to adminAuthorities,
            JwtClaim.TEMP_PASSWORD.key to tempPassword,
            JwtClaim.USER_ACCESS.key to accesses,
            JwtClaim.USER_VISIBILITY.key to visibilities,
            JwtClaim.PARTNER_ID.key to partnerId
        )
    }
}