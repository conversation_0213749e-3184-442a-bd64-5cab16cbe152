package com.centuroglobal.shared.security

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.ConciergeNotificationState
import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.enums.Scope
import com.centuroglobal.shared.data.enums.TokenType
import com.centuroglobal.shared.data.pojo.AdminAuthorities
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AdminAuthoritiesRepository
import com.centuroglobal.shared.repository.ValidationTokenRepository
import io.jsonwebtoken.*
import io.jsonwebtoken.security.Keys
import javax.crypto.SecretKey
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import jakarta.servlet.http.HttpServletRequest

private const val TOKEN_ISSUER = "Centuro-Global"
private const val TOKEN_PREFIX = "Bearer "
private const val HEADER_NAME = "Authorization"
private const val REQUEST_PARAM = "token"

private val log = KotlinLogging.logger {}

@Component
class JwtHelper(
    private val validationTokenRepository: ValidationTokenRepository,
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository,
    @Value("\${app.authentication.jwt.secret}")
    private val secret: String,
    @Value("\${app.authentication.jwt.temp-token-expiry-in-secs}")
    private val tempTokenExpiry: Int,
    @Value("\${app.authentication.jwt.access-token-expiry-in-secs}")
    private val tokenExpiry: Int
) {
    private val secretKey: SecretKey = Keys.hmacShaKeyFor(secret.toByteArray())
    fun extractToken(request: HttpServletRequest): String {
        return request.getHeader(HEADER_NAME)
            ?: if (!request.getParameter(REQUEST_PARAM).isNullOrBlank()) {
                TOKEN_PREFIX + request.getParameter(REQUEST_PARAM)
            } else {
                ""
            }
    }

    fun getClaimsFromToken(token: String): Claims? {
        return try {
            Jwts.parser()
                .verifyWith(secretKey)
                .build()
                .parseSignedClaims(token.substring(TOKEN_PREFIX.length))
                .payload
        } catch (ex: Exception) {
            when (ex) {
                is ExpiredJwtException -> log.error("Token had expired: $token", ex)
                is JwtException -> log.error("Token's signature is not valid: $token", ex)
                else -> log.error("Failed to get claims from token: $token", ex)
            }
            null
        }
    }

    fun generateTokenResult(
        refreshToken: String?,
        claims: Map<String, Any?>,
        isTemp: Boolean,
        lastLoginDate: LocalDateTime?
    ): TokenResult? {
        val expiry = if (isTemp) {
            tempTokenExpiry
        } else {
            tokenExpiry
        }
        val accessToken = generateToken(claims, expiry)
        return if (accessToken == null) {
            null
        } else {
            val tempPassword = isTempPassword(claims)
            TokenResult(
                tokenType = TokenType.BEARER.value,
                expiresIn = expiry,
                accessToken = accessToken,
                refreshToken = refreshToken,
                lastLoginDate = lastLoginDate?.atZone(ZoneId.systemDefault())
                    ?.toInstant()?.epochSecond?.let { it * 1000 },
                scope = Scope.fromUserType(claims[JwtClaim.USER_TYPE.key] as String)?.value ?: "",
                onboard = claims[JwtClaim.ON_BOARDED.key] as Boolean,
                isTempPassword = tempPassword,
                validationToken = setValidationToken(tempPassword, claims[JwtClaim.USER_ID.key] as Long),
                adminAuthorities = setAccessList(
                    claims[JwtClaim.USER_ID.key] as Long,
                    claims[JwtClaim.ROLE.key] as String
                ),
                userAccess = claims[JwtClaim.USER_ACCESS.key] as List<UserAccess>?,
                userVisibilities = claims[JwtClaim.USER_VISIBILITY.key] as List<UserAccess>?,
                isFirstTimeLogin = lastLoginDate == null
            )
        }
    }

    private fun setAccessList(userId: Long, role: String): List<AdminAuthorities>? {
        return if (role == "ROLE_ADMIN") {
            val adminAuthoritiesList = adminAuthoritiesRepository.findAllByUserId(userId)

            adminAuthoritiesList.map { it ->
                AdminAuthorities(
                    key = it.accessName,
                    hasAccess = it.hasAccess,
                    accessType = if (it.hasAccess) "READ_WRITE" else null
                )
            }.toList()
        } else {
            null
        }
    }

    private fun setValidationToken(tempPassword: Boolean, userId: Long): String? {
        if (tempPassword) {
            val validation =
                validationTokenRepository.findByUserId(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            if (validation.state.name == ConciergeNotificationState.EMAIL_SENT.name) {
                return validation.code
            }
            throw ApplicationException(ErrorCode.LOGIN_FAIL)
        }
        return ""
    }

    private fun isTempPassword(claims: Map<String, Any?>): Boolean {
        if (claims[JwtClaim.TEMP_PASSWORD.key] != null) {
            return claims[JwtClaim.TEMP_PASSWORD.key] as Boolean
        }
        return false
    }

    fun generateToken(claims: Map<String, Any?>, expiry: Int): String? {
        return try {
            val now = System.currentTimeMillis()
            Jwts.builder()
                .claims(claims.minus(JwtClaim.TEMP_PASSWORD.key))
                .expiration(Date(now + (expiry * 1000L)))
                .notBefore(Date(now - 2000L)) // gives a 2 seconds leeway
                .issuedAt(Date(now))
                .subject(claims[JwtClaim.EMAIL.key] as String)
                .issuer(TOKEN_ISSUER)
                .signWith(secretKey)
                .compact()
        } catch (ex: Exception) {
            log.error("Error generating token", ex)
            null
        }
    }

    fun generateClaims(
        loginAccount: LoginAccountEntity,
        displayName: String,
        corporateId: Long?,
        countryCode: String,
        adminAuthorities: String,
        partnerId: Long?

        ): MutableMap<String, Any?> {
        val subscriptionType = loginAccount.subscriptionType?.toString() ?: "FREE"
        return mutableMapOf(
            (JwtClaim.USER_ID.key) to loginAccount.id,
            (JwtClaim.FIRST_NAME.key) to loginAccount.firstName,
            (JwtClaim.LAST_NAME.key) to loginAccount.lastName,
            (JwtClaim.COMPANY_ID.key) to corporateId,
            (JwtClaim.EMAIL.key) to loginAccount.email,
            (JwtClaim.USER_TYPE.key) to loginAccount.getUserType().name,
            (JwtClaim.ROLE.key) to loginAccount.role.name,
            (JwtClaim.STATUS.key) to loginAccount.status,
            (JwtClaim.DISPLAY_NAME.key) to displayName,
            (JwtClaim.ON_BOARDED.key) to loginAccount.onboard,
            (JwtClaim.SUBSCRIBE_FLAG.key) to subscriptionType,
            (JwtClaim.COUNTRY_CODE.key) to countryCode,
            (JwtClaim.ADMIN_AUTHORITIES.key) to adminAuthorities,
            (JwtClaim.PARTNER_ID.key) to partnerId
        )
    }
}