package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.EventEntity
import com.centuroglobal.shared.data.entity.EventInviteeEntity
import com.centuroglobal.shared.data.enums.EventInviteeStatus
import com.centuroglobal.shared.data.enums.EventStatus
import com.centuroglobal.shared.data.pojo.event.EventSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface EventRepository : JpaRepository<EventEntity, Long> {

    @Query(
        """
            select ev from event ev left join ev.invitees i left join ev.speakers sp
            where 
            (ev.status IN :#{#status}) AND 
            (:#{#filter.search} IS NULL OR (ev.name like :#{#filter.search} or  ev.about like :#{#filter.search})) AND
            (:#{#filter.from} IS NULL OR ev.endDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR ev.startDate <= :#{#filter.to}) AND
            (:#{#filter.status} IS NULL OR ev.status = :#{#filter.status}) AND
            (:#{#userId} IS NULL OR i.client.userId = :#{#userId} OR sp.internalMemberId = :#{#userId})
            group by ev.id
        """
    )
    fun searchByCriteria(
        @Param("status")
        status: Array<EventStatus>,
        @Param("userId")
        userId: Long?,
        @Param("filter")
        filter: EventSearchFilter,
        pageable: Pageable
    ): Page<EventEntity>

    @Query(
        """
            select ev from event ev left join ev.invitees i left join ev.speakers sp
            where 
            (ev.status IN :#{#status}) AND 
            (:#{#filter.search} IS NULL OR (ev.name like :#{#filter.search} or  ev.about like :#{#filter.search})) AND
            (:#{#filter.from} IS NULL OR ev.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR ev.createdDate <= :#{#filter.to}) AND
            (:#{#userId} IS NULL OR i.client.userId = :#{#userId} OR sp.internalMemberId = :#{#userId})
            group by ev.id
        """
    )
    fun searchByCriteriaWithoutStatus(
        @Param("status")
        status: Array<EventStatus>,
        @Param("userId")
        userId: Long?,
        @Param("filter")
        filter: EventSearchFilter,
        pageable: Pageable
    ): Page<EventEntity>

    @Query(
        """
        select i from event e join e.invitees i where e = ?1 and i.status = ?2
    """
    )
    fun getInvitees(eventEntity: EventEntity, status: EventInviteeStatus, pageable: Pageable): Page<EventInviteeEntity>

    @Query(
        """
        select i from event e join e.invitees i where e = ?1
    """
    )
    fun getInvitees(eventEntity: EventEntity, pageable: Pageable): Page<EventInviteeEntity>
    fun countByEndDateGreaterThan(now: Date): Long
}