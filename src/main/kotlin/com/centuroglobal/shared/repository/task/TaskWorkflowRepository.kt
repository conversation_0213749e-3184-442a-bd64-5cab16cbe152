package com.centuroglobal.shared.repository.task

import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.repository.BaseRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.*

@Repository
interface TaskWorkflowRepository : BaseRepository<TaskWorkflowEntity, Long> {


    @Query(value = """
        SELECT tw FROM task_workflow tw
        WHERE
        ((:#{#searchFilter.referenceId} IS NULL AND tw.referenceId IS NULL) OR tw.referenceId = :#{#searchFilter.referenceId}) AND
        ((:#{#searchFilter.referenceType} IS NULL AND tw.referenceType IS NULL) OR tw.referenceType = :#{#searchFilter.referenceType}) AND
        (:#{#searchFilter.name} IS NULL OR tw.name LIKE :#{#searchFilter.name}) AND
        (:#{#searchFilter.country} IS NULL OR tw.country LIKE :#{#searchFilter.country}) AND
        (:#{#searchFilter.status} IS NULL OR tw.status = :#{#searchFilter.status}) AND
        (:#{#searchFilter.id} IS NULL OR tw.id = :#{#searchFilter.id}) AND
        (:#{#searchFilter.category} IS NULL OR tw.category = :#{#searchFilter.category}) AND
        (:#{#searchFilter.category} IS NULL OR tw.category = :#{#searchFilter.category}) AND
        (:#{#searchFilter.updatedBy} IS NULL OR tw.updatedBy = :#{#searchFilter.updatedBy}) AND
        (:#{#searchFilter.showToPartner} IS NULL OR tw.showToPartner = :#{#searchFilter.showToPartner}) AND
        (:#{#searchFilter.partnerId} IS NULL OR (tw.partner.id = :#{#searchFilter.partnerId} AND true=:#{#searchFilter.isPartner}) OR (false=:#{#searchFilter.isPartner} AND tw.partner.id IS NULL)) AND
        ((false=:#{#searchFilter.isPartner} AND tw.partner.id IS NULL) OR (true=:#{#searchFilter.isPartner} AND tw.partner.id IS NOT NULL))
        
        group by tw.id
    """)
    override fun searchByCriteriaForAdmin(searchFilter: AbstractSearchFilter, pageable: Pageable): Page<TaskWorkflowEntity>

    @Query(value = """
        SELECT tw FROM task_workflow tw
        WHERE
        ((:#{#searchFilter.referenceId} IS NULL AND tw.referenceId IS NULL) OR tw.referenceId = :#{#searchFilter.referenceId}) AND
        ((:#{#searchFilter.referenceType} IS NULL AND tw.referenceType IS NULL) OR tw.referenceType = :#{#searchFilter.referenceType}) AND
        (:#{#searchFilter.name} IS NULL OR tw.name LIKE :#{#searchFilter.name}) AND
        (:#{#searchFilter.country} IS NULL OR tw.country LIKE :#{#searchFilter.country}) AND
        (:#{#searchFilter.status} IS NULL OR tw.status = :#{#searchFilter.status}) AND
        (:#{#searchFilter.category} IS NULL OR tw.category = :#{#searchFilter.category}) AND
        (:#{#searchFilter.updatedBy} IS NULL OR tw.updatedBy = :#{#searchFilter.updatedBy}) AND
        tw.createdBy IN :#{#userIds}
        
        group by tw.id
    """)
    override fun searchByCriteria(
        searchFilter: AbstractSearchFilter,
        userIds: List<Long>,
        pageable: Pageable
    ): Page<TaskWorkflowEntity>

    // TODO not used currently
    @Query(value = """
        SELECT tw FROM task_workflow tw
        WHERE
        tw.status = :#{#searchFilter.status} AND tw.createdBy=-1
    """)
    override fun searchByCriteriaForFullAccess(
        searchFilter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<TaskWorkflowEntity>

    @Query(value = """
        SELECT tw FROM task_workflow tw
        WHERE tw.id=:#{#id} AND 
        tw.createdBy IN (:#{#createdByIn})
    """)
    override fun findByIdAndCreatedByIdIn(id: Long, createdByIn: MutableList<Long>): Optional<TaskWorkflowEntity>

    /**
     * override default methods
     */

    @Query(value = """
            SELECT tw FROM task_workflow tw
            WHERE
            tw.id=:#{#id} AND tw.createdBy=:#{#corporateId}
    """)
    override fun findByIdAndCreatedByCorporateId(id: Long, corporateId: Long): Optional<TaskWorkflowEntity>

    @Query(value = """
            SELECT tw FROM task_workflow tw
            WHERE
            tw.id=:#{#id} AND 
            (:#{#showToPartner} IS NULL OR tw.showToPartner = :#{#showToPartner}) AND
            (
                (tw.partner.id IS NOT NULL AND :#{#partnerId} IS NOT NULL AND tw.partner.id=:#{#partnerId}) OR
                (tw.partner.id IS NULL AND :#{#partnerId} IS NOT NULL AND tw.showToPartner=true) OR
                (:#{#partnerId} IS NULL)
             )
    """)
    fun getWorkflowForView(id: Long, partnerId: Long?): TaskWorkflowEntity?

    fun findByReferenceIdAndReferenceType(referenceId: Long, referenceType: String): TaskWorkflowEntity?
    fun findAllByReferenceTypeAndTaskTemplatesTaskStatusIn(referenceType: String, statusList: List<TaskStatus>): List<TaskWorkflowEntity>

}