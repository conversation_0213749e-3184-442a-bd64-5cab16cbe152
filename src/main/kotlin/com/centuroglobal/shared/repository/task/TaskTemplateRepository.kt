package com.centuroglobal.shared.repository.task

import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TaskTemplateRepository : JpaRepository<TaskTemplateEntity, Long> {

    fun findByWorkflow(workflow: TaskWorkflowEntity): List<TaskTemplateEntity>?

    fun findByIdAndWorkflow(id: Long, workflow: TaskWorkflowEntity): TaskTemplateEntity?

    fun deleteByIdAndWorkflow(id: Long, workflow: TaskWorkflowEntity)

    fun deleteByWorkflow(workflow: TaskWorkflowEntity)

}