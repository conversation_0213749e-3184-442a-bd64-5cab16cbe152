package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ImmigrationRequirementEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ImmigrationRequirementRepository : JpaRepository<ImmigrationRequirementEntity, String>{
    fun findBySourceCountryAndDestCountry(sourceCountry: String, destinationCountry: String) : ImmigrationRequirementEntity
}