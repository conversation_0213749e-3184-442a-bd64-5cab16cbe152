package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BackofficeUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.pojo.AdminUserSearchFilter
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface BackofficeUserRepository : JpaRepository<BackofficeUserEntity, Long> {

    @Query(
        value = """
            SELECT DISTINCT bu FROM backoffice_user bu left join admin_auth au on bu.id = au.userId
            WHERE
            bu.status <> 'DELETED' AND
            (:#{#filter.search} IS NULL OR bu.firstName LIKE :#{#filter.search} OR bu.lastName LIKE :#{#filter.search} OR CONCAT(bu.firstName, ' ', bu.lastName) LIKE :#{#filter.search}) AND
            (:#{#filter.role} IS NULL OR bu.role = :#{#filter.role}) AND 
            (:#{#filter.status} IS NULL OR bu.status = :#{#filter.status})AND 
            (:#{#filter.responsibility} IS NULL OR au.accessName = :#{#filter.responsibility} AND au.hasAccess = true)
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: AdminUserSearchFilter
    ): List<BackofficeUserEntity>

    fun findByIdAndStatus(userId: Long, status: AccountStatus): BackofficeUserEntity?
}
