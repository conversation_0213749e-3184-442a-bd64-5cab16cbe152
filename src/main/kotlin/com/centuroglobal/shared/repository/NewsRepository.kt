package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.NewsEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface NewsRepository : JpaRepository<NewsEntity, Long> {
   fun findTop5ByIndustryCodeAndCreatedDateGreaterThanOrderByPublishedDateDesc(industryCode: String, newsExpiryDate: LocalDateTime): List<NewsEntity>

    fun deleteByIndustryCode(industryCode: String)
}