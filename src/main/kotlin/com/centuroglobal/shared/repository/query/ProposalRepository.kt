package com.centuroglobal.shared.repository.query

import com.centuroglobal.shared.data.entity.query.QueryEntity
import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface ProposalRepository : JpaRepository<ProposalEntity, Long> {

    /*fun findByIdAndQuery(id: Long, query: QueryEntity): ProposalEntity?

    @Query("""
        SELECT qp.isApproved, COUNT(DISTINCT qp.id) FROM query_proposal qp JOIN qp.query q JOIN q.categories c
        WHERE(
        (:#{#filter.search} IS NULL OR (q.heading LIKE :#{#filter.search} OR q.description LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR q.country = :#{#filter.countryCode}) AND
        (:#{#filter.categories} IS NULL OR c.name = :#{#filter.categories}) AND
        (:#{#filter.status} IS NULL OR q.status IN :#{#filter.status}) AND
        (:#{#filter.from} IS NULL OR q.createdDate >= :#{#filter.from}) AND
        (:#{#filter.to} IS NULL OR q.createdDate <= :#{#filter.to}) AND
        (:#{#filter.corporateId} IS NULL OR q.createdBy.corporate.id = :#{#filter.corporateId})
        )
        GROUP BY qp.isApproved
    """)
    fun findStatsByCriteria(@Param("filter") searchFilter: QuerySearchFilter): List<List<Any>>*/

    fun findAllByTypeAndReferenceId(type: ChatType, referenceId: Long): List<ProposalEntity>

    fun findByIdAndReferenceIdAndType(id: Long, referenceId: Long, type: ChatType): Optional<ProposalEntity>

}