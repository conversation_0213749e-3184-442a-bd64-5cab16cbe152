package com.centuroglobal.shared.repository.query

import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.query.QueryAssigneeEntity
import com.centuroglobal.shared.data.entity.query.QueryEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface QueryAssigneeRepository : JpaRepository<QueryAssigneeEntity, Long> {
    fun findAllByQuery(query: QueryEntity): MutableList<QueryAssigneeEntity>
    fun deleteByUserIn(users: MutableList<LoginAccountEntity>)
}