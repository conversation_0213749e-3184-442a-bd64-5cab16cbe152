package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CircleEntity
import com.centuroglobal.shared.data.entity.CircleMemberEntity
import com.centuroglobal.shared.data.enums.CircleMemberStatus
import com.centuroglobal.shared.data.enums.CircleType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CircleMemberRepository : JpaRepository<CircleMemberEntity, Long> {
    fun findTopByCircleAndUserId(circleEntity: CircleEntity, userId: Long): CircleMemberEntity?
    fun findTopByCircle_IdAndUserId(circleId: Long, userId: Long): CircleMemberEntity?

    fun findAllByUserIdAndCircle_CircleType(userId: Long, circleType: CircleType): List<CircleMemberEntity>

    fun findTop5ByCircle_IdAndCircleMemberStatus(circleId: Long,circleMemberStatus: CircleMemberStatus): List<CircleMemberEntity>
}