package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ReferralCodeEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ReferralCodeRepository : JpaRepository<ReferralCodeEntity, Long> {
    fun findFirstByReferralCodeAndExpiryAfter(referralCode: String, expiry: LocalDateTime): ReferralCodeEntity?
    fun findFirstByRootUserIdAndExpiryAfter(rootUserId: Long, expiry: LocalDateTime): ReferralCodeEntity?
}