package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BandDetailsEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface BandsDetailsRepository : JpaRepository<BandDetailsEntity, Long> {
    fun findByBandId(id: Long?): List<BandDetailsEntity>

    @Query(value = """
        DELETE FROM band_details bd LEFT JOIN bd.access access LEFT JOIN bd.visibility visibility 
        WHERE 
            bd.band.corporate = :#{#corporate} AND
            (access.featureKey IN :#{#features} OR visibility.featureKey IN :#{#features}) 
    """)
    @Modifying
    fun deleteAllByBandCorporateAndAccessFeatureKeyIn(corporate: CorporateEntity, features: List<String>?)

}
