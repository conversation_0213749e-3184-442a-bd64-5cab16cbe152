package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BlueprintStepEntity
import com.centuroglobal.shared.data.enums.StepName
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface BlueprintStepRepository : JpaRepository<BlueprintStepEntity, Long> {
    fun findByBlueprintCountryCodeAndStepName(countryCode: String, stepName: StepName): BlueprintStepEntity?
}