package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.RfpEntity
import com.centuroglobal.shared.data.entity.RfpDocumentEntity

import com.centuroglobal.shared.data.pojo.RfpSearchFilter

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface RfpDocumentRepository : JpaRepository<RfpDocumentEntity, Long> {

    fun findByIdAndRfp(id: Long, rfpEntity: RfpEntity): RfpDocumentEntity?

    @Query("""
        SELECT rd.id, COUNT(DISTINCT rd.id) FROM rfp_document rd JOIN rd.rfp r JOIN r.serviceDetails s LEFT JOIN s.countries sc
        WHERE(
        (:#{#filter.search} IS NULL OR (r.heading LIKE :#{#filter.search} OR r.description LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR sc.country = :#{#filter.countryCode}) AND
        (:#{#filter.categories} IS NULL OR s.serviceName = :#{#filter.categories}) AND
        (:#{#filter.status} IS NULL OR r.status IN :#{#filter.status}) AND
        (:#{#filter.from} IS NULL OR r.createdDate >= :#{#filter.from}) AND
        (:#{#filter.to} IS NULL OR r.createdDate <= :#{#filter.to}) AND
        (:#{#filter.corporateId} IS NULL OR r.createdBy.corporate.id = :#{#filter.corporateId}) AND
          ((:#{#filter.isPartnerRfp} IS NULL) OR (true=:#{#filter.isPartnerRfp} AND r.partner.id IS NOT NULL) OR 
                (false=:#{#filter.isPartnerRfp} AND r.partner.id IS NULL)) AND
        ((:#{#filter.cgRequested} IS NULL) OR (true=:#{#filter.cgRequested} AND r.partner.id IS NULL OR r.cgRequestedStatus IN ('CG_REQUESTED', 'CG_ACCEPTED')) OR 
                (false=:#{#filter.cgRequested} AND r.partner.id IS NOT NULL))
        )
        GROUP BY rd.id
    """)
    fun findStatsByCriteria(@Param("filter") searchFilter: RfpSearchFilter): List<List<Any>>


}