package com.centuroglobal.shared.repository.stripe

import com.centuroglobal.shared.data.entity.stripe.StripeTransactionEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface StripeTransactionRepository : JpaRepository<StripeTransactionEntity, Long> {

    fun findAllByCustomerIdOrderByIdDesc(customerId: String): List<StripeTransactionEntity>
}
