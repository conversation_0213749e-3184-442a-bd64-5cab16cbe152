package com.centuroglobal.shared.repository.stripe

import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import com.centuroglobal.shared.data.entity.stripe.StripeSubscriptionEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface StripeSubscriptionRepository : JpaRepository<StripeSubscriptionEntity, Long> {

    fun findAllByStripeAccountOrderByCreatedDateDesc(
        stripeAccountEntity: StripeAccountEntity
    ): List<StripeSubscriptionEntity?>

    fun findAllByStripeAccountAndSubscriptionActiveOrderByCreatedDateDesc(
        stripeAccountEntity: StripeAccountEntity,
        subscriptionActive: Boolean
    ): List<StripeSubscriptionEntity?>

    fun findAllBySubscriptionIdOrderByLastUpdatedDateDesc(
        subscriptionId: String
    ): List<StripeSubscriptionEntity?>

    fun findAllBySubscriptionIdAndSubscriptionActiveOrderByCreatedDateDesc(
        subscriptionId: String,
        subscriptionActive: Boolean
    ): List<StripeSubscriptionEntity?>
}
