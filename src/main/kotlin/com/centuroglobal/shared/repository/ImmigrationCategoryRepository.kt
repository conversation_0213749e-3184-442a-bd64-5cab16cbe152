package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ImmigrationCategoryEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ImmigrationCategoryRepository : JpaRepository<ImmigrationCategoryEntity, String> {
    fun findAllByVisaCode(visaCategory: String): List<ImmigrationCategoryEntity>
    fun findByVisaCode(visaType: String): ImmigrationCategoryEntity
}