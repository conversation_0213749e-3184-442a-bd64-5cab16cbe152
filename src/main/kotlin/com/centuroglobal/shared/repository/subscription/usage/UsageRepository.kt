package com.centuroglobal.shared.repository.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.AbstractUsageEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.NoRepositoryBean
import org.springframework.stereotype.Repository

@NoRepositoryBean
interface UsageRepository : JpaRepository<AbstractUsageEntity, Long> {

    fun findAllBySubscriptionUsageDetailsId(id: Long): List<AbstractUsageEntity>
}