package com.centuroglobal.shared.repository.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionDetailsEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SubscriptionDetailsRepository : JpaRepository<SubscriptionDetailsEntity, Long> {


    @Query("""
        select sd.threshold from subscription_details sd 
        join sd.plan sp
        where sp.companyId = :#{#corporateId}
        and sp.isActive = true
        and sd.code = :#{#code}
    """)
    fun findThresholdByCorporateIdAndCode(corporateId: Long, code: String): Long?

}