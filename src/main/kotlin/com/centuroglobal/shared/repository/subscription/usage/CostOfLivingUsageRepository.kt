package com.centuroglobal.shared.repository.subscription.usage

import com.centuroglobal.shared.data.entity.dto.usage.CostOfLivingDto
import com.centuroglobal.shared.data.entity.subscription.usage.CostOfLivingUsageEntity
import com.centuroglobal.shared.data.pojo.UsageLogSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CostOfLivingUsageRepository : UsageRepository {

    override fun findAllBySubscriptionUsageDetailsId(id: Long): List<CostOfLivingUsageEntity>
    fun findTopByUserIdOrderByCreatedAtDesc(userId: Long): CostOfLivingUsageEntity?
    @Query(value = """
        
        SELECT 
        col.id AS id,
        col.country AS country,
        col.band_name AS bandName,
        col.created_at AS createdDate,
        col.end_timestamp AS endTimestamp,
        co.name AS corporateName,
        CONCAT(la.first_name, ' ', la.last_name) AS createdBy,
        p.name AS partnerName
        FROM usage_cost_of_living col JOIN corporate_user cu ON col.user_id=cu.id JOIN
        corporate co ON co.id = cu.corporate_id JOIN login_account la ON la.id=col.user_id LEFT JOIN
        partner p ON p.id = co.partner_id
        WHERE
         (:#{#filter.country} IS NULL OR col.country = :#{#filter.country}) AND
         (:#{#filter.corporate} IS NULL OR cu.corporate_id = :#{#filter.corporate}) AND
         (:#{#filter.partnerId} IS NULL OR co.partner_id = :#{#filter.partnerId}) AND
         (:#{#filter.from} IS NULL  OR (col.created_at) BETWEEN :#{#filter.from} AND :#{#filter.to}) AND
        ((false=:#{#filter.isPartner} AND co.partner_id IS NULL) OR (true=:#{#filter.isPartner} AND co.partner_id IS NOT NULL))
         
         group by col.id
        
    """, nativeQuery = true)
    fun searchByCriteria(filter: UsageLogSearchFilter, pageRequest: Pageable): Page<CostOfLivingDto>
}