package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.TaskAssigneeEntity
import com.centuroglobal.shared.data.entity.TaskEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDate

@Repository
interface TaskAssigneeRepository : JpaRepository<TaskAssigneeEntity, Long> {

    fun findByTaskId(taskId: Long): List<TaskAssigneeEntity>?

    @Query(value = "DELETE FROM task_assignee ta where ta.task_id = :#{#taskId}", nativeQuery = true)
    fun deleteByTaskId(@Param("taskId") taskId: Long)

    @Query("""
        SELECT ta FROM task_assignee ta JOIN ta.task t
        WHERE(
        (:#{#dueDate} = CURRENT_DATE AND ((t.dueDate < CURRENT_DATE AND t.status != 'COMPLETED') OR t.dueDate = CURRENT_DATE)) OR
        (:#{#dueDate} = DATE(t.dueDate)) AND t.expectedDueDate IS NULL
        ) group by ta.id
        """)
    fun searchByDueDateCriteria(
        @Param("dueDate") dueDate: LocalDate
    ): List<TaskAssigneeEntity>

    @Query("""
        SELECT ta FROM task_assignee ta JOIN ta.task t
        WHERE(
        t.expectedDueDate IS NOT NULL AND
        (:#{#dueDate} = CURRENT_DATE AND ((t.expectedDueDate < CURRENT_DATE AND t.status != 'COMPLETED') OR t.expectedDueDate = CURRENT_DATE)) OR
        (:#{#dueDate} = DATE(t.expectedDueDate))
        ) group by ta.id
        """)
    fun searchByExpectedDueDateCriteria(
        @Param("dueDate") dueDate: LocalDate
    ): List<TaskAssigneeEntity>

}