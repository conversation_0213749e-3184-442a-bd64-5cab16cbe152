package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseDocumentsRepository : JpaRepository<CaseDocumentsEntity, Long> {
    fun findByCaseAndDocumentCode(case: CaseEntity, documentCode: String): CaseDocumentsEntity?
    fun findAllByCase(case: CaseEntity, pageRequest: PageRequest): MutableList<CaseDocumentsEntity>
    fun findAllByCase(case: CaseEntity): MutableList<CaseDocumentsEntity>

    fun existsByCase(case: CaseEntity): Boolean

    fun deleteByCase(case: CaseEntity)

    fun findAllByCaseAndExpiryDateNotNull(case: CaseEntity): MutableList<CaseDocumentsEntity>
}