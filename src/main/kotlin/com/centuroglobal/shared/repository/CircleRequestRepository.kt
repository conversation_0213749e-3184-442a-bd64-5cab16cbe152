package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CircleEntity
import com.centuroglobal.shared.data.entity.CircleRequestEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CircleRequestRepository : JpaRepository<CircleRequestEntity, Long> {
    fun findTopByCircleAndUserIdOrderByIdDesc(circleEntity: CircleEntity, userId: Long): CircleRequestEntity?
    fun findTopByCircle_IdAndUserIdOrderByIdDesc(circleId: Long, userId: Long): CircleRequestEntity?
}