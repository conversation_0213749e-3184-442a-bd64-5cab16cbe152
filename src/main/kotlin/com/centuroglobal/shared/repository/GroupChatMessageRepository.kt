package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.GroupChatEntity
import com.centuroglobal.shared.data.entity.GroupChatMessageEntity
import com.centuroglobal.shared.data.enums.ChatType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface GroupChatMessageRepository : JpaRepository<GroupChatMessageEntity, Long> {

    fun findByGroupChatAndIsHiddenAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity: GroupChatEntity, isHidden: Boolean, fromInstantMillis: LocalDateTime)
        : List<GroupChatMessageEntity>?

    fun findByGroupChatAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity: GroupChatEntity, fromInstantMillis: LocalDateTime)
            : List<GroupChatMessageEntity>?

    fun findByCreatedDateGreaterThanAndGroupChatChatType(createdAt: LocalDateTime, chatType: ChatType): List<GroupChatMessageEntity>?
    fun countByGroupChat(groupChatEntity: GroupChatEntity): Long

    fun countByGroupChatAndCreatedDateGreaterThan(groupChatEntity: GroupChatEntity, createdAt: LocalDateTime): Long


}