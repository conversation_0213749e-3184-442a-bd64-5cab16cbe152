package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CountryIndicesEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface CountryIndicesRepository : JpaRepository<CountryIndicesEntity, Long> {
    fun findAllByCreatedDateGreaterThan(date: LocalDateTime?): MutableList<CountryIndicesEntity>
}