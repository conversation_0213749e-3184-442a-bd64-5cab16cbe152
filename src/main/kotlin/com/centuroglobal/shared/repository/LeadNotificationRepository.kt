package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.LeadNotificationEntity
import com.centuroglobal.shared.data.enums.LeadNotificationState
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface LeadNotificationRepository : JpaRepository<LeadNotificationEntity, Long> {

    fun findAllByState(state: LeadNotificationState, pageable: Pageable): Page<LeadNotificationEntity>

    fun deleteAllByStateInAndLastUpdatedDateBefore(state: List<LeadNotificationState>, cutOffDate: LocalDateTime)
}
