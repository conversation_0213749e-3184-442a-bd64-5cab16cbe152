package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.TaskEntity
import com.centuroglobal.shared.data.entity.TaskReminderEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TaskReminderRepository : JpaRepository<TaskReminderEntity, Long> {

    fun deleteByTask(task: TaskEntity)
}