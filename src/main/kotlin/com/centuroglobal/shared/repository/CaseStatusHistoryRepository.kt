package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.case.CaseStatusHistory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CaseStatusHistoryRepository : JpaRepository<CaseStatusHistory, Long>{
    fun findAllByCase(case: CaseEntity): MutableList<CaseStatusHistory>

    fun findAllByCaseIn(cases: List<CaseEntity>): MutableList<CaseStatusHistory>

    fun findAllByCaseInAndIsDeleted(cases: List<CaseEntity>, isDeleted: Boolean ): MutableList<CaseStatusHistory>

    @Query(""" 
         select c from case_history c 
         where
         (c.case in (:#{#caseList})) 
         AND (:#{#status} IS NULL OR c.status = :#{#status})
         AND (:#{#actionFor} IS NULL OR c.actionFor = :#{#actionFor})
         group by c.id
    """)
    fun searchByCriteria(
        @Param("caseList") caseList: List<CaseEntity>,
        @Param("status") status: String?,
        @Param("actionFor") actionFor: String?,
        pageable: Pageable
    ) : Page<CaseStatusHistory>

    fun findByActionForIsNull(): List<CaseStatusHistory>

    fun deleteAllByCase(case: CaseEntity)
}