package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BlueprintStepExpertEntity
import com.centuroglobal.shared.data.enums.StepName
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface BlueprintStepExpertRepository : JpaRepository<BlueprintStepExpertEntity, Long> {

    fun findAllByCountryCode(countryCode: String): List<BlueprintStepExpertEntity>

    fun findAllByCountryCodeAndStepName(countryCode: String, stepName: StepName): List<BlueprintStepExpertEntity>

    fun findByCountryCodeAndStepNameAndExpertId(countryCode: String, stepName: StepName, expertId: Long): BlueprintStepExpertEntity?

    fun deleteByIdIn(id: List<Long>)

}