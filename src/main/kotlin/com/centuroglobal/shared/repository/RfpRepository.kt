package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.RfpEntity
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.data.pojo.RfpSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.*

@Repository
interface RfpRepository  : BaseRepository<RfpEntity, Long> {

    @Query("""
        SELECT r FROM rfp r JOIN r.serviceDetails s LEFT JOIN s.countries sc JOIN
        group_chat gc ON gc.referenceId = r.id AND gc.chatType = 'RFP' LEFT JOIN
        group_chat_messages gcm ON gcm.groupChat.id = gc.id
        WHERE(
        ((gc.id, gcm.groupChat.id, gcm.createdDate) IN (select gc2.id, gcm2.groupChat.id, max(gcm2.createdDate) as latestCreatedDate from
 group_chat_messages gcm2 right join group_chat gc2 on gcm2.groupChat.id = gc2.id group by gcm2.groupChat.id) OR gcm.groupChat.id IS NULL) AND
        (:#{#filter.search} IS NULL OR (r.heading LIKE :#{#filter.search} OR r.description LIKE :#{#filter.search} OR str(r.id) LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR sc.country = :#{#filter.countryCode}) AND
        (:#{#filter.categories} IS NULL OR s.serviceName = :#{#filter.categories}) AND
        (:#{#filter.status} IS NULL OR r.status IN :#{#filter.status}) AND
        (:#{#filter.from} IS NULL OR r.createdDate >= :#{#filter.from}) AND
        (:#{#filter.to} IS NULL OR r.createdDate <= :#{#filter.to}) AND
        (:#{#filter.corporateId} IS NULL OR r.createdBy.corporate.id = :#{#filter.corporateId}) AND
        (:#{#filter.partnerId} IS NULL OR r.partner.id = :#{#filter.partnerId}) AND
        ((:#{#filter.isPartnerRfp} IS NULL) OR (false=:#{#filter.isPartnerRfp} AND r.partner.id IS NULL) OR (true=:#{#filter.isPartnerRfp} AND r.partner.id IS NOT NULL)) AND
        ((:#{#filter.cgRequested} IS NULL OR false=:#{#filter.cgRequested}) OR (true=:#{#filter.cgRequested} AND (r.managedBy='CG' OR r.cgRequestedStatus IN ('CG_REQUESTED', 'CG_ACCEPTED'))))
        )
        group by r.id
    """)

    //change it to abstractFilter
    override fun searchByCriteriaForAdmin(
        @Param("filter") filter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<RfpEntity>

    @Query("""
        SELECT r FROM rfp r JOIN r.serviceDetails s LEFT JOIN s.countries sc JOIN
        group_chat gc ON gc.referenceId = r.id LEFT JOIN
        group_chat_messages gcm ON gcm.groupChat.id = gc.id LEFT JOIN
        r.createdBy.accounts a
        WHERE(
        (:#{#filter.search} IS NULL OR (r.heading LIKE :#{#filter.search} OR r.description LIKE :#{#filter.search} OR str(r.id) LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR sc.country = :#{#filter.countryCode}) AND
        (:#{#filter.user} IS NULL OR r.createdBy.id = :#{#filter.user}) AND
        (:#{#filter.status} IS NULL OR r.status IN :#{#filter.status}) AND
        (:#{#filter.categories} IS NULL OR s.serviceName = :#{#filter.categories}) AND
        (:#{#filter.responses} IS NULL OR gcm.message LIKE :#{#filter.responses}) AND
        (:#{#filter.accountId} IS NULL OR a.id = :#{#filter.accountId}) AND
        (:#{#filter.loggedInUserCorporateId} IS NULL OR r.createdBy.corporate.id = :#{#filter.loggedInUserCorporateId}) 
        )
        group by r.id
    """)
    override fun searchByCriteriaForFullAccess(filter: AbstractSearchFilter, pageable: Pageable): Page<RfpEntity>

    @Query("""
        SELECT r FROM rfp r JOIN r.serviceDetails s LEFT JOIN s.countries sc JOIN
        group_chat gc ON gc.referenceId = r.id LEFT JOIN
        group_chat_messages gcm ON gcm.groupChat.id = gc.id LEFT JOIN
        r.createdBy.accounts a
        WHERE(
        (:#{#filter.search} IS NULL OR (r.heading LIKE :#{#filter.search} OR r.description LIKE :#{#filter.search} OR str(r.id) LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR sc.country = :#{#filter.countryCode}) AND
        (:#{#filter.user} IS NULL OR r.createdBy.id = :#{#filter.user}) AND
        (:#{#filter.status} IS NULL OR r.status IN :#{#filter.status}) AND
        (:#{#filter.categories} IS NULL OR s.serviceName = :#{#filter.categories}) AND
        (:#{#filter.responses} IS NULL OR gcm.message LIKE :#{#filter.responses}) AND
        (:#{#filter.accountId} IS NULL OR a.id = :#{#filter.accountId}) AND
        (:#{#filter.loggedInUserCorporateId} IS NULL OR r.createdBy.corporate.id = :#{#filter.loggedInUserCorporateId}) AND
        (r.createdBy.id IN :#{#userIds})
        )
        group by r.id
    """)
    override fun searchByCriteria(
        filter: AbstractSearchFilter,
        userIds: List<Long>,
        pageable: Pageable
    ): Page<RfpEntity>

    override fun findByIdAndCreatedByIdIn(queryId: Long, reportees: MutableList<Long>): Optional<RfpEntity>
    override fun findByIdAndCreatedByCorporateId(queryId: Long, corporateId: Long): Optional<RfpEntity>

    @Query("""
        SELECT r.status, COUNT(DISTINCT r.id) FROM rfp r JOIN r.serviceDetails s LEFT JOIN s.countries sc
        WHERE(
        (:#{#filter.search} IS NULL OR (r.heading LIKE :#{#filter.search} OR r.description LIKE :#{#filter.search} OR str(r.id) LIKE :#{#filter.search})) AND
        (:#{#filter.countryCode} IS NULL OR sc.country = :#{#filter.countryCode}) AND
        (:#{#filter.categories} IS NULL OR s.serviceName = :#{#filter.categories}) AND
        (:#{#filter.status} IS NULL OR r.status IN :#{#filter.status}) AND
        (:#{#filter.from} IS NULL OR r.createdDate >= :#{#filter.from}) AND
        (:#{#filter.to} IS NULL OR r.createdDate <= :#{#filter.to}) AND
        (:#{#filter.corporateId} IS NULL OR r.createdBy.corporate.id = :#{#filter.corporateId}) AND
        (:#{#filter.partnerId} IS NULL OR r.partner.id = :#{#filter.partnerId}) AND
        ((:#{#filter.isPartnerRfp} IS NULL OR false=:#{#filter.isPartnerRfp}) OR (true=:#{#filter.isPartnerRfp} AND r.partner.id IS NOT NULL)) AND
        ((:#{#filter.cgRequested} IS NULL OR false=:#{#filter.cgRequested}) OR (true=:#{#filter.cgRequested} AND (r.managedBy='CG' OR r.cgRequestedStatus IN ('CG_REQUESTED', 'CG_ACCEPTED'))))
        )
        GROUP BY r.status
    """)
    fun findStatsByCriteria(@Param("filter") searchFilter: RfpSearchFilter): List<List<Any>>

    fun findByIdAndStatus(id: Long, draft: RfpStatus): Optional<RfpEntity>

    fun deleteAllByStatusAndCreatedDateBefore(status: RfpStatus, date: LocalDateTime)

    fun countByCreatedByCorporateIdAndStatusNotIn(corporateId: Long, statusesToSkip: List<RfpStatus>): Long
    fun countByAssignedToIdIn(assigneeList: List<Long>): Long

    fun countByCreatedById(corporateUserId: Long): Long
    fun findByCreatedByCorporateId(corporateId: Long): List<RfpEntity>

    @Query(value = """
        UPDATE rfp r SET r.lastUpdatedDate = :#{#lastUpdatedDate} WHERE id = :#{#id}
    """)
    @Modifying
    fun touch(id: Long, lastUpdatedDate: LocalDateTime)
}