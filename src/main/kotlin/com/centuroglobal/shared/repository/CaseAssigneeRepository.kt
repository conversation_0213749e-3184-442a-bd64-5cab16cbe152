package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseAssigneeEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseAssigneeRepository : JpaRepository<CaseAssigneeEntity, Long> {
    fun findAllByCaseAndExpert(case: <PERSON>E<PERSON><PERSON>, expert: ExpertUserEntity): CaseAssigneeEntity?
    fun findAllByCase(case: CaseEntity): MutableList<CaseAssigneeEntity>
}