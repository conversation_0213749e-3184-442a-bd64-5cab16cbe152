package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.pojo.CorporateUserDetailsFilter
import com.centuroglobal.shared.data.pojo.CorporateUserSearchFilter
import com.centuroglobal.shared.data.pojo.CorporateUsers
import com.centuroglobal.shared.data.pojo.playbook.PlaybookShareUserReferenceData
import com.centuroglobal.shared.data.pojo.subscription.dto.ISubscriptionUsageDetailsDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface CorporateUserRepository : JpaRepository<CorporateUserEntity, Long> {

    fun findByCorporateId(corporateId: Long): List<CorporateUserEntity>

    fun findIdByCorporateId(corporateId: Long): List<EntityIdDto>

    fun findFirstByCorporateId(corporateId: Long): CorporateUserEntity?

    fun findIdById(id: Long): EntityIdDto?

    @Query(
        value = """
        UPDATE `login_account` 
        SET 
            `role` = ?2,
            `refresh_token` = null
        WHERE 
            `id` IN (SELECT `id` FROM `corporate_user` WHERE `corporate_id` = ?1) AND 
            `role` != ?2
    """, nativeQuery = true
    )
    fun updateRoleByCorporateId(corporateId: Long, roleName: String)

    fun findAllByCorporateIdAndStatus(
        corporateId: Long,
        accountStatus: AccountStatus
    ): List<CorporateUserEntity>

    fun findAllByManagersManagerId(managerId: Long): MutableList<CorporateUserEntity>
    fun findIdByManagersManagerId(managerId: Long): MutableList<EntityIdDto>
    fun findByIdAndCorporate(managerUserId: Long?, corporate: CorporateEntity) :CorporateUserEntity?
    fun countByManagersManagerId(id: Long): Long

    fun findAllByManagersManagerIdAndManagersCorporateUser(managerId: Long, corporateUserEntity: CorporateUserEntity): Optional<CorporateUserEntity>

    @Query("""
        SELECT distinct cu FROM corporate_user cu join cu.accounts a WHERE (
            (:#{#filter.search} IS NULL OR (cu.firstName LIKE :#{#filter.search} OR 
            cu.lastName LIKE :#{#filter.search} OR cu.email LIKE :#{#filter.search})) AND
            (:#{#filter.accountId} IS NULL OR a.id = :#{#filter.accountId}) AND
            (:#{#filter.status} IS NULL OR cu.status= :#{#filter.status}) AND
            (:#{#filter.bandId} IS NULL OR cu.band.id= :#{#filter.bandId}) AND
            (:#{#filter.countryCode} IS NULL OR cu.countryCode= :#{#filter.countryCode}) AND
            cu.id>0
        ) AND (cu.corporate.id=:#{#corporateId})
    """)
    fun searchByCriteriaForFullAccess(@Param("filter") searchFilter: CorporateUserSearchFilter, @Param("corporateId")
                                        corporateId: Long, pageable: Pageable): Page<CorporateUserEntity>

    @Query("""
        SELECT distinct cu FROM corporate_user cu join cu.accounts a WHERE (
            (:#{#filter.search} IS NULL OR (cu.firstName LIKE :#{#filter.search} OR cu.lastName LIKE :#{#filter.search} OR cu.email LIKE :#{#filter.search})) AND
            (:#{#filter.accountId} IS NULL OR a.id = :#{#filter.accountId}) AND
            (:#{#filter.status} IS NULL OR cu.status= :#{#filter.status}) AND
            (:#{#filter.bandId} IS NULL OR cu.band.id= :#{#filter.bandId}) AND
            (:#{#filter.countryCode} IS NULL OR cu.countryCode= :#{#filter.countryCode})
        ) AND (cu.corporate.id=:#{#corporateId}) AND (cu.id IN :#{#userIds}) AND
            cu.id>0

    """)
    fun searchByCriteriaForReporteesAccess(@Param("filter") searchFilter: CorporateUserSearchFilter, corporateId: Long,
                                     userIds: List<Long>,
                                     pageRequest: Pageable): Page<CorporateUserEntity>

    @Query("""
        SELECT distinct cu FROM corporate_user cu join cu.accounts a WHERE (
            (:#{#filter.search} IS NULL OR (cu.firstName LIKE :#{#filter.search} OR cu.lastName LIKE :#{#filter.search} OR cu.email LIKE :#{#filter.search})) AND
            (:#{#filter.accountId} IS NULL OR a.id = :#{#filter.accountId}) AND
            (:#{#filter.status} IS NULL OR cu.status= :#{#filter.status}) AND
            (:#{#filter.bandId} IS NULL OR cu.band.id= :#{#filter.bandId}) AND
            (:#{#filter.countryCode} IS NULL OR cu.countryCode= :#{#filter.countryCode})
        ) AND (cu.corporate.id=:#{#corporateId}) AND (cu.id = :#{#userId}) AND
            cu.id>0

    """)
    fun searchByCriteriaForOwnAccess(@Param("filter") searchFilter: CorporateUserSearchFilter, corporateId: Long,
                                     userId: Long,
                                     pageRequest: Pageable): Page<CorporateUserEntity>

    fun findByBand(band: BandsEntity): List<CorporateUserEntity>
    fun findAllByCorporateIdAndBandNameIn(corporateId: Long, bandNames: List<String>): List<CorporateUserEntity>

    fun findAllByCorporateIn(corporates: List<CorporateEntity>): List<CorporateUserEntity>

    @Query("""
            SELECT cu FROM corporate_user cu 
            WHERE (
                (:#{#filter.search} IS NULL  OR cu.firstName LIKE :#{#filter.search} OR cu.lastName 
                                        LIKE :#{#filter.search} OR cu.email LIKE :#{#filter.search}) AND
                (:#{#filter.countryCode} IS NULL  OR cu.countryCode = :#{#filter.countryCode}) AND
                (:#{#filter.corporateId} IS NULL OR cu.corporate.id = :#{#filter.corporateId}) AND
                (:#{#filter.subscription} IS NULL  OR cu.subscriptionType = :#{#filter.subscription}) AND
                (:#{#filter.status} IS NULL  OR cu.status = :#{#filter.status}) AND
                (:#{#filter.bandName} IS NULL  OR cu.band.id = :#{#filter.bandName}) AND
                (
                    (:#{#filter.createdFrom} IS NULL AND :#{#filter.createdTo} IS NULL) OR 
                    cu.createdDate BETWEEN :#{#filter.createdFrom} AND :#{#filter.createdTo}
                ) AND
                (
                    (:#{#filter.joinedFrom} IS NULL AND :#{#filter.joinedTo} IS NULL) OR 
                    cu.joinedDate BETWEEN :#{#filter.joinedFrom} AND :#{#filter.joinedTo}
                ) AND
                (:#{#filter.partnerId} IS NULL OR cu.corporate.partner.id = :#{#filter.partnerId}) AND
                (:#{#filter.isPartnerCompany} IS NULL OR (true=:#{#filter.isPartnerCompany} AND cu.corporate.partner IS NOT NULL) OR 
                (false=:#{#filter.isPartnerCompany} AND cu.corporate.partner IS NULL)) AND
                cu.id>0
            ) 
            GROUP BY cu.id
    """)
    fun searchByCriteria(
        @Param("filter") searchFilter: CorporateUserDetailsFilter,
        pageRequest: Pageable
    ): Page<CorporateUserEntity>


    @Query("""
            SELECT cu.status, COUNT(DISTINCT cu.id) FROM corporate_user cu 
            WHERE (
            (:#{#filter.search} IS NULL  OR cu.firstName LIKE :#{#filter.search} OR cu.lastName LIKE :#{#filter.search} OR cu.email LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL  OR cu.countryCode = :#{#filter.countryCode}) AND
            (:#{#filter.corporateId} IS NULL  OR cu.corporate.id = :#{#filter.corporateId}) AND
            (:#{#filter.subscription} IS NULL  OR cu.subscriptionType = :#{#filter.subscription}) AND
            (:#{#filter.status} IS NULL  OR cu.status = :#{#filter.status}) AND
            (:#{#filter.bandName} IS NULL  OR cu.band.id = :#{#filter.bandName}) AND
            (
                (:#{#filter.createdFrom} IS NULL AND :#{#filter.createdTo} IS NULL ) OR 
                cu.createdDate BETWEEN :#{#filter.createdFrom} AND :#{#filter.createdTo}
            ) AND
            (
                (:#{#filter.joinedFrom} IS NULL AND :#{#filter.joinedTo} IS NULL) OR 
                    cu.joinedDate BETWEEN :#{#filter.joinedFrom} AND :#{#filter.joinedTo}
                ) AND
            (:#{#filter.partnerId} IS NULL OR cu.corporate.partner.id = :#{#filter.partnerId}) AND
            (:#{#filter.isPartnerCompany} IS NULL OR (true=:#{#filter.isPartnerCompany} AND cu.corporate.partner IS NOT NULL) OR 
            (false=:#{#filter.isPartnerCompany} AND cu.corporate.partner IS NULL)) AND
            cu.id>0
            ) 
            GROUP BY cu.status
    """)
    fun findStatsByCriteria(
        @Param("filter") searchFilter: CorporateUserDetailsFilter
    ): List<List<Any>>

    fun findByIdAndCorporatePartnerId(id: Long?, partnerId: Long): CorporateUserEntity?
    fun findUsersByCorporatePartnerId(partnerId: Long): List<CorporateUsers>

    fun findUsersByCorporateId(corporateId: Long): List<CorporateUsers>
    fun findAllProjectedBy(): List<CorporateUsers>
    fun findPlaybookUsersByCorporateIdAndStatusIn(
        corporateId: Long,
        status: List<AccountStatus>
    ): List<PlaybookShareUserReferenceData>

    fun findPlaybookUsersByManagersManagerIdAndStatusIn(
        managerId: Long,
        status: List<AccountStatus>
    ): MutableList<PlaybookShareUserReferenceData>

    fun findByEmail(email: String): CorporateUserEntity?
    fun findByIdAndPartnerId(id: Long, partnerId: Long): CorporateUserEntity?


    @Query("""
        SELECT distinct sud
        FROM subscription_usage_details sud
        JOIN sud.plan su
        WHERE sud.code = :#{#code}
        AND su.companyId = :#{#corporateId}
        AND MONTH(su.date) = :#{#month}
        AND YEAR(su.date) = :#{#year}
    """)
    fun findSubscriptionUsageDetailsByCode(corporateId: Long?, code: String, month: Int, year: Int): List<ISubscriptionUsageDetailsDto>

}