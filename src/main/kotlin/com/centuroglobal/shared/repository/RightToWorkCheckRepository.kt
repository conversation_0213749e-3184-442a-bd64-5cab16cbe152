package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.case.RightToWorkCheckEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDate

@Repository
interface RightToWorkCheckRepository : JpaRepository<RightToWorkCheckEntity, Long> {

    @Query("""
        SELECT c FROM right_to_work_check c 
        WHERE c.visaExpiryDate IS NOT NULL AND 
        CAST(from_unixtime(c.visaExpiryDate/1000) AS DATE) = :#{#expiryDate}
    """)
    fun findAllByVisaExpiryDate(expiryDate: LocalDate): List<RightToWorkCheckEntity>?
}