package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.ExpertUserDetailsFilter
import com.centuroglobal.shared.data.pojo.ExpertUserReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface ExpertUserRepository : JpaRepository<ExpertUserEntity, Long> {

    @Query(
        value = """
            SELECT eu FROM expert_user eu left join eu.expertises e
            WHERE
            (:#{#filter.search} IS NULL OR eu.displayName LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL OR eu.countryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR e.id = :#{#filter.expertiseId}) AND
            (:#{#status} IS NULL OR eu.status = :#{#status}) group by eu.id
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: ExpertSearchFilter,
        @Param("status")
        status: AccountStatus?,
        pageable: Pageable
    ): Page<ExpertUserEntity>


    @Query(
        value = """
            SELECT eu FROM expert_user eu left join eu.expertises e
            WHERE
            (:#{#filter.search} IS NULL OR eu.displayName LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL OR eu.countryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR e.id = :#{#filter.expertiseId}) AND
            (:#{#status} IS NULL OR eu.status = :#{#status}) AND 
            (eu.id in :#{#ids}) group by eu.id
        """
    )
    fun searchByCriteriaAndIdIn(
        @Param("filter")
        filter: ExpertSearchFilter,
        @Param("status")
        status: AccountStatus?,
        pageable: Pageable,
        @Param("ids")
        ids: List<Long>
    ): Page<ExpertUserEntity>

    @Query(
        value = """
            SELECT eu FROM expert_user eu join eu.expertises e
            WHERE
            (eu.countryCode = ?1 and
            e.id in ?2 and
            eu.status = ?3 and
            eu.id <> ?4) group by eu.id
        """
    )
    fun findAllByCountryCodeAndExpertiseIdAndStatusAndIdNot(
        countryCode: String,
        expertiseId: List<Int>,
        status: AccountStatus,
        id: Long
    ): List<ExpertUserEntity>

    fun findByIdAndStatus(userId: Long, status: AccountStatus): ExpertUserEntity?


    @Query(
        value = """
            SELECT eu FROM expert_user eu left join eu.expertises e
            WHERE
            (:#{#filter.search} IS NULL OR eu.displayName LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL OR eu.countryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR e.id = :#{#filter.expertiseId}) AND
            (:#{#status} IS NULL OR eu.status = :#{#status}) AND 
            (COALESCE(:#{#filter.countryCodes},NULL) IS NULL OR eu.countryCode in :#{#filter.countryCodes}) AND
            (COALESCE(:#{#filter.expertiseIds},NULL) IS NULL OR e.id in :#{#filter.expertiseIds})
        """
    )
    fun searchByCriteriaAndIdInForCircleOrderByProfilePhotoUrlDesc(
        @Param("filter")
        filter: ExpertSearchFilter,
        @Param("status")
        status: AccountStatus?,
        pageable: Pageable
    ): Page<ExpertUserEntity>

    fun findAllByExpertType(expertType: String): List<ExpertUserEntity>

    fun findAllByExpertTypeAndCompanyProfileAssociatedPartnersId(expertType: String, partnerId: Long): List<ExpertUserEntity>

    fun findAllByCompanyProfileAndExpertType(companyProfile: ExpertCompanyProfileEntity, expertType: String): List<ExpertUserEntity>
    fun findAllByCompanyProfile(companyProfile: ExpertCompanyProfileEntity?): List<ExpertUserEntity>
    fun findByCompanyProfileAndExpertType(companyProfile: ExpertCompanyProfileEntity, expertType: String): ExpertUserEntity

    fun findByIdAndPartnerId(id: Long,partnerId: Long): ExpertUserEntity?
    fun findAllByCompanyProfileIn(companyProfiles: List<ExpertCompanyProfileEntity>): List<ExpertUserEntity>

    @Query(
        value = """             
                SELECT distinct eu FROM expert_user eu left join eu.companyProfile.associatedPartners ap
                    WHERE (
                    (:#{#filter.search} IS NULL OR eu.firstName LIKE :#{#filter.search} OR eu.lastName LIKE :#{#filter.search} OR eu.email LIKE :#{#filter.search}) AND
                    (:#{#filter.countryCode} IS NULL OR eu.countryCode = :#{#filter.countryCode}) AND
                    (:#{#filter.companyId} IS NULL OR eu.companyProfile.id = :#{#filter.companyId}) AND
                    (:#{#filter.expertType} IS NULL OR eu.expertType = :#{#filter.expertType}) AND
                    (:#{#filter.status} IS NULL OR eu.status = :#{#filter.status}) AND
                    (:#{#filter.partnerId} IS NULL OR ap.id = :#{#filter.partnerId}) AND
                    (:#{#filter.expertiseIds} IS NULL OR eu.expertiseId IN :#{#filter.expertiseIds}) AND
                    
                    (
                    (:#{#filter.createdFrom} IS NULL OR :#{#filter.createdTo} IS NULL) OR
                    eu.createdDate BETWEEN :#{#filter.createdFrom} AND :#{#filter.createdTo}
                    ) AND
                    (
                    (:#{#filter.joinedFrom} IS NULL AND :#{#filter.joinedTo} IS NULL) OR 
                    eu.joinedDate BETWEEN :#{#filter.joinedFrom} AND :#{#filter.joinedTo}
                    ) AND
                    ((:#{#filter.isPartnerCompany} IS NULL) 
                        OR (true=:#{#filter.isPartnerCompany} 
                            AND ((eu.companyProfile.companyType='SUPPLIER' AND ap.id IS NOT NULL) OR (eu.companyProfile.companyType='EXPERT' AND ap.id IS NOT NULL)) 
                     ) OR 
                        (false=:#{#filter.isPartnerCompany} 
                        AND ((eu.companyProfile.companyType='SUPPLIER' AND ap.id IS NULL) OR (eu.companyProfile.companyType='EXPERT'))
                        )
                      )
                    )
                    GROUP BY eu.id
                    
    """)
    fun searchByCriteriaForListing(
        @Param("filter")
        filter: ExpertUserDetailsFilter,
        pageable: Pageable
    ): Page<ExpertUserEntity>


    @Query(
        value = """             
                SELECT eu.status, COUNT(DISTINCT eu.id) FROM expert_user eu left join eu.companyProfile.associatedPartners ap
                    WHERE (
                    (:#{#filter.search} IS NULL OR eu.firstName LIKE :#{#filter.search} OR eu.lastName LIKE :#{#filter.search} OR eu.email LIKE :#{#filter.search}) AND
                    (:#{#filter.countryCode} IS NULL OR eu.countryCode = :#{#filter.countryCode}) AND
                    (:#{#filter.companyId} IS NULL OR eu.companyProfile.id = :#{#filter.companyId}) AND
                    (:#{#filter.expertType} IS NULL OR eu.expertType = :#{#filter.expertType}) AND
                    (:#{#filter.status} IS NULL OR eu.status = :#{#filter.status}) AND
                    (:#{#filter.partnerId} IS NULL OR ap.id = :#{#filter.partnerId}) AND
                    (:#{#filter.expertiseIds} IS NULL OR eu.expertiseId IN :#{#filter.expertiseIds}) AND
                    (
                    (:#{#filter.createdFrom} IS NULL OR :#{#filter.createdTo} IS NULL) OR
                    eu.createdDate BETWEEN :#{#filter.createdFrom} AND :#{#filter.createdTo}
                    ) AND
                    (
                    (:#{#filter.joinedFrom} IS NULL OR :#{#filter.joinedTo} IS NULL) OR
                    eu.joinedDate BETWEEN :#{#filter.joinedFrom} AND :#{#filter.joinedTo}
                    ) AND
                    ((:#{#filter.isPartnerCompany} IS NULL) 
                        OR (true=:#{#filter.isPartnerCompany} 
                            AND ((eu.companyProfile.companyType='SUPPLIER' AND ap.id IS NOT NULL) OR (eu.companyProfile.companyType='EXPERT' AND ap.id IS NOT NULL)) 
                     ) OR 
                        (false=:#{#filter.isPartnerCompany} 
                        AND ((eu.companyProfile.companyType='SUPPLIER' AND ap.id IS NULL) OR (eu.companyProfile.companyType='EXPERT'))
                        )
                      )
                    )
                    GROUP BY eu.status
                    
    """)
    fun findStatsByCriteria(
        @Param("filter")
        filter: ExpertUserDetailsFilter
    ): List<List<Any>>


    @Query(
        value = """
            SELECT e FROM expert_user e LEFT JOIN e.companyProfile.associatedPartners ap
            WHERE (e.id = :#{#id} and ap.id = :#{#partnerId} ) GROUP BY e.id
        """
    )
    fun searchByCompanyAndPartner(id: Long, partnerId: Long): List<ExpertUserEntity>
    fun findByCompanyProfile(companyProfile: ExpertCompanyProfileEntity): List<ExpertUserReferenceData>
    fun findByStatusAndCompanyProfileIn(
        active: AccountStatus,
        expertCompanies: List<ExpertCompanyProfileEntity>
    ): List<ExpertUserReferenceData>

    @Query(
        value = """
        SELECT co.id, co.name FROM case_assignee ca 
        JOIN cases c ON c.id=ca.case_id 
        JOIN corporate_user cu ON c.created_by=cu.id
        JOIN corporate co ON co.id=cu.corporate_id
        WHERE ca.expert_id IN :#{#expertUserIds}
        GROUP BY co.id
    """, nativeQuery = true
    )
    fun findCaseAssigneeOwnerCompanies(expertUserIds: List<Long>): List<Map<String, Any>>

    @Query(
        value = """
        SELECT la.id, la.email, la.first_name, la.last_name, la.status FROM case_assignee ca 
        JOIN cases c ON c.id=ca.case_id 
        JOIN corporate_user cu ON c.created_by=cu.id
        JOIN corporate co ON co.id=cu.corporate_id
        JOIN login_account la ON la.id=cu.id
        WHERE ca.expert_id IN :#{#expertUserIds} AND co.id=:#{#corporateId}
        GROUP BY la.id
    """, nativeQuery = true
    )
    fun findCaseAssigneeOwnerCompanyUsers(expertUserIds: List<Long>, corporateId: Long): List<Map<String, Any>>
}
