package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.CountryView
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CountryViewRepository : JpaRepository<CountryView, Long> {
    fun findAllByCountryCode(countryCode: String): List<CountryView>
    fun findAllByCountryStartsWith(prefix: String): List<CountryView>
}