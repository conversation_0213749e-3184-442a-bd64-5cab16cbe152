package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.CircleView
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.data.pojo.circle.CircleSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CircleViewRepository : JpaRepository<CircleView, Long> {

    @Query(
        value = """
            SELECT vw FROM  CircleView vw
            WHERE
            (:#{#filter.search} IS NULL OR vw.name LIKE :#{#filter.search} OR vw.about LIKE :#{#filter.search}) AND
            (:#{#filter.circleType} IS NULL OR vw.circleType = :#{#filter.circleType}) AND 
            (:#{#filter.status} IS NULL OR vw.status = :#{#filter.status}) AND
            (:#{#filter.from} IS NULL OR vw.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR vw.createdDate <= :#{#filter.to}) AND
            (:#{#filter.countryCode} IS NULL OR vw.countryCodesString like :#{#filter.countryCode}) AND
            (:#{#filter.expertise} IS NULL OR vw.expertiseId = :#{#filter.expertise}) 
            group by vw.id
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: CircleSearchFilter,
        pageable: Pageable
    ): Page<CircleView>

    @Query(
        value = """
            SELECT COUNT(DISTINCT vw.id) AS num, vw.status, vw.circleType
            FROM CircleView vw
            WHERE
            (:#{#filter.search} IS NULL OR vw.name LIKE :#{#filter.search} OR vw.about LIKE :#{#filter.search}) AND
            (:#{#filter.circleType} IS NULL OR vw.circleType = :#{#filter.circleType}) AND 
            (:#{#filter.status} IS NULL OR vw.status = :#{#filter.status}) AND
            (:#{#filter.from} IS NULL OR vw.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR vw.createdDate <= :#{#filter.to}) AND
            (:#{#filter.countryCode} IS NULL OR vw.countryCodesString like :#{#filter.countryCode}) AND
            (:#{#filter.expertise} IS NULL OR vw.expertiseId = :#{#filter.expertise})
            GROUP BY vw.status, vw.circleType, vw.id
        """
    )
    fun findStatsByCriteria(
        @Param("filter")
        filter: CircleSearchFilter
    ): List<List<Any>>

    @Query(
        value = """
            SELECT vw FROM  CircleView vw
            WHERE
            (
            (vw.circleType = :#{#type})
             or (vw.id in :#{#joinedCircleIds} AND vw.status = :#{#status})) AND 
            (:#{#filter.circleType} IS NULL OR vw.circleType = :#{#filter.circleType}) AND
            (:#{#filter.status} IS NULL OR vw.status = :#{#filter.status}) AND
            (:#{#filter.from} IS NULL OR vw.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR vw.createdDate <= :#{#filter.to}) AND
            (:#{#filter.search} IS NULL OR vw.name LIKE :#{#filter.search} OR vw.about LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL OR vw.countryCodesString like :#{#filter.countryCode}) AND
            (:#{#filter.expertise} IS NULL OR vw.expertiseId = :#{#filter.expertise})
            group by vw.id
        """
    )
    fun searchForExpertByCriteria(
        @Param("filter")
        filter: CircleSearchFilter,
        @Param("joinedCircleIds")
        joinedCircleIds: Set<Long>,
        @Param("status")
        status: CircleStatus,
        @Param("type")
        type: CircleType,
        pageable: Pageable
    ): Page<CircleView>

    fun findFirstById(circleId: Long): CircleView?
}