package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.CaseTrackingView
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDate

@Repository
interface CaseTrackingViewRepository : JpaRepository<CaseTrackingView, Long> {

    @Query("""
        SELECT ctv FROM CaseTrackingView ctv
        WHERE 
            DATE(ctv.date) = :#{#date} AND
            (
                (ctv.caseOwner IN (:#{#caseOwners}) AND ctv.notificationType <> 'COMPANY_DOCUMENT_EXPIRY') OR 
                (ctv.referenceId = :#{#referenceId} AND ctv.notificationType = 'COMPANY_DOCUMENT_EXPIRY')
            )
        
        GROUP BY ctv.id
        
    """)
    fun findByDateAndCaseOwnerIn(date: LocalDate, caseOwners: List<Long>, referenceId: Long?): List<CaseTrackingView>
}