package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.entity.view.UserView
import com.centuroglobal.shared.data.pojo.client.ClientSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface UserViewRepository : JpaRepository<UserView, Long> {


    @Query(
        value = """
            SELECT vw FROM  ClientView vw
            WHERE
            (:#{#filter.search} IS NULL OR vw.fullName LIKE :#{#filter.search} OR vw.company LIKE :#{#filter.search} OR vw.email LIKE :#{#filter.search}) AND
            (:#{#filter.userType} IS NULL OR vw.userType = :#{#filter.userType}) AND 
            (:#{#filter.status} IS NULL OR vw.status = :#{#filter.status}) AND
            (:#{#filter.from} IS NULL OR vw.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR vw.createdDate <= :#{#filter.to}) AND 
            (COALESCE(:#{#filter.countryCodes},NULL) IS NULL OR vw.countryCode in :#{#filter.countryCodes}) AND
            (COALESCE(:#{#filter.expertiseIds},NULL) IS NULL OR vw.expertiseId in :#{#filter.expertiseIds})
            group by vw.userId
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: ClientSearchFilter,
        pageable: Pageable
    ): Page<ClientView>



}