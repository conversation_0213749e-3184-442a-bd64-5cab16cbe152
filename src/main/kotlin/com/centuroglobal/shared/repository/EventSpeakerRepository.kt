package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.EventSpeakerEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface EventSpeakerRepository : JpaRepository<EventSpeakerEntity, Long> {
    fun findAllByEvent_Id(eventId: Long, pageable: Pageable): Page<EventSpeakerEntity>
    fun findFirstByEventIdAndInternalId(eventId: Long, internalId: String): EventSpeakerEntity?
}