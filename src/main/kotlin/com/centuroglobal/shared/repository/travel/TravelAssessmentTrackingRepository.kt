package com.centuroglobal.shared.repository.travel

import com.centuroglobal.shared.data.entity.travel.TravelAssessmentTrackingEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface TravelAssessmentTrackingRepository: JpaRepository<TravelAssessmentTrackingEntity, Long>  {

    @Query(value = """
        SELECT tat FROM travel_assessment_tracking tat
        WHERE :#{#assessmentId} = tat.assessment.id
        order by tat.id
    """)
    fun findByAssessmentId(
        assessmentId: Long
    ): List<TravelAssessmentTrackingEntity>

}
