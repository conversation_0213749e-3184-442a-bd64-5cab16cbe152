package com.centuroglobal.shared.repository.travel

import com.centuroglobal.shared.data.entity.travel.TravelAssessmentEntity
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.repository.BaseRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface TravelAssessmentRepository: BaseRepository<TravelAssessmentEntity, Long> {

    @Query(value = """
        SELECT ta FROM travel_assessment ta
        WHERE(
        ta.status = 'COMPLETE' AND
        (:#{#searchFilter.purpose} IS NULL OR ta.purpose LIKE :#{#searchFilter.purpose}) AND
        (:#{#searchFilter.originCountry} IS NULL OR ta.originCountry LIKE :#{#searchFilter.originCountry}) AND
        (:#{#searchFilter.destinationCountry} IS NULL OR ta.destinationCountry LIKE :#{#searchFilter.destinationCountry}) AND
        (:#{#searchFilter.applicant} IS NULL OR ta.createdBy = :#{#searchFilter.applicant}) AND
        (:#{#searchFilter.id} IS NULL OR ta.id = :#{#searchFilter.id}) AND
        (
        (:#{#searchFilter.assessmentType} IS NULL) OR
        (:#{#searchFilter.assessmentType} = 'MYSELF' AND (ta.createdBy = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.assessmentType} = 'TEAM_MEMBER' AND ta.createdBy IN :#{#userIds} AND ta.createdBy != :#{#searchFilter.currentUser})
        ) AND
        (:#{#searchFilter.partnerId} IS NULL OR ta.partner.id = :#{#searchFilter.partnerId}) AND
        ((:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR (ta.createdDate BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate})))
        group by ta.id
    """)
    override fun searchByCriteria(
        searchFilter: AbstractSearchFilter,
        userIds: List<Long>,
        pageable: Pageable
    ): Page<TravelAssessmentEntity>


    @Query(value = """
        SELECT ta FROM travel_assessment ta
        WHERE(
        ta.status = 'COMPLETE' AND
        (:#{#searchFilter.purpose} IS NULL OR ta.purpose LIKE :#{#searchFilter.purpose}) AND
        (:#{#searchFilter.originCountry} IS NULL OR ta.originCountry LIKE :#{#searchFilter.originCountry}) AND
        (:#{#searchFilter.destinationCountry} IS NULL OR ta.destinationCountry LIKE :#{#searchFilter.destinationCountry}) AND
        (:#{#searchFilter.applicant} IS NULL OR ta.user.id = :#{#searchFilter.applicant}) AND
        (:#{#searchFilter.corporate} IS NULL OR ta.user.corporate.id = :#{#searchFilter.corporate}) AND
        (:#{#searchFilter.id} IS NULL OR ta.id = :#{#searchFilter.id}) AND
        (
        (:#{#searchFilter.assessmentType} IS NULL) OR
        (:#{#searchFilter.assessmentType} = 'MYSELF' AND (ta.createdBy = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.assessmentType} = 'TEAM_MEMBER' AND ta.createdBy != :#{#searchFilter.currentUser} AND ta.creatorRole IN :#{#searchFilter.roles})
        ) AND
        (:#{#searchFilter.partnerId} IS NULL OR ta.partner.id = :#{#searchFilter.partnerId}) AND
        ((:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR (ta.createdDate BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate})))
        group by ta.id
    """)
    override fun searchByCriteriaForAdmin(
        searchFilter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<TravelAssessmentEntity>

    @Query(value = """
        SELECT ta FROM travel_assessment ta
        WHERE(
        ta.status = 'COMPLETE' AND
        (:#{#searchFilter.purpose} IS NULL OR ta.purpose LIKE :#{#searchFilter.purpose}) AND
        (:#{#searchFilter.originCountry} IS NULL OR ta.originCountry LIKE :#{#searchFilter.originCountry}) AND
        (:#{#searchFilter.destinationCountry} IS NULL OR ta.destinationCountry LIKE :#{#searchFilter.destinationCountry}) AND
        (:#{#searchFilter.applicant} IS NULL OR ta.createdBy = :#{#searchFilter.applicant}) AND
        (:#{#searchFilter.id} IS NULL OR ta.id = :#{#searchFilter.id}) AND
        (
        (:#{#searchFilter.assessmentType} IS NULL) OR
        (:#{#searchFilter.assessmentType} = 'MYSELF' AND (ta.createdBy = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.assessmentType} = 'TEAM_MEMBER' AND ta.user.corporate.id = :#{#searchFilter.loggedInUserCorporateId} AND ta.creatorRole='ROLE_CORPORATE' AND  ta.createdBy != :#{#searchFilter.currentUser})
        ) AND
        (:#{#searchFilter.partnerId} IS NULL OR ta.partner.id = :#{#searchFilter.partnerId}) AND
        ((:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR (ta.createdDate BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate})))
        group by ta.id
    """)
    override fun searchByCriteriaForFullAccess(
        searchFilter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<TravelAssessmentEntity>

    @Query(value = """
        SELECT t FROM travel_assessment t
        WHERE t.id=:#{#id} AND
        t.createdBy IN (:#{#createdByIn})
    """)
    override fun findByIdAndCreatedByIdIn(id: Long, createdByIn: MutableList<Long>): Optional<TravelAssessmentEntity>

    /**
     * override default methods
     */

    @Query(value = """
            SELECT t FROM travel_assessment t
            WHERE
            t.id=:#{#id} AND t.user.corporate.id=:#{#corporateId}
    """)
    override fun findByIdAndCreatedByCorporateId(id: Long, corporateId: Long): Optional<TravelAssessmentEntity>
    fun findBySessionId(sessionId: String): Optional<TravelAssessmentEntity>
}
