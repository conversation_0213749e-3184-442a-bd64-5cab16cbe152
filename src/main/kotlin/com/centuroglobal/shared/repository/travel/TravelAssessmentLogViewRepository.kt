package com.centuroglobal.shared.repository.travel

import com.centuroglobal.shared.data.entity.travel.view.TravelAssessmentLogView
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentLogsSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface TravelAssessmentLogViewRepository: JpaRepository<TravelAssessmentLogView, Long> {

    @Query(value = """
        SELECT ta FROM TravelAssessmentLogView ta
        WHERE
        (:#{#searchFilter.destination} IS NULL OR ta.destination = :#{#searchFilter.destination}) AND
        (:#{#searchFilter.corporateId} IS NULL OR ta.corporateId = :#{#searchFilter.corporateId}) AND
        (:#{#searchFilter.visaType} IS NULL OR ta.visaType = :#{#searchFilter.visaType}) AND
        (:#{#searchFilter.partner} IS NULL OR ta.partnerId = :#{#searchFilter.partner}) AND
        ((:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR (ta.createdAt BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate})) AND
        ((false=:#{#searchFilter.isPartner} AND ta.partnerId IS NULL) OR (true=:#{#searchFilter.isPartner} AND ta.partnerId IS NOT NULL))
        group by ta.id
    """)
    fun searchByCriteria(
        searchFilter: TravelAssessmentLogsSearchFilter,
        pageable: Pageable
    ): Page<TravelAssessmentLogView>

}
