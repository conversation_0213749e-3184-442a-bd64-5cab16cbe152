package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseDocumentFileEntity
import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseDocumentFileRepository : JpaRepository<CaseDocumentFileEntity, Long> {
    fun findByIdAndCaseDocument(fileId: Long?, document: CaseDocumentsEntity): CaseDocumentFileEntity?
    fun findByCaseDocument(document: CaseDocumentsEntity): List<CaseDocumentFileEntity>

}