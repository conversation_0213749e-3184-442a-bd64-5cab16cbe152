package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.AdminAuthoritiesEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface AdminAuthoritiesRepository : JpaRepository<AdminAuthoritiesEntity, Long> {
    fun findAllByUserId(userId: Long): MutableList<AdminAuthoritiesEntity>
    fun findAllByUserIdAndHasAccess(userId: Long, hasAccess: Boolean): MutableList<AdminAuthoritiesEntity>
    fun deleteAllByUserId(userId: Long)
}