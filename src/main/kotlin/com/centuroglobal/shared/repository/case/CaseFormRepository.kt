package com.centuroglobal.shared.repository.case

import com.centuroglobal.shared.data.entity.CaseFormEntity
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.repository.BaseRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface CaseFormRepository : BaseRepository<CaseFormEntity, Long> {


    @Query(value = """
        SELECT cf FROM case_form cf JOIN cf.countries c
        WHERE
        (:#{#filter.search} IS NULL OR cf.name LIKE :#{#filter.search}) AND
        (:#{#filter.country} IS NULL OR c.code = :#{#filter.country}) AND
        (:#{#filter.status} IS NULL OR cf.status = :#{#filter.status}) AND
        (:#{#filter.category} IS NULL OR cf.category = :#{#filter.category}) AND
        (:#{#filter.updatedBy} IS NULL OR cf.updatedBy = :#{#filter.updatedBy}) AND
        (:#{#filter.visibility} IS NULL OR cf.visibility = :#{#filter.visibility}) AND
        (:#{#filter.partnerId} IS NULL OR (cf.partner.id = :#{#filter.partnerId} AND true=:#{#filter.isPartner}) OR (false=:#{#filter.isPartner} AND cf.partner.id IS NULL)) AND
        ((false=:#{#filter.isPartner} AND cf.partner.id IS NULL) OR (true=:#{#filter.isPartner} AND cf.partner.id IS NOT NULL))
        
        group by cf.id
    """)
    override fun searchByCriteriaForAdmin(filter: AbstractSearchFilter, pageable: Pageable): Page<CaseFormEntity>

    @Query(value = """
        SELECT cf FROM case_form cf JOIN cf.countries c
        WHERE
        (:#{#filter.search} IS NULL OR cf.name LIKE :#{#filter.search}) AND
        (:#{#filter.country} IS NULL OR c.code = :#{#filter.country}) AND
        (:#{#filter.status} IS NULL OR cf.status = :#{#filter.status}) AND
        (:#{#filter.category} IS NULL OR cf.category = :#{#filter.category}) AND
        (:#{#filter.updatedBy} IS NULL OR cf.updatedBy = :#{#filter.updatedBy}) AND
        (:#{#filter.visibility} IS NULL OR cf.visibility = :#{#filter.visibility}) AND
        (:#{#filter.partnerId} IS NULL OR (cf.partner.id = :#{#filter.partnerId} AND true=:#{#filter.isPartner}) OR (false=:#{#filter.isPartner} AND cf.partner.id IS NULL)) AND
        ((false=:#{#filter.isPartner} AND cf.partner.id IS NULL) OR (true=:#{#filter.isPartner} AND cf.partner.id IS NOT NULL)) AND
        :#{#userIds} IS NOT NULL
        
        group by cf.id
    """)
    override fun searchByCriteria(
        filter: AbstractSearchFilter,
        userIds: List<Long>,
        pageable: Pageable
    ): Page<CaseFormEntity>

    @Query(value = """
            SELECT cf FROM case_form cf
            WHERE
            cf.id=:#{#id} AND
            (
                (cf.partner.id IS NOT NULL AND :#{#partnerId} IS NOT NULL AND cf.partner.id=:#{#partnerId}) OR
                (cf.partner.id IS NULL AND :#{#partnerId} IS NOT NULL AND cf.visibility='PUBLIC') OR
                (:#{#partnerId} IS NULL)
             )
    """)
    fun getFormForView(id: Long, partnerId: Long?): CaseFormEntity?

    @Query(value = """
        SELECT cf FROM case_form cf JOIN cf.countries c
        WHERE
        (:#{#filter.search} IS NULL OR cf.name LIKE :#{#filter.search}) AND
        (:#{#filter.country} IS NULL OR c.code = :#{#filter.country}) AND
        (:#{#filter.status} IS NULL OR cf.status = :#{#filter.status}) AND
        (:#{#filter.category} IS NULL OR cf.category = :#{#filter.category}) AND
        (:#{#filter.updatedBy} IS NULL OR cf.updatedBy = :#{#filter.updatedBy}) AND
        (:#{#filter.visibility} IS NULL OR cf.visibility = :#{#filter.visibility}) AND
        (:#{#filter.partnerId} IS NULL OR (cf.partner.id = :#{#filter.partnerId} AND true=:#{#filter.isPartner}) OR (false=:#{#filter.isPartner} AND cf.partner.id IS NULL)) AND
        ((false=:#{#filter.isPartner} AND cf.partner.id IS NULL) OR (true=:#{#filter.isPartner} AND cf.partner.id IS NOT NULL))
        
        group by cf.id
    """)
    override fun searchByCriteriaForFullAccess(
        filter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<CaseFormEntity>

    // TODO not used currently

    @Query(value = """
        SELECT cf FROM case_form cf
        WHERE cf.id=:#{#id} AND 
        cf.createdBy IN (:#{#createdByIn})
    """)
    override fun findByIdAndCreatedByIdIn(id: Long, createdByIn: MutableList<Long>): Optional<CaseFormEntity>

    /**
     * override default methods
     */

    @Query(value = """
            SELECT cf FROM case_form cf
            WHERE
            cf.id=:#{#id} AND cf.createdBy=:#{#corporateId}
    """)
    override fun findByIdAndCreatedByCorporateId(id: Long, corporateId: Long): Optional<CaseFormEntity>

}