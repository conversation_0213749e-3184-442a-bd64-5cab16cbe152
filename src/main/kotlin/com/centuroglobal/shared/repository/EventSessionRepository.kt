package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.EventSessionEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface EventSessionRepository : JpaRepository<EventSessionEntity, Long> {
    fun findAllByEvent_Id(eventId: Long, pageable: Pageable): Page<EventSessionEntity>

    @Query(
        """
        select es from event_session es where es.event.id = ?1 group by es.date
    """
    )
    fun findAllByDateGroup(eventId: Long): List<EventSessionEntity>

    fun findAllByEvent_IdAndDate(eventId: Long, date: Date): List<EventSessionEntity>
}