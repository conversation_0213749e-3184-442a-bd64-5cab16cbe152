package com.centuroglobal.costofliving.service

import com.centuroglobal.costofliving.client.NumbeoClient
import com.centuroglobal.costofliving.data.constant.Constant
import com.centuroglobal.costofliving.data.entity.CityEntity
import com.centuroglobal.costofliving.data.entity.CityPriceEntity
import com.centuroglobal.costofliving.data.entity.ExchangeRateEntity
import com.centuroglobal.costofliving.data.entity.ItemEntity
import com.centuroglobal.costofliving.data.payload.CityPriceDto
import com.centuroglobal.costofliving.data.payload.CityPriceList
import com.centuroglobal.costofliving.data.payload.Currency
import com.centuroglobal.costofliving.data.payload.PriceDto
import com.centuroglobal.costofliving.repository.CityPriceRepository
import com.centuroglobal.costofliving.repository.CityRepository
import com.centuroglobal.costofliving.repository.ExchangeRatesRepository
import com.centuroglobal.costofliving.repository.ItemsRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Collectors

@Service
class CostOfLivingService(
    private val cityRepository: CityRepository,
    private val exchangeRatesRepository: ExchangeRatesRepository,
    private val cityPriceRepository: CityPriceRepository,
    private val itemsRepository: ItemsRepository,
    @Value("\${app.costOfLiving.apiKey}")
    private val apiKey: String,
    @Value("\${app.costOfLiving.expiry.city_price_days}")
    private val cityPriceExpiryDay: Long,
    @Value("\${app.costOfLiving.expiry.city_day}")
    private val cityExpiryDay: Long
) {
    @Autowired
    lateinit var numbeoClient: NumbeoClient

    @Transactional
    fun getCityList(countryCode: String): List<CityEntity> {
        val date = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS).minusDays(cityExpiryDay)
        val cityList = cityRepository.findAllByCountryCodeAndCreatedDateGreaterThan(countryCode, date)
        if (cityList.isEmpty()) {
            val data = numbeoClient.findAllCity(apiKey, countryCode)
            return data.cities.map { it ->
                cityRepository.deleteByCityId(it.city_id)
                val cityData = CityEntity(
                    country = it.country, countryCode = countryCode, city = it.city,
                    cityId = it.city_id, latitude = it.latitude, longitude = it.longitude
                )
                cityRepository.save(cityData)
            }.toList()
        }
        return cityList
    }

    @Transactional
    fun updateExchangeRatesList(): List<ExchangeRateEntity> {
        val data = numbeoClient.findAllExchangeRates(apiKey)
        return data.exchange_rates.map { it ->
            val exchangeRatesData = ExchangeRateEntity(
                currencyType = it.currency, conversionRateAgainstUsd = it.one_usd_to_currency.toString()
            )
            exchangeRatesRepository.save(exchangeRatesData)
        }.toList()
    }

    @Transactional
    fun getCityPrice(
        sourceCountryCode: String, sourceCityId: Long, currency: String, targetCountryCode: String,
        targetCityId: Long
    ): MutableMap<String?, MutableList<CityPriceDto>?> {
        val date = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS).minusDays(cityPriceExpiryDay)

        val itemsList = itemsRepository.findAll()
        val itemMap = itemsList.stream()
            .collect(Collectors.toMap<ItemEntity, Long, String>(ItemEntity::itemId, ItemEntity::category))

        val sourceCityPriceList: List<CityPriceEntity> =
            getCityPriceList(sourceCityId, sourceCountryCode, date, itemMap)
        val sourceCountryPriceList: List<CityPriceEntity> = getCountryPriceList(sourceCountryCode, date, itemMap)
        val sourceCountryMap = sourceCountryPriceList.stream().collect(Collectors.groupingBy(CityPriceEntity::itemId))
        val sourceCityMap: MutableMap<Long, MutableList<CityPriceEntity>> =
            getCityMap(sourceCityPriceList, sourceCountryMap)

        var targetCountryMap: MutableMap<Long, MutableList<CityPriceEntity>> = HashMap()
        var targetCityMap: MutableMap<Long, MutableList<CityPriceEntity>> = HashMap()
        if (targetCountryCode.isNotEmpty()) {
            val targetCityPriceList: List<CityPriceEntity> =
                getCityPriceList(targetCityId, targetCountryCode, date, itemMap)
            val targetCountryPriceList: List<CityPriceEntity> = getCountryPriceList(targetCountryCode, date, itemMap)
            targetCountryMap = targetCountryPriceList.stream().collect(Collectors.groupingBy(CityPriceEntity::itemId))
            targetCityMap = getCityMap(targetCityPriceList, targetCountryMap)
        }

        val conversionRate = getConversionRate(currency)

        return getCityPriceMap(
            itemsList,
            conversionRate,
            sourceCityMap,
            sourceCountryMap,
            targetCityMap,
            targetCountryMap
        )
    }

    private fun getCityPriceMap(
        itemsList: MutableList<ItemEntity>,
        conversionRate: Double,
        sourceCityMap: MutableMap<Long, MutableList<CityPriceEntity>>,
        sourceCountryMap: MutableMap<Long, MutableList<CityPriceEntity>>,
        targetCityMap: MutableMap<Long, MutableList<CityPriceEntity>>,
        targetCountryMap: MutableMap<Long, MutableList<CityPriceEntity>>
    ): MutableMap<String?, MutableList<CityPriceDto>?> {

        val cityPriceMap: MutableMap<String?, MutableList<CityPriceDto>?> = HashMap()
        for (item in itemsList) {
            if (!cityPriceMap.containsKey(item.category)) {
                cityPriceMap[item.category] = ArrayList()
            }
            val sourcePriceDto: PriceDto? = getPriceDto(sourceCityMap, sourceCountryMap, conversionRate, item.itemId)
            var targetPriceDto: PriceDto? = null
            if (targetCountryMap.isNotEmpty()) {
                targetPriceDto = getPriceDto(targetCityMap, targetCountryMap, conversionRate, item.itemId)
            }

            val dto = CityPriceDto(
                itemId = item.itemId,
                itemName = item.name,
                source = sourcePriceDto,
                target = targetPriceDto
            )
            cityPriceMap[item.category]!!.add(dto)
        }
        return cityPriceMap
    }

    private fun getPriceDto(
        cityMap: MutableMap<Long, MutableList<CityPriceEntity>>, countryMap: MutableMap<Long,
                MutableList<CityPriceEntity>>, conversionRate: Double, itemId: Long?
    ): PriceDto? {
        var priceDto: PriceDto? = null
        var value = cityMap[itemId]
        if (value == null) {
            value = countryMap[itemId]
        }
        if (value != null) {
            val city = value[0]
            priceDto = PriceDto(
                lowestPrice = city.lowestPrice * conversionRate,
                averagePrice = city.averagePrice * conversionRate,
                highestPrice = city.highestPrice * conversionRate
            )
        }
        return priceDto
    }

    private fun getCityMap(
        sourceCityPriceList: List<CityPriceEntity>, sourceCountryMap: MutableMap<Long,
                MutableList<CityPriceEntity>>
    ): MutableMap<Long, MutableList<CityPriceEntity>> {
        var cityMap: MutableMap<Long, MutableList<CityPriceEntity>> = HashMap()
        if (sourceCityPriceList.isNotEmpty()) {
            cityMap = sourceCityPriceList.stream().collect(Collectors.groupingBy(CityPriceEntity::itemId))
        }
        if (cityMap.isEmpty()) {
            cityMap = sourceCountryMap
        }
        return cityMap
    }

    private fun getConversionRate(currency: String): Double {
        var conversionRate = 1.0
        if (currency != Constant.CURRENCY_USD) {
            val exchangeRates = exchangeRatesRepository.findAllByCurrencyType(currency)
            conversionRate = exchangeRates.conversionRateAgainstUsd.toDouble()
        }
        return conversionRate
    }

    private fun getCountryPriceList(countryCode: String, date: LocalDateTime?, itemMap: MutableMap<Long, String>):
            List<CityPriceEntity> {
        var countryPriceList =
            cityPriceRepository.findAllByCityIdAndCountryCodeAndCreatedDateGreaterThan(99999L, countryCode, date)
        if (countryPriceList.isEmpty()) {
            val data = numbeoClient.findAllCountryPrice(apiKey, countryCode, Constant.CURRENCY_USD)
            countryPriceList = savePriceData(data, countryCode, null, itemMap)
        }
        return countryPriceList
    }

    private fun getCityPriceList(
        cityId: Long,
        countryCode: String,
        date: LocalDateTime?,
        itemMap: MutableMap<Long, String>
    ): List<CityPriceEntity> {
        var cityPriceList: List<CityPriceEntity> = ArrayList()
        if (cityId != 99999L) {
            cityPriceList =
                cityPriceRepository.findAllByCityIdAndCountryCodeAndCreatedDateGreaterThan(cityId, countryCode, date)
            if (cityPriceList.isEmpty()) {
                val city = cityRepository.findAllByCityId(cityId)
                val data = numbeoClient.findAllCityPrice(apiKey, cityId, Constant.CURRENCY_USD)
                cityPriceRepository.deleteByCityId(cityId)
                cityPriceList = savePriceData(data, countryCode, city, itemMap)
            }
        }
        return cityPriceList
    }

    private fun savePriceData(
        data: CityPriceList,
        countryCode: String,
        city: CityEntity?,
        itemMap: MutableMap<Long, String>
    ): List<CityPriceEntity> {
        val cityPriceList = data.prices.map { it ->
            val cityPriceData = CityPriceEntity(
                countryCode = countryCode,
                country = if (data.city_id == 0L) data.name else city?.country,
                city = if (data.city_id == 0L) "All" else city?.city,
                cityId = if (data.city_id == 0L) 99999 else data.city_id,
                category = itemMap[it.item_id],
                itemId = it.item_id,
                itemName = it.item_name,
                lowestPrice = it.lowest_price,
                averagePrice = it.average_price,
                highestPrice = it.highest_price,
                monthLastUpdate = data.monthLastUpdate,
                yearLastUpdate = data.yearLastUpdate
            )
            cityPriceRepository.save(cityPriceData)
        }.toList()
        return cityPriceList
    }

    fun getCurrencies(): List<Currency>? {
        return exchangeRatesRepository.findAll().map { Currency("", it.currencyType) }.toList()
    }
}