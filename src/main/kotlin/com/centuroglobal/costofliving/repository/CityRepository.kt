package com.centuroglobal.costofliving.repository

import com.centuroglobal.costofliving.data.entity.CityEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface CityRepository : JpaRepository<CityEntity, Long> {
    fun findAllByCountryCodeAndCreatedDateGreaterThan(countryCode: String, date: LocalDateTime?): List<CityEntity>
    fun findAllByCityId(cityId: Long): CityEntity
    fun deleteByCityId(cityId: Long)
}