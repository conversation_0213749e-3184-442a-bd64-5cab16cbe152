package com.centuroglobal.costofliving.repository

import com.centuroglobal.costofliving.data.entity.CityPriceEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface CityPriceRepository : JpaRepository<CityPriceEntity, Long> {
    fun findAllByCityIdAndCountryCodeAndCreatedDateGreaterThan(cityId: Long, countryCode: String, date: LocalDateTime? ): List<CityPriceEntity>
    fun deleteByCityId(cityId: Long)
}