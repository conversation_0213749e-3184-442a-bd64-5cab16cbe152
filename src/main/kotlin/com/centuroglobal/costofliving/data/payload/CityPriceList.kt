package com.centuroglobal.costofliving.data.payload

class CityPriceList {
    var name: String = ""
    var currency: String = ""
    var contributors12months: Long = 0
    var monthLastUpdate: Long = 0
    var contributors: Long = 0
    var yearLastUpdate: Long = 0
    var prices: List<Price> = emptyList()
    var city_id: Long = 0
}

class Price {
    var data_points: Long = 0
    var item_id: Long = 0
    var lowest_price: Double = 0.0
    var average_price: Double = 0.0
    var highest_price: Double = 0.0
    var item_name: String = ""
}