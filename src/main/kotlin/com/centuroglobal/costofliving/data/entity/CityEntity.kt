package com.centuroglobal.costofliving.data.entity

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*

@Entity(name = "city")
data class CityEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var countryCode: String?,

    @Column(nullable = false)
    var country: String?,

    @Column(nullable = false)
    var city: String?,

    @Column(nullable = false)
    var cityId: Long,

    @Column(nullable = false)
    var latitude: Double,

    @Column(nullable = false)
    var longitude: Double

) : AuditBaseEntity()