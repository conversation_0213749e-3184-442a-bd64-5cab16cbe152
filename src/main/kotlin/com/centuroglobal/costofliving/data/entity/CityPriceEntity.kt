package com.centuroglobal.costofliving.data.entity

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*

@Entity(name = "city_price")
data class CityPriceEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var countryCode: String?,

    @Column(nullable = false)
    var country: String?,

    @Column(nullable = false)
    var city: String?,

    @Column(nullable = false)
    var cityId: Long,

    @Column(nullable = false)
    var category: String? = null,

    @Column(nullable = false)
    var itemId: Long,

    @Column(nullable = false)
    var itemName: String? = null,

    @Column(nullable = false)
    var lowestPrice: Double,

    @Column(nullable = false)
    var averagePrice: Double,

    @Column(nullable = false)
    var highestPrice: Double,

    @Column(nullable = false)
    var monthLastUpdate: Long,

    @Column(nullable = false)
    var yearLastUpdate: Long

) : AuditBaseEntity()