package com.centuroglobal.util

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import mu.KotlinLogging

private val log = KotlinLogging.logger {}

class UserAccessUtil(

) {

    companion object {

        @JvmStatic
        fun getAccessesByKey(authenticatedUser: AuthenticatedUser, accessKey: String): MutableList<String> {
            val accesses = authenticatedUser.visibilities.filter { it.feature == accessKey }
            if(accesses.isEmpty()){
                log.error("User with id: ${authenticatedUser.userId} is not having $accessKey accesses.")
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
            return accesses[0].accesses
        }

        @JvmStatic
        fun hasAccessToCompanyDocuments(authenticatedUser: AuthenticatedUser): Boolean {

            if(AdminAccessUtil.isAdminOrPartner(authenticatedUser)) {
                return true
            }

            return authenticatedUser.accesses.firstOrNull { it.feature == "COMPANY_INFO" }?.accesses?.contains("VIEW_DOCUMENTS")
                ?: false
        }

    }


}