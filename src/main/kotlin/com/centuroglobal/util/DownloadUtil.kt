package com.centuroglobal.util

import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.util.DownloadWrapper
import com.centuroglobal.shared.util.MEDIA_TYPE_OCTET_STREAM
import org.springframework.http.ResponseEntity
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

class DownloadUtil {

    companion object {

        @JvmStatic
        fun copyFilesAsZip(fileNameInputStreamMap: Map<String, InputStream>): ByteArrayInputStream {

            val outputStream = ByteArrayOutputStream()
            val zipFilesSet = mutableSetOf<String>()
            ZipOutputStream(outputStream).use { zos ->
                fileNameInputStreamMap.forEach { (zipFileName, fileInputStream) ->

                    zipFilesSet.add(zipFileName)
                    zos.putNextEntry(ZipEntry(zipFileName))
                    zos.write(fileInputStream.readAllBytes())
                    zos.closeEntry()
                }
            }
            return ByteArrayInputStream(outputStream.toByteArray())
        }

        @JvmStatic
        fun downloadFile(fileName: String, inputStream: InputStream): ResponseEntity<StreamingResponseBody> {

            return try {
                DownloadWrapper.downloadFile(fileName, MEDIA_TYPE_OCTET_STREAM) { out: OutputStream ->
                    inputStream.copyTo(out) }
            } catch (ex: ApplicationException) {
                val streamError = StreamingResponseBody { out ->
                    out.write(ex.error.errorMessage.toByteArray(Charsets.UTF_8))
                    out.flush()
                    out.close()
                }
                ResponseEntity(streamError, ex.error.httpStatus!!)
            }

        }

    }



}