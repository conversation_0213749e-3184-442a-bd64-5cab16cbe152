package com.centuroglobal.util

import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Error
import com.centuroglobal.shared.exception.ApplicationException
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionException
import jakarta.servlet.http.HttpServletResponse

/**
 * One of the recommended ways to make this interoperable as Java static method
 * https://www.baeldung.com/kotlin-static-methods
 */
class ResponseUtil {
    companion object {

        @JvmStatic
        fun <T> toDeferredResult(f: CompletableFuture<T>, timeout: Long? = 30000L): DeferredResult<Response<T>> {
            val deferred: DeferredResult<Response<T>> = DeferredResult(timeout!!)

            f.whenComplete { res, ex ->
                if (ex != null) {
                    if (ex is CompletionException && ex.cause!=null) {
                        deferred.setErrorResult(ex.cause!!)
                    } else {
                        deferred.setErrorResult(ex)
                    }
                } else {
                    deferred.setErrorResult(Response(true, res))
                }
            }

            return deferred
        }

        @JvmStatic
        fun writeErrorResponseAsJson(objectMapper: ObjectMapper, response: HttpServletResponse, error: Error) {
            val responseEntity = toErrorResponseEntity(error)
            response.status = responseEntity.statusCodeValue
            response.contentType = MediaType.APPLICATION_JSON_VALUE
            response.writer.write(objectMapper.writeValueAsString(responseEntity.body))
            response.writer.flush()
            response.writer.close()
        }

        @JvmStatic
        fun writeResponseAsJson(objectMapper: ObjectMapper, response: HttpServletResponse, payload: Response<Any>) {
            response.status = HttpStatus.OK.value()
            response.contentType = MediaType.APPLICATION_JSON_VALUE
            response.writer.write(objectMapper.writeValueAsString(payload))
            response.writer.flush()
            response.writer.close()
        }

        @JvmStatic
        fun toErrorResponseEntity(ex: ApplicationException): ResponseEntity<Any> {
            return toErrorResponseEntity(ex.error)
        }

        @JvmStatic
        fun toErrorResponseEntity(error: Error): ResponseEntity<Any> {
            val response = Response<String>(success = false, error = error)
            return ResponseEntity(response, error.httpStatus ?: HttpStatus.INTERNAL_SERVER_ERROR)
        }

    }
}