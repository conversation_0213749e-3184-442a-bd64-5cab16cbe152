package com.centuroglobal.util

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import com.google.i18n.phonenumbers.PhoneNumberUtil

private val phoneNumberUtil = PhoneNumberUtil.getInstance()

class PhoneUtil {
    companion object {

        @JvmStatic
        fun validateAndFormatPhoneNumber(phoneNumberRaw: String, countryCode: String): String {
            val phone = phoneNumberUtil.parse(phoneNumberRaw, countryCode)
            if (!phoneNumberUtil.isValidNumberForRegion(phone, countryCode)) {
                throw ApplicationException(ErrorCode.PHONE_NUMBER_INVALID)
            }
            return phoneNumberUtil.format(phone, PhoneNumberUtil.PhoneNumberFormat.E164)
        }

        @JvmStatic
        fun getDialCode(countryCode: String): String? {
            val dialCode = "+${phoneNumberUtil.getCountryCodeForRegion(countryCode)}"
            return if (dialCode == "+1") {
                getNorthAmericaDialCode(countryCode)
            } else {
                dialCode
            }
        }

        private fun getNorthAmericaDialCode(countryCode: String): String {
            return when (countryCode) {
                "AS" -> "******"
                "AI" -> "******"
                "AG" -> "******"
                "BS" -> "******"
                "BB" -> "******"
                "BM" -> "******"
                "DM" -> "******"
                "DO" -> "******"
                "GD" -> "******"
                "GU" -> "******"
                "JM" -> "******"
                "MS" -> "******"
                "MP" -> "******"
                "PR" -> "******"
                "TT" -> "******"
                "TC" -> "******"
                "KN" -> "******"
                "LC" -> "******"
                "VC" -> "******"
                "VG" -> "******"
                "VI" -> "+1 340"
                else -> "+1"
            }
        }
    }
}
