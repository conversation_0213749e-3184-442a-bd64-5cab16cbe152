package com.centuroglobal.config

import com.centuroglobal.data.properties.AppCacheProperties
import com.centuroglobal.shared.data.properties.AwsS3Properties
import com.centuroglobal.data.properties.PasswordPolicyProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(
    value = [
        PasswordPolicyProperties::class,
        AwsS3Properties::class,
        AppCacheProperties::class
    ]
)
class PropertiesConfig