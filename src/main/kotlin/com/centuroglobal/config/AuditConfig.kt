package com.centuroglobal.config

import com.centuroglobal.shared.security.AuthenticatedUser
import mu.KotlinLogging
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.security.core.context.SecurityContextHolder
import java.util.*

private val log = KotlinLogging.logger {}

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorAware")
class AuditConfig() {

    @Bean
   fun auditorAware(): AuditorAware<Long> {
       return AuditorAwareImpl()
   }
}

class AuditorAwareImpl : AuditorAware<Long> {

    override fun getCurrentAuditor(): Optional<Long> {

        var authenticatedUser: AuthenticatedUser? = null
        return try {
            if(SecurityContextHolder.getContext().authentication is AuthenticatedUser) {
                authenticatedUser = SecurityContextHolder.getContext().authentication as AuthenticatedUser
                Optional.of(authenticatedUser.userId)
            }
            else{
                Optional.empty()
            }
        } catch (ex: Exception){
            log.error("Unexpected Exception", ex)
            Optional.empty()
        }
    }
}