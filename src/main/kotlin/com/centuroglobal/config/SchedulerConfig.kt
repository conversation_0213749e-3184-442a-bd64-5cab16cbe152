package com.centuroglobal.config

import com.centuroglobal.shared.data.enums.MdcAttribute
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.SchedulingConfigurer
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.scheduling.config.ScheduledTaskRegistrar
import java.util.*

@EnableScheduling
@Configuration
@Aspect
class SchedulerConfig(
    @Value("\${app.scheduler.task.pool-size}")
    private val taskPoolSize: Int
) : SchedulingConfigurer {

    override fun configureTasks(taskRegistrar: ScheduledTaskRegistrar) {
        val taskScheduler = ThreadPoolTaskScheduler()
        taskScheduler.poolSize = taskPoolSize
        taskScheduler.initialize()
        taskRegistrar.setTaskScheduler(taskScheduler)
    }

    @Throws(Throwable::class)
    @Around("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    fun injectMDC(joinPoint: ProceedingJoinPoint): Any? {
        val result: Any?
        try {
            MDC.put(MdcAttribute.XID.key, UUID.randomUUID().toString())
            MDC.put(MdcAttribute.CONTROLLER_NAME.key, joinPoint.target.javaClass.simpleName)
            MDC.put(MdcAttribute.ACTION_NAME.key, joinPoint.signature.name)

            result = joinPoint.proceed()

        } catch (e: Throwable) {
            throw e
        } finally {
            MDC.clear()
        }
        return result
    }
}