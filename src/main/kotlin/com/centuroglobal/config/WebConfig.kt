package com.centuroglobal.config

import com.centuroglobal.interceptor.LoggingInterceptor
import com.centuroglobal.shared.data.AppConstant.REQUEST_ID_HEADER
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.task.AsyncTaskExecutor
import org.springframework.data.web.config.EnableSpringDataWebSupport
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor
import org.springframework.web.servlet.config.annotation.*
import java.util.concurrent.Callable
import jakarta.servlet.http.HttpServletRequest

private val log = KotlinLogging.logger {}

@Configuration
@EnableSpringDataWebSupport
@EnableWebMvc
class WebConfig(
    @Qualifier("genericExecutor")
    private val asyncTaskExecutor: AsyncTaskExecutor,
    private val loggingInterceptor: LoggingInterceptor,
    @Value("\${spring.mvc.async.request-timeout}")
    private val asyncTimeout: Long
) : WebMvcConfigurationSupport() {

    @Bean
    fun objectMapper(): ObjectMapper {
        val objectMapper = ObjectMapper()
        objectMapper.registerModule(KotlinModule.Builder().build())
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
        return objectMapper
    }

    override fun addCorsMappings(registry: CorsRegistry) {
        registry.addMapping("/**")
            .allowedOrigins("*")
            .allowedHeaders("*")
            .allowedMethods("*")
            .exposedHeaders("Content-Type", "Accept", "Authorization", REQUEST_ID_HEADER)
    }

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(loggingInterceptor)
    }

    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        // swagger-ui resources
        registry.addResourceHandler("swagger-ui.html")
            .addResourceLocations("classpath:/META-INF/resources/")
        registry.addResourceHandler("/webjars/**")
            .addResourceLocations("classpath:/META-INF/resources/webjars/")
    }

    override fun configureAsyncSupport(configurer: AsyncSupportConfigurer) {
        configurer.setDefaultTimeout(asyncTimeout)
        configurer.setTaskExecutor(asyncTaskExecutor)
        configurer.registerCallableInterceptors(CallableProcessingInterceptor())
    }

    private class CallableProcessingInterceptor : TimeoutCallableProcessingInterceptor() {
        override fun <T : Any?> handleTimeout(request: NativeWebRequest, task: Callable<T>): Any {
            val uri = if (request.nativeRequest is HttpServletRequest) {
                " - ${(request.nativeRequest as HttpServletRequest).requestURI}"
            } else {
                ""
            }
            log.error("Async mvc timout detected!!!$uri")
            return super.handleTimeout(request, task)
        }
    }
}