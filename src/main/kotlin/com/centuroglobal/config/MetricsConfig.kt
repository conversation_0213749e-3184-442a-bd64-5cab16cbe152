//package com.centuroglobal.config
//
////import io.micrometer.prometheus.PrometheusMeterRegistry
//import io.micrometer.prometheus.PrometheusMeterRegistry
//import org.springframework.beans.factory.InitializingBean
//import org.springframework.beans.factory.config.BeanPostProcessor
//import org.springframework.context.annotation.Bean
//import org.springframework.context.annotation.Configuration
//
//@Configuration
//class MetricsConfig {
//
//    @Bean
//    fun forcePrometheusPostProcessor(
//        meterRegistryPostProcessor: BeanPostProcessor,
//        registry: PrometheusMeterRegistry?
//    ): InitializingBean {
//        return InitializingBean { meterRegistryPostProcessor.postProcessAfterInitialization(registry!!, "") }
//    }
//}