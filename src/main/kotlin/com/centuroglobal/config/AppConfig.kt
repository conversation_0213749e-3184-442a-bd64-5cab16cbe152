package com.centuroglobal.config

import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.web.client.RestTemplate
import java.util.*

private val log = KotlinLogging.logger {}

@Configuration
class AppConfig(
    @Value("\${app.aws.secret-key}")
    private val awsSecret: String?,
    @Value("\${app.aws.access-key}")
    private val awsAccessKey: String?
) {

    init {
        // force to UTC timezone....
        log.info("Setting system timezone to UTC...")
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
        log.info("Current system timezone is now ${TimeZone.getDefault().id}")
        log.info("System's java.io.tmpdir is at ${System.getProperty("java.io.tmpdir")}")

        // inject the AWS credentials into java properties if available
        if (!awsSecret.isNullOrBlank() && !awsAccessKey.isNullOrBlank()) {
            System.setProperty("aws.accessKeyId", awsAccessKey)
            System.setProperty("aws.secretAccessKey", awsSecret)
        }
    }

    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return BCryptPasswordEncoder()
    }

    @Bean
    fun restTemplate(): RestTemplate {
        return RestTemplate()
    }
}