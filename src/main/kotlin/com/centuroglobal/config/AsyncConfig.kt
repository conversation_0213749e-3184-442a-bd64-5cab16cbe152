package com.centuroglobal.config

import com.elementalconcept.common.asyncexecutor.AsyncExecutorConfig
import com.elementalconcept.common.asyncexecutor.AsyncExecutorMetrics
import mu.KotlinLogging
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler
import org.springframework.beans.factory.annotation.Value
import org.springframework.beans.factory.config.MethodInvokingFactoryBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.security.core.context.SecurityContextHolder
import java.lang.reflect.Method
import java.util.concurrent.Executor

private val log = KotlinLogging.logger {}

@EnableAsync
@Configuration
class AsyncConfig(
    @Value("\${app.executor.default.core-pool-size}")
    private val corePoolSize: Int,
    @Value("\${app.executor.default.core-pool-size}")
    private val maxPoolSize: Int,
    @Value("\${app.executor.default.queue-capacity}")
    private val queueCapacity: Int
) : AsyncConfigurer {

    override fun getAsyncUncaughtExceptionHandler(): AsyncUncaughtExceptionHandler? {
        return CustomAsyncUncaughtExceptionHandler()
    }

    override fun getAsyncExecutor(): Executor {
        return genericExecutor()
    }

    @Bean
    fun genericExecutor(): AsyncExecutorMetrics {
        return AsyncExecutorMetrics(
            AsyncExecutorConfig.builder()
                .poolSize(corePoolSize)
                .maxPoolSize(maxPoolSize)
                .queueCapacity(queueCapacity)
                .mdcPropagationEnabled(true)
                .name("WORKER task")
                .build()
        )
    }

    //Added this bean to access SecurityContext.getContext().authentication for methods with @Sync and CompletedFuture.
    /*@Bean
    fun methodInvokingFactoryBean(): MethodInvokingFactoryBean? {
        val methodInvokingFactoryBean = MethodInvokingFactoryBean()
        methodInvokingFactoryBean.targetClass = SecurityContextHolder::class.java
        methodInvokingFactoryBean.targetMethod = "setStrategyName"
        methodInvokingFactoryBean.setArguments(*arrayOf(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL))
        return methodInvokingFactoryBean
    }*/

}

class CustomAsyncUncaughtExceptionHandler : AsyncUncaughtExceptionHandler {
    override fun handleUncaughtException(ex: Throwable, method: Method, vararg params: Any?) {
        log.error("An exception during async execution in method [${method.name}]", ex)
    }
}