package com.centuroglobal.config

import io.swagger.v3.oas.annotations.enums.SecuritySchemeType
import io.swagger.v3.oas.annotations.security.SecurityScheme
import org.springdoc.core.models.GroupedOpenApi
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


@Configuration
@SecurityScheme(
    name = "jwt",
    type = SecuritySchemeType.HTTP,
    bearerFormat = "JWT",
    scheme = "bearer"
)
class SwaggerConfig {

    @Bean
    fun apiAuth(): GroupedOpenApi? {
        return docket("centuro-global-auth-api-1.0", "Centuro Global Sign-up & Authentication API", "1", "/api/v1/auth/**")
    }

   @Bean
    fun apiAdmin(): GroupedOpenApi? {
        return docket("centuro-global-admin-api-1.0", "Centuro Global Admin API", "1", "/api/v1/admin/**")
    }

    @Bean
    fun apiExpertSearch(): GroupedOpenApi? {
        return docket("centuro-global-expert-search-api-1.0", "Centuro Global Expert Search API", "1", "/api/v1/expert-search/**")
    }

    @Bean
    fun apiCorporate(): GroupedOpenApi? {
        return docket("centuro-global-corporate-api-1.0", "Centuro Global Corporate API", "1", "/api/v1/corporate/**")
    }

    @Bean
    fun apiCostOfLiving(): GroupedOpenApi? {
        return docket("centuro-global-cost-of-living-api-1.0", "Centuro Global Cost Of Living API", "1", "/api/v1/costofliving/**")
    }

    @Bean
    fun apiCountryTax(): GroupedOpenApi? {
        return docket("centuro-global-country-tax-api-1.0", "Centuro Global Country Tax API", "1", "/api/v1/countryTax/**")
    }

    @Bean
    fun apiDashboard(): GroupedOpenApi? {
        return docket("centuro-global-dashboard-api-1.0", "Centuro Global Dashboard API", "1", "/api/v1/dashboard/**")
    }

    @Bean
    fun apiCase(): GroupedOpenApi? {
        return docket("centuro-global-case-api-1.0", "Centuro Global Case API", "1", "/api/v1/case/**")
    }

    @Bean
    fun apiAccount(): GroupedOpenApi? {
        return docket("centuro-global-account-api-1.0", "Centuro Global Account API", "1", "/api/v1/account/**")
    }

    @Bean
    fun apiBand(): GroupedOpenApi? {
        return docket("centuro-global-band-api-1.0", "Centuro Global Bands API", "1", "/api/v1/band/**")
    }

    @Bean
    fun apiTermsAndCondition(): GroupedOpenApi? {
        return docket("centuro-global-terms-and-condition-api-1.0", "Centuro Global Terms And Condition API", "1", "/api/v1/t-and-c/**")
    }

    @Bean
    fun apiExpert(): GroupedOpenApi? {
        return docket("centuro-global-expert-api-1.0", "Centuro Global Expert API", "1", "/api/v1/expert/**")
    }

    @Bean
    fun apiExpertUser(): GroupedOpenApi? {
        return docket("centuro-global-expert-user-api-1.0", "Centuro Global Expert User API", "1", "/api/v1/expert-user/**")
    }

    @Bean
    fun apiPaymentAndSubscription(): GroupedOpenApi? {
        return docket("centuro-global-payment-and-subscription-api-1.0", "Centuro Global Customer Payment & Subscription API", "1", "/api/v1/(payment|subscription)/**")
    }

    @Bean
    fun apiBlueprint(): GroupedOpenApi? {
        return docket("centuro-global-blueprint-api-1.0", "Centuro Global Blueprint API", "1", "/api/v1/blueprint/**")
    }

    @Bean
    fun apiLead(): GroupedOpenApi? {
        return docket("centuro-global-lead-api-1.0", "Centuro Global Lead API", "1", "/api/v1/lead/**")
    }

    @Bean
    fun apiConcierge(): GroupedOpenApi? {
        return docket("centuro-global-concierge-api-1.0", "Centuro Global Concierge API", "1", "/api/v1/concierge/**")
    }

    @Bean
    fun apiFactoryData(): GroupedOpenApi? {
        return docket("centuro-global-factory-data-api-1.0", "Centuro Global Factory Data API", "1", "/api/v1/factory-data/**")
    }

    @Bean
    fun apiReferral(): GroupedOpenApi? {
        return docket("centuro-global-referral-api-1.0", "Centuro Global Referral API", "1", "/api/v1/referral/**")
    }

    @Bean
    fun apiCircle(): GroupedOpenApi? {
        return docket("centuro-global-circle-api-1.0", "Centuro Global Circle API", "1", "/api/v1/circle/**")
    }

    @Bean
    fun apiEvent(): GroupedOpenApi? {
        return docket("centuro-global-event-api-1.0", "Centuro Global Event API", "1", "/api/v1/event/**")
    }

    @Bean
    fun apiImmigration(): GroupedOpenApi? {
        return docket("centuro-global-immigration-api-1.0", "Centuro Global Immigration API", "1", "/api/v1/immigration/**")
    }

    @Bean
    fun apiUserAction(): GroupedOpenApi? {
        return docket("centuro-global-user-action-api-1.0", "Centuro Global User Action API", "1", "/api/v1/userAction/**")
    }

    @Bean
    fun apiUserProfile(): GroupedOpenApi? {
        return docket("centuro-global-user-profile-api-1.0", "Centuro Global User Profile API", "1", "/api/v1/user-profile/**")
    }

    @Bean
    fun apiTravelAssessment(): GroupedOpenApi? {
        return docket("centuro-global-travel-assessment-api-1.0", "Centuro Global Travel Assessment API", "1", "/api/v1/travel-assessment/**")
    }

    @Bean
    fun apiTour(): GroupedOpenApi? {
        return docket("centuro-global-tour-api-1.0", "Centuro Global tour API", "1", "/api/v1/tour/**")
    }

    @Bean
    fun apiBands(): GroupedOpenApi? {
        return docket("centuro-global-bands-api-1.0", "Centuro Global Band API", "1", "/api/v1/band/**")
    }

    @Bean
    fun docRepo(): GroupedOpenApi? {
        return docket("centuro-global-doc-repo-api-1.0", "Centuro Global Document Repository API", "1", "/api/v1/doc_repo/**")
    }

    @Bean
    fun groupChats(): GroupedOpenApi? {
        return docket("centuro-global-group-chat-api-1.0", "Centuro Global Group Chat API", "1", "/api/v1/group-chat/**")
    }

    @Bean
    fun aiChats(): GroupedOpenApi? {
        return docket("centuro-global-ai-chat-api-1.0", "Centuro Global AI Chat API", "1", "/api/v1/ai-chat/**")
    }

    @Bean
    fun queries(): GroupedOpenApi? {
        return docket("centuro-global-queries-api-1.0", "Centuro Global Queries API", "1", "/api/v1/queries/**")
    }

    @Bean
    fun rfp(): GroupedOpenApi? {
        return docket("centuro-global-rfp-api-1.0", "Centuro Global Rfp API", "1", "/api/v1/rfp/**")
    }

    @Bean
    fun uploads(): GroupedOpenApi? {
        return docket("centuro-global-upload-api-1.0", "Centuro Global Upload API", "1", "/api/v1/uploads/**")
    }

    @Bean
    fun partners(): GroupedOpenApi? {
        return docket("centuro-global-partners-api-1.0", "Centuro Global Partners API", "1", "/api/v1/partner/**")
    }

    @Bean
    fun switchRole(): GroupedOpenApi? {
        return docket("centuro-global-token-api-1.0", "Centuro Global Token API", "1", "/api/v1/switch-role/**")
    }

    @Bean
    fun playbook(): GroupedOpenApi? {
        return docket("centuro-global-playbook-api-1.0", "Centuro Global Playbook API", "1", "/api/v1/playbook/**")
    }

    @Bean
    fun tasks(): GroupedOpenApi? {
        return docket("centuro-global-task-api-1.0", "Centuro Global Task API", "1", "/api/v1/task/**")
    }

    @Bean
    fun travelHistory(): GroupedOpenApi? {
        return docket("centuro-global-travel-history-api-1.0", "Centuro Global Travel history API", "1", "/api/v1/travel/**")
    }

    @Bean
    fun usageLogs(): GroupedOpenApi? {
        return docket("centuro-global-usage-logs-api-1.0", "Centuro Global Usage Logs API", "1", "/api/v1/usage/**")
    }

    @Bean
    fun caseForms(): GroupedOpenApi? {
        return docket("centuro-global-case-forms-api-1.0", "Centuro Global Case Forms API", "1", "/api/v1/case-form/**")
    }

    @Bean
    fun aiReporter(): GroupedOpenApi? {
        return docket("centuro-global-ai-reporter-api-1.0", "Centuro Global AI Reporter API", "1", "/api/v1/reporter/**")
    }

    /*private fun docket(groupName: String, apiName: String, ver: String, url: String): Docket? {
        return Docket(DocumentationType.SWAGGER_2)
            .groupName(groupName)
            .apiInfo(apiInfo(apiName, ver))
            .select()
            .apis(RequestHandlerSelectors.any())
            .paths(PathSelectors.regex(url)) // and by path
            .build()
            .useDefaultResponseMessages(false)
            .genericModelSubstitutes(ResponseEntity::class.java, DeferredResult::class.java)
            .securitySchemes(listOf(apiKey()))
    }*/

    private fun docket(groupName: String, apiName: String, ver: String, url: String): GroupedOpenApi? {
        return GroupedOpenApi.builder()
            .group(groupName)
            .pathsToMatch(url)
            .addOpenApiCustomizer { it.info.title(apiName).version(ver)}
            .build()
    }

    /*private fun apiInfo(apiName: String, ver: String): ApiInfo? {
        return ApiInfo(
            apiName,
            "", ver, "",
            Contact("", "", ""),
            "", "", emptyList()
        )
    }*/

    /*private fun apiKey(): ApiKey? {
        return ApiKey("jwt", "Authorization", "header")
    }*/
}