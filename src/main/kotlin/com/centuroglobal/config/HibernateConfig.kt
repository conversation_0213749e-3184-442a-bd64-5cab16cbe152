package com.centuroglobal.config
/*
//import io.hypersistence.utils.hibernate.type.basic.PostgreSQLEnumType
import org.hibernate.boot.model.TypeContributions
import org.hibernate.boot.model.TypeContributor
import org.hibernate.engine.spi.SharedSessionContractImplementor
import org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl
import org.hibernate.jpa.boot.spi.TypeContributorList
import org.hibernate.service.ServiceRegistry
import org.hibernate.type.BasicTypeRegistry
import org.hibernate.type.EnumType
import org.hibernate.type.descriptor.java.EnumJavaType
import org.hibernate.type.descriptor.jdbc.ObjectJdbcType
import org.hibernate.type.spi.TypeConfiguration
import org.hibernate.usertype.DynamicParameterizedType
import org.hibernate.usertype.UserType
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider
import org.springframework.context.annotation.Configuration
import org.springframework.core.type.filter.RegexPatternTypeFilter
import java.io.Serializable
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.Types
import java.util.*
import java.util.regex.Pattern


@Configuration
class HibernateConfig {

    // Enums in @Query fails with NullPointerException in Hibernate 6. This bean fixes it.
   @Bean
    fun customHibernateTypeRegistrar(): HibernatePropertiesCustomizer? {
        return HibernatePropertiesCustomizer { props: MutableMap<String?, Any?> ->
            props[EntityManagerFactoryBuilderImpl.TYPE_CONTRIBUTORS] = TypeContributorList {
                listOf(TypeContributor { typeContributions: TypeContributions, _: ServiceRegistry? ->
                   val basicTypeRegistry =
                        typeContributions.typeConfiguration.basicTypeRegistry

                    val provider = ClassPathScanningCandidateComponentProvider(false)
                    provider.addIncludeFilter(RegexPatternTypeFilter(Pattern.compile(".*")))

                    val classes = provider.findCandidateComponents("com.centuroglobal.shared.data.enums")

                    for (bean in classes) {
                        val clazz = Class.forName(bean.beanClassName)
                        try{
                            Class.forName(clazz.canonicalName)
                        }catch (ex: ClassNotFoundException){
                            //ignore
                            continue
                        }
                        registerType(basicTypeRegistry, clazz.canonicalName)
                    }
                })
            }
        }
    }

    private fun registerType(basicTypeRegistry: BasicTypeRegistry, enumClassName: String?) {
        val type = MySQLEnumType<EnumType>()
        val props = Properties()
        props.setProperty(DynamicParameterizedType.RETURNED_CLASS, enumClassName)

       basicTypeRegistry.register(type, enumClassName)
    }

}



class MySQLEnumType : EnumType<Enum()>() {

}*/

