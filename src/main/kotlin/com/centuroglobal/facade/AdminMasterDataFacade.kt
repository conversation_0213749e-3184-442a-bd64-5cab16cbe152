package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.ExtentionType
import com.centuroglobal.shared.data.enums.stripe.TemplateType
import com.centuroglobal.service.AdminMasterDataService
import mu.KotlinLogging
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException


private val log = KotlinLogging.logger {}

@Component
class AdminMasterDataFacade(
    private val adminMasterDataService: AdminMasterDataService
) {

    fun uploadTemplate(templateType: String, uploadedExcel: MultipartFile, userId: Long, displayName: String): HttpStatus {
        val file = File.createTempFile(templateType, ExtentionType.XLSX.key)
        log.info("Upload process started for template $templateType")
        FileOutputStream(file).use { fileOuputStream -> fileOuputStream.write(uploadedExcel.bytes) }
        try {
            FileInputStream(file).use { excelFile ->
                try {
                    XSSFWorkbook(excelFile).use { workbook ->
                        val sheet = workbook.getSheetAt(0)
                        when (templateType) {
                            TemplateType.CORPORATE_TAX.name -> adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, uploadedExcel)
                            TemplateType.EMPLOYEE_TAX.name -> adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, uploadedExcel)
                            TemplateType.EMPLOYER_TAX.name -> adminMasterDataService.setTaxData(sheet, templateType, userId, displayName, uploadedExcel)
                            TemplateType.GLOBAL_RANKING.name -> adminMasterDataService.setGlobalRankingData(sheet, templateType, userId, displayName, uploadedExcel)
                            TemplateType.COUNTRY_HIGHLIGHTS.name -> adminMasterDataService.setCountryHighlightsData(sheet, templateType, userId, displayName, uploadedExcel)
                            TemplateType.IMMIGRATION_REQUIREMENT.name -> adminMasterDataService.setImmigrationRequirement(workbook, templateType, userId, displayName, uploadedExcel)
                            TemplateType.ENTITY_TYPE.name -> adminMasterDataService.setEntityType(workbook, templateType, userId, displayName, uploadedExcel)
                            TemplateType.CASE_STATUS_MILESTONE.name -> adminMasterDataService.setMilestoneStatusData(workbook, templateType, userId, displayName, uploadedExcel)
                            TemplateType.SUBSCRIPTION_PLAN.name -> adminMasterDataService.setSubscriptionData(workbook, userId, displayName)
                        }
                    }
                } catch (e: IOException) {
                    return HttpStatus.INTERNAL_SERVER_ERROR
                }
                file.delete()
                if (templateType == TemplateType.IMMIGRATION_REQUIREMENT.name) return HttpStatus.ACCEPTED
                return HttpStatus.OK
            }
        } catch (exp: Exception) {
            return HttpStatus.INTERNAL_SERVER_ERROR
        }
    }
}