package com.centuroglobal.facade

import com.centuroglobal.data.payload.auth.ChangePasswordRequest
import com.centuroglobal.data.payload.auth.RecoverPasswordRequest
import com.centuroglobal.data.payload.auth.ResetPasswordRequest
import com.centuroglobal.service.PasswordService
import com.centuroglobal.shared.data.pojo.TokenResult
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class PasswordFacade(private val passwordService: PasswordService) {

    @Async
    @Throws(InterruptedException::class)
    fun changePassword(userId: Long, request: ChangePasswordRequest): CompletableFuture<String> {
        return CompletableFuture.completedFuture(passwordService.changePassword(userId, request))
    }

    @Async
    @Throws(InterruptedException::class)
    fun recoverPassword(request: RecoverPasswordRequest): CompletableFuture<String> {
        return CompletableFuture.completedFuture(passwordService.recoverPassword(request))
    }

    @Async
    @Throws(InterruptedException::class)
    fun resetPassword(request: ResetPasswordRequest): CompletableFuture<TokenResult> {
        return CompletableFuture.completedFuture(passwordService.resetPassword(request))
    }

    @Async
    @Throws(InterruptedException::class)
    fun loginAccountUuidApproval(uuid: String): CompletableFuture<Boolean> {
        return CompletableFuture.completedFuture(passwordService.loginAccountUuidApproval(uuid))
    }
}