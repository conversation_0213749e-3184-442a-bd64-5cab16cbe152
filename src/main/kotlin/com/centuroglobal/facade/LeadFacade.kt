package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.data.payload.lead.CreateLeadRequest
import com.centuroglobal.data.payload.lead.LeadResponseRequest
import com.centuroglobal.data.payload.lead.UpdateLeadRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.LeadService
import org.springframework.data.domain.PageRequest
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class LeadFacade(
    private val leadService: LeadService
) {

    @Async
    @Throws(InterruptedException::class)
    fun create(request: CreateLeadRequest, user: AuthenticatedUser): CompletableFuture<LeadDetail> {
        return CompletableFuture.completedFuture(leadService.create(request, user))
    }

    @Async
    @Throws(InterruptedException::class)
    fun update(leadId: String, request: UpdateLeadRequest, userId: Long): CompletableFuture<LeadDetail> {
        return CompletableFuture.completedFuture(leadService.update(leadId, request, userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun search(
        leadType: String,
        leadTypeId: Long,
        leadSearchFilter: LeadSearchFilter,
        pageRequest: PageRequest
    ): CompletableFuture<PagedResult<LeadSummary>> {
        return CompletableFuture.completedFuture(
            leadService.search(
                leadType,
                leadTypeId,
                leadSearchFilter,
                pageRequest
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun searchLeadsForExpert(
        leadSearchFilter: LeadSearchFilter,
        expertUserId: Long,
        pageRequest: PageRequest
    ): CompletableFuture<PagedResult<ExpertLeadSummary>> {
        return CompletableFuture.completedFuture(
            leadService.searchLeadsForExpert(
                leadSearchFilter,
                expertUserId,
                pageRequest
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieve(leadId: String, userId: Long): CompletableFuture<LeadDetail> {
        return CompletableFuture.completedFuture(leadService.retrieve(leadId, userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveLeadForExpert(expertId: Long, leadId: String): CompletableFuture<LeadDetail> {
        return CompletableFuture.completedFuture(leadService.retrieveLeadDetailForExpert(expertId, leadId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun delete(leadId: String, userId: Long): CompletableFuture<String> {
        return CompletableFuture.completedFuture(leadService.delete(leadId, userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun respond(userId: Long, leadId: String, responseRequest: LeadResponseRequest): CompletableFuture<String> {
        return CompletableFuture.completedFuture(leadService.respond(leadId, userId, responseRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun updateResponse(userId: Long, leadId: String, responseRequest: LeadResponseRequest): CompletableFuture<String> {
        return CompletableFuture.completedFuture(leadService.updateResponse(leadId, userId, responseRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun searchLeadsForAdmin(
        leadSearchFilter: LeadSearchFilter,
        pageRequest: PageRequest
    ): CompletableFuture<PagedResult<AdminLeadSummary>> {
        return CompletableFuture.completedFuture(leadService.searchLeadsForAdmin(leadSearchFilter, pageRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun recent(): CompletableFuture<List<RecentLeadSummary>> {
        return CompletableFuture.completedFuture(leadService.recent())
    }

    @Async
    @Throws(InterruptedException::class)
    fun saveMessageTrail(
        userId: Long,
        leadId: String,
        respondedBy: LeadType,
        leadMessageTrailRequest: LeadMessageTrailRequest
    ): CompletableFuture<List<List<LeadResponseTrail>>> {
        return CompletableFuture.completedFuture(leadService.saveMessageTrail(userId, leadId, respondedBy, leadMessageTrailRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun getLeadResponseTrail(
        leadId: String,
        expertId: Long,
        userId: Long,
        lastMessageDateTime: Long?,
        seenBy: LeadType
    ): CompletableFuture<List<List<LeadResponseTrail>>> {
        return CompletableFuture.completedFuture(leadService.getLeadResponseTrail(leadId, expertId, userId,lastMessageDateTime,seenBy))
    }
}
