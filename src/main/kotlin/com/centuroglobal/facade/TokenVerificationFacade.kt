package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.VerifiedUser
import com.centuroglobal.service.TokenVerificationService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class TokenVerificationFacade(private val tokenVerificationService: TokenVerificationService) {

    @Async
    @Throws(InterruptedException::class)
    fun validateCode(code: String?): CompletableFuture<VerifiedUser> {
        return CompletableFuture.completedFuture(tokenVerificationService.validateCode(code))
    }
}