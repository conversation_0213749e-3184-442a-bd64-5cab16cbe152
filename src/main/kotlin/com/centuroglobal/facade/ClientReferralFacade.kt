package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.referral.ClientReferralCreateRequest
import com.centuroglobal.shared.data.pojo.referral.ClientReferralDetails
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSearchFilter
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSummary
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.ClientReferralService
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class ClientReferralFacade(
    private val clientReferralService: ClientReferralService
) {
    @Async
    @Throws(InterruptedException::class)
    fun create(request: ClientReferralCreateRequest, user: AuthenticatedUser): CompletableFuture<ClientReferralDetails>{
        return CompletableFuture.completedFuture(clientReferralService.createReferral(request,user))
    }

    @Async
    @Throws(InterruptedException::class)
    fun update(clientReferralId: Long, request: ClientReferralCreateRequest, user: AuthenticatedUser): CompletableFuture<ClientReferralDetails>{
        return CompletableFuture.completedFuture(clientReferralService.updateReferral(clientReferralId,request,user))
    }

    @Async
    @Throws(InterruptedException::class)
    fun get(clientReferralId: Long, user: AuthenticatedUser): CompletableFuture<ClientReferralDetails> {
        return CompletableFuture.completedFuture(clientReferralService.getReferralDetails(clientReferralId, user, true))
    }

    @Async
    @Throws(InterruptedException::class)
    fun searchExpert(filter: ClientReferralSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<PagedResult<ClientReferralDetails>> {
        return CompletableFuture.completedFuture(clientReferralService.listingForExpert(filter,pageRequest,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun delete(
        clientReferralId: Long, userId: Long
    ): CompletableFuture<String> {
        return CompletableFuture.completedFuture(clientReferralService.delete(clientReferralId, userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun search(
        filter: ClientReferralSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<ClientReferralSummary>> {
        return CompletableFuture.completedFuture(clientReferralService.search(filter, pageRequest, authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun clientReferralExpertSearch(
        clientReferralId: Long,
        filter: ExpertSearchFilter,
        pageRequest: Pageable,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<ExpertProfileSummary>> {
        return CompletableFuture.completedFuture(clientReferralService.referralMemberSearch(clientReferralId,filter,pageRequest,authenticatedUser))
    }
}