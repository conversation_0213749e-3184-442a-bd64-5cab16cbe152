package com.centuroglobal.facade

import com.centuroglobal.shared.data.payload.account.signup.OnBoardingProceedRequest
import com.centuroglobal.shared.data.payload.account.signup.OnBoardingRequest
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.OnBoardingService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class OnBoardingFacade(private val onBoardingService: OnBoardingService) {

    @Async
    @Throws(InterruptedException::class)
    fun onBoarding(onBoardingRequest: OnBoardingRequest): CompletableFuture<TokenResult> {
        return CompletableFuture.completedFuture(onBoardingService.onBoarding(onBoardingRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun onBoardingProceed(
        onBoardingProceedRequest: OnBoardingProceedRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<TokenResult> {
        onBoardingService.onBoardingProceed(onBoardingProceedRequest, authenticatedUser)
        return CompletableFuture.completedFuture(null)
    }
}