package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.CircleMemberAction
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.CircleListing
import com.centuroglobal.shared.data.pojo.circle.CircleMessageTrailRequest
import com.centuroglobal.shared.data.pojo.circle.CircleResponseTrail
import com.centuroglobal.shared.data.pojo.circle.CircleSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.CircleService
import org.springframework.data.domain.PageRequest
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class ExpertCircleFacade(
    private val circleService: CircleService
) {

    @Async
    @Throws(InterruptedException::class)
    fun circleDetails(circleId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<Circle> {
        return CompletableFuture.completedFuture(circleService.circleDetails(circleId, authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleMemberAction(circleId: Long, circleMemberAction: CircleMemberAction, authenticatedUser: AuthenticatedUser): CompletableFuture<Any> {
        return CompletableFuture.completedFuture(circleService.circleMemberAction(circleId,circleMemberAction,authenticatedUser.userId,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun getMessageTrail(circleId: Long, lastMessageDateTime: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<List<List<CircleResponseTrail>>> {
        return CompletableFuture.completedFuture(circleService.getCircleResponseTrail(circleId,authenticatedUser.userId,lastMessageDateTime,
            UserType.valueOf(authenticatedUser.userType)))
    }

    @Async
    @Throws(InterruptedException::class)
    fun saveMessageTrail(
        circleId: Long,
        circleMessageTrailRequest: CircleMessageTrailRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<List<List<CircleResponseTrail>>> {
        return CompletableFuture.completedFuture(
            circleService.saveMessageTrail(authenticatedUser.userId,circleId,circleMessageTrailRequest,authenticatedUser)
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun listCircle(build: CircleSearchFilter, of: PageRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<CircleListing> {
        return CompletableFuture.completedFuture(
            circleService.listCircleExpert(authenticatedUser, build, of)
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleMemberSearch(
        circleId: Long,
        build: ExpertSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<ExpertProfileSummary>> {
        return CompletableFuture.completedFuture(
            circleService.circleMemberSearch(circleId, build, pageRequest, authenticatedUser)
        )
    }

/*fun requestToJoin(circleId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<Any> {
    return CompletableFuture.completedFuture(
        circleService.requestToJoin(circleId,authenticatedUser)
    )
}*/

}