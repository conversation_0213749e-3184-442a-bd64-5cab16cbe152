package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.Expertise
import com.centuroglobal.service.ExpertiseService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class ExpertiseFacade(private val expertiseService: ExpertiseService) {

    @Async
    @Throws(InterruptedException::class)
    fun listExpertise(prefix: String?, groupBy: Boolean): CompletableFuture<List<Expertise>> {
        return if (prefix.isNullOrBlank() && groupBy) {
            CompletableFuture.completedFuture(expertiseService.listExpertise())
        } else {
            CompletableFuture.completedFuture(expertiseService.searchByGroupNameOrNamePrefix(prefix))
        }
    }
}