package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.pojo.Blueprint
import com.centuroglobal.shared.data.pojo.CountrySummary
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintStep
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.service.BlueprintPdfService
import com.centuroglobal.service.BlueprintService
import com.centuroglobal.shared.util.DownloadWrapper
import com.centuroglobal.shared.util.MEDIA_TYPE_PDF
import org.springframework.http.ResponseEntity
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.OutputStream
import java.util.concurrent.CompletableFuture

@Component
class BlueprintFacade(
    private val blueprintService: BlueprintService,
    private val blueprintPdfService: BlueprintPdfService
) {

    @Async
    @Throws(InterruptedException::class)
    fun listActive(): CompletableFuture<List<CountrySummary>> {
        return CompletableFuture.completedFuture(blueprintService.listActive())
    }

    //@Async
    @Throws(InterruptedException::class)
    fun retrieveBluePrint(countryCode: String, sessionId: String?): CompletableFuture<Blueprint> {
        return CompletableFuture.completedFuture(blueprintService.retrieveBluePrint(countryCode,false, sessionId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveStep(countryCode: String, stepName: StepName): CompletableFuture<BlueprintStep> {
        return CompletableFuture.completedFuture(blueprintService.retrieveStep(countryCode, stepName))
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveTeaser(countryCode: String): CompletableFuture<Blueprint> {
        return CompletableFuture.completedFuture(blueprintService.retrieveTeaser(countryCode))
    }

    fun downloadPdf(countryCode: String, step: StepName): ResponseEntity<StreamingResponseBody> {
        return try {
            blueprintPdfService.validateBlueprintPdf(countryCode, step)
            DownloadWrapper.downloadFile("blueprint_${countryCode}_${step}.pdf", MEDIA_TYPE_PDF) { out: OutputStream ->
                blueprintPdfService.downloadBlueprint(countryCode, step, out)
            }
        } catch (ex: ApplicationException) {
            val streamError = StreamingResponseBody { out ->
                out.write(ex.error.errorMessage.toByteArray(Charsets.UTF_8))
                out.flush()
                out.close()
            }
            ResponseEntity(streamError, ex.error.httpStatus!!)
        }
    }
}
