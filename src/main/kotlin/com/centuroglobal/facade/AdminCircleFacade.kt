package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.CircleBannerUploadResponse
import com.centuroglobal.shared.data.pojo.circle.CircleListing
import com.centuroglobal.shared.data.pojo.circle.CircleSearchFilter
import com.centuroglobal.shared.data.pojo.circle.CreateUpdateCircleRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.CircleService
import org.springframework.data.domain.PageRequest
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import java.util.concurrent.CompletableFuture

@Component
class AdminCircleFacade(
    private val circleService: CircleService
) {
    @Async
    @Throws(InterruptedException::class)
    fun createCircle(createUpdateCircleRequest: CreateUpdateCircleRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<Circle> {
        return CompletableFuture.completedFuture(circleService.createCircle(createUpdateCircleRequest,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun updateCircle(circleId :Long, createUpdateCircleRequest: CreateUpdateCircleRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<Circle> {
        return CompletableFuture.completedFuture(circleService.updateCircle(circleId,createUpdateCircleRequest,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun deleteCircle(circleId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<Any> {
        return CompletableFuture.completedFuture(circleService.deleteCircle(circleId, authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleStatusUpdate(circleId: Long, circleStatus: CircleStatus, authenticatedUser: AuthenticatedUser) : CompletableFuture<Any>{
        return CompletableFuture.completedFuture(circleService.circleStatusUpdate(circleId,circleStatus,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun approveMember(approve: Boolean, circleId: Long, memberId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<Any>{
        return CompletableFuture.completedFuture(circleService.approveMember(approve,circleId,memberId,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun listCircle(build: CircleSearchFilter, of: PageRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<CircleListing> {
        return CompletableFuture.completedFuture(circleService.listCircle(build,of,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleMemberSearch(
        circleId: Long,
        build: ExpertSearchFilter,
        of: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<ExpertProfileSummary>> {
        return CompletableFuture.completedFuture(circleService.circleMemberSearch(circleId,build,of,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun uploadCoverPicture(circleId: Long, photo: MultipartFile, user: AuthenticatedUser): CompletableFuture<CircleBannerUploadResponse> {
        return CompletableFuture.completedFuture(circleService.uploadCoverPicture(circleId,user.userId,photo))
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleRequestSearch(circleId: Long, build: ExpertSearchFilter, of: PageRequest): CompletableFuture<PagedResult<ExpertProfileSummary>> {
        return CompletableFuture.completedFuture(circleService.circleRequestSearch(circleId,build,of))
    }

    @Async
    @Throws(InterruptedException::class)
    fun circleDetails(circleId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<Circle> {
        return CompletableFuture.completedFuture(circleService.circleDetails(circleId, authenticatedUser))
    }
}