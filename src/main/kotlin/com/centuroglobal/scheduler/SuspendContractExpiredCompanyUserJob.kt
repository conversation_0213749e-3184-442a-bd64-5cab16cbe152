package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.ExpertCompanyClientService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class SuspendContractExpiredCompanyUserJob(private val expertCompanyClientService: ExpertCompanyClientService) {

    @Scheduled(cron = "0 0 2 * * *") // every day 2am
    fun cleanupToken() {
        log.info("******  Contract Expired company users cleanup Job [STARTED] *********")
        expertCompanyClientService.suspendExpiredContractCompanyUsers()
        log.info("******  Contract Expired company users cleanup Job [ENDED] *********")
    }
}