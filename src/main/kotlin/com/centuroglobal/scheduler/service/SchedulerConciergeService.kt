package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.entity.ConciergeNotificationEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ConciergeNotificationState
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.email.ConciergeContext
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.repository.ConciergeNotificationRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private const val CONCIERGE_EMAIL_TEMPLATE_NAME = "email/concierge"

private const val CONCIERGE_EMAIL_SUBJECT = "New concierge request"

@Service
class SchedulerConciergeService(
    @Value("\${app.concierge-notification.cleanup-after-sent-in-days}")
    private val cleanupAfterSentInDays: Int,
    @Value("\${app.concierge-notification.max-email-per-cron}")
    private val maxEmailPerCron: Int,
    @Value("\${app.concierge-notification.contact-address}")
    private val cgEmail: String,
    private val conciergeNotificationRepository: ConciergeNotificationRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val expertRepository: ExpertUserRepository,
    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service
) {


    @Transactional
    fun cleanUp() {
        // compute the cutoff date
        val cutoffDate = LocalDateTime.now().minusDays(cleanupAfterSentInDays.toLong()).truncatedTo(ChronoUnit.DAYS)
        conciergeNotificationRepository.deleteAllByStateInAndLastUpdatedDateBefore(
            listOf(ConciergeNotificationState.EMAIL_SENT, ConciergeNotificationState.INVALID), cutoffDate
        )
    }

    @Transactional
    fun sendNotifications() {
        val conciergeNotifications = conciergeNotificationRepository.findAllByState(
            ConciergeNotificationState.CREATED,
            PageRequest.of(0, maxEmailPerCron, Sort.by(Sort.Direction.ASC, "lastUpdatedDate"))
        ).toList()
        val loginAccountMap = loginAccountRepository.findAllByIdInAndStatus(
            conciergeNotifications.map { it.userId },
            AccountStatus.ACTIVE
        ).map { it.id to it }.toMap()

        if (conciergeNotifications.size > 0) {
            val updateList: MutableList<ConciergeNotificationEntity> = mutableListOf()

            conciergeNotifications.forEach {
                if (loginAccountMap[it.userId] != null) {
                    val company = when (loginAccountMap.getValue(it.userId).getUserType()) {
                        UserType.CORPORATE -> corporateUserRepository.findByIdOrNull(it.userId)!!.corporate.name
                        UserType.EXPERT -> expertRepository.findByIdOrNull(it.userId)!!.companyProfile?.name
                        else -> null
                    }

                    val result = sendEmail(cgEmail, loginAccountMap.getValue(it.userId), company, it.requestBody)
                    if (result) {
                        it.state = ConciergeNotificationState.EMAIL_SENT
                        updateList.add(it)
                    }
                } else {
                    it.state = ConciergeNotificationState.INVALID
                    updateList.add(it)
                }
            }

            if (updateList.isNotEmpty()) {
                conciergeNotificationRepository.saveAll(updateList)
            }
        }
    }

    @Transactional
    fun sendEmail(to: String, loginAccount: LoginAccountEntity, company: String?, query: String): Boolean {
        return mailSendingService.sendEmail(
            MailTemplate(
                templateName = CONCIERGE_EMAIL_TEMPLATE_NAME,
                subject = CONCIERGE_EMAIL_SUBJECT,
                recipient = to,
                ccRecipients = listOf(loginAccount.email),
                context = ConciergeContext.ModelMapper.toContext(
                    ConciergeContext(
                        loginAccount.firstName, loginAccount.lastName, company, loginAccount.email, query,
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                    )
                )
            )
        )
    }
}

