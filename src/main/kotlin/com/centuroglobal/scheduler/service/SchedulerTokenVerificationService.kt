package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.email.*
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.ValidationTokenRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.PartnerEmailUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

private const val CORPORATE_SIGN_UP_EMAIL_TEMPLATE_NAME = "email/corporate-sign-up-verification"
private const val BACKOFFICE_USER_CONFIRMATION_EMAIL_TEMPLATE_NAME = "email/backoffice-user-confirmation"
private const val EXPERT_INVITATION_EMAIL_TEMPLATE_NAME = "email/expert-invitation"
private const val PARTNER_SIGN_UP_EMAIL_TEMPLATE_NAME = "email/partner-sign-up-verification"
private const val PARTNER_CORPORATE_INVITATION_EMAIL_TEMPLATE_NAME = "email/partner_invite_corporate_verification"
private const val PARTNER_EXPERT_INVITATION_EMAIL_TEMPLATE_NAME = "email/partner_invite_expert_verification"
private const val RECOVER_PASSWORD_EMAIL_TEMPLATE_NAME = "email/recover-password"

private const val CORPORATE_SIGN_UP_EMAIL_SUBJECT = "Verification of Email Required"
private const val BACKOFFICE_USER_CONFIRMATION_EMAIL_SUBJECT = "Verification of Email Required"
private const val EXPERT_INVITATION_EMAIL_SUBJECT = "Invitation to Join"
private const val PARTNER_SIGN_UP_EMAIL_SUBJECT = "Verification of Email Required"
private const val PARTNER_CORPORATE_INVITATION_EMAIL_SUBJECT = "Invitation to Join"
private const val PARTNER_EXPERT_INVITATION_EMAIL_SUBJECT = "Invitation to Join"
private const val RECOVER_PASSWORD_EMAIL_SUBJECT = "Reset your Password"

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private val log = KotlinLogging.logger {}
private val signUpTokenUrl: (webUrl: String, code: String) -> String =
    { webUrl, code -> "${webUrl}/auth/email-confirmation?code=${code}" }
private val recoverPasswordTokenUrl: (webUrl: String, code: String) -> String =
    { webUrl, code -> "${webUrl}/auth/recover-password?code=${code}" }

@Service
class SchedulerTokenVerificationService(
    @Value("\${app.web-url}")
    private val webUrl: String,

    @Value("\${app.verification.cleanup-after-expiry-in-days}")
    private val cleanupAfterExpiryInDays: Int,
    private val awsS3Service: AwsS3Service,
    private val mailSendingService: MailSendingService,
    private val validationTokenRepository: ValidationTokenRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val partnerEmailUtil: PartnerEmailUtil

) {
    private val tokenExpiryInSeconds: Int = 6 * 60 * 60

    @Transactional
    fun cleanUp() {
        // compute the cutoff date
        val cutoffDate = LocalDateTime.now().minusDays(cleanupAfterExpiryInDays.toLong()).truncatedTo(ChronoUnit.DAYS)
        validationTokenRepository.deleteAllByExpiryDateBefore(cutoffDate)
    }

    @Transactional
    fun sendVerificationEmails() {
        val page = validationTokenRepository.findAllByStateAndTypeIn(
            ValidationState.CREATED, ValidationType.values().toList(),
            PageRequest.of(0, 20, Sort.by(Sort.Direction.ASC, "lastUpdatedDate")) // send 20 at a time
        )

        if (page.size > 0) {
            val updateList: MutableList<ValidationTokenEntity> = mutableListOf()
            val tokenList = page.toList()
            val mapOfLoginAccount = loginAccountRepository.findAllByIdIn(tokenList.map { it.userId })
                .map { it.id to it }.toMap()
            tokenList.forEach {
                val result = sendVerificationEmail(it, mapOfLoginAccount[it.userId], it.type)
                if (result) {
                    it.state = ValidationState.EMAIL_SENT
                    updateList.add(it)
                }
            }
            if (updateList.isNotEmpty()) {
                validationTokenRepository.saveAll(updateList)
            }
        }
    }
    private fun sendVerificationEmail(
        entity: ValidationTokenEntity,
        loginAccount: LoginAccountEntity?,
        type: ValidationType
    ): Boolean {
        val ctx = getContext(type, loginAccount, entity) ?: return false

        val template: String
        val subject: String

        when (type) {
            ValidationType.RECOVER_PASSWORD -> {
                template = RECOVER_PASSWORD_EMAIL_TEMPLATE_NAME
                subject = RECOVER_PASSWORD_EMAIL_SUBJECT
            }
            ValidationType.CORPORATE_SIGNUP -> {
                template = CORPORATE_SIGN_UP_EMAIL_TEMPLATE_NAME
                subject = CORPORATE_SIGN_UP_EMAIL_SUBJECT
                ctx.setVariable("BG_COLOR", "#ca6b86")
            }
            ValidationType.INVITE_EXPERT -> {
                template = EXPERT_INVITATION_EMAIL_TEMPLATE_NAME
                subject = EXPERT_INVITATION_EMAIL_SUBJECT
            }
            ValidationType.INVITE_BACKOFFICE -> {
                template = BACKOFFICE_USER_CONFIRMATION_EMAIL_TEMPLATE_NAME
                subject = BACKOFFICE_USER_CONFIRMATION_EMAIL_SUBJECT
            }
            ValidationType.PARTNER_SIGNUP -> {
                template = PARTNER_SIGN_UP_EMAIL_TEMPLATE_NAME
                subject = PARTNER_SIGN_UP_EMAIL_SUBJECT
            }
            ValidationType.INVITE_PARTNER_CORPORATE -> {
                template = PARTNER_CORPORATE_INVITATION_EMAIL_TEMPLATE_NAME
                subject = PARTNER_CORPORATE_INVITATION_EMAIL_SUBJECT
            }
            ValidationType.INVITE_PARTNER_EXPERT -> {
                template = PARTNER_EXPERT_INVITATION_EMAIL_TEMPLATE_NAME
                subject = PARTNER_EXPERT_INVITATION_EMAIL_SUBJECT
            }
        }

        loginAccount?.let { partnerEmailUtil.updateContext(ctx, it) }

        return mailSendingService.sendEmail(
            MailTemplate(
                templateName = template,
                subject = subject,
                recipient = loginAccount?.email!!, context = ctx
            )
        )
    }
    private fun getContext(
        type: ValidationType,
        loginAccount: LoginAccountEntity?,
        entity: ValidationTokenEntity
    ): Context? {
        if (loginAccount == null) {
            log.warn("UserId = ${entity.userId} not found!!! Sign up verification / Expert invitation email will not be sent....")
            return null
        }

        return when (type) {
            ValidationType.RECOVER_PASSWORD -> {
                RecoverPasswordContext.ModelMapper.toContext(
                    RecoverPasswordContext(
                        loginAccount.firstName,
                        recoverPasswordTokenUrl(webUrl, entity.code),
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                    )
                )
            }
            ValidationType.CORPORATE_SIGNUP -> {
                if (loginAccount is CorporateUserEntity) {
                    CorporateUserContext.ModelMapper.toContext(
                        CorporateUserContext(
                            loginAccount.firstName,
                            loginAccount.corporate.name,
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                        )
                    )
                } else {
                    log.warn("Entity is not of userType CorporateUser!! Sign up verification email will not be sent....")
                    null
                }
            }
            ValidationType.INVITE_EXPERT -> {
                if (loginAccount is ExpertUserEntity) {
                    ExpertInviteContext.ModelMapper.toContext(
                        ExpertInviteContext(
                            loginAccount.displayName.ifBlank {
                                "Sir/Madam"
                            },
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                        )
                    )
                } else {
                    log.warn("Entity is not of userType ExpertUser!! Expert invitation email will not be sent....")
                    null
                }
            }
            ValidationType.PARTNER_SIGNUP -> {
                if (AdminAccessUtil.isPartner(loginAccount)) {
                    PartnerInviteContext.ModelMapper.toContext(
                        PartnerInviteContext(
                            loginAccount.firstName,
                            "${loginAccount.firstName} ${loginAccount.lastName}",
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                            loginAccount.partner?.name
                        )
                    )
                } else {
                    log.warn("Entity is not of userType PartnerUser!! Partner invitation email will not be sent....")
                    null
                }
            }
            ValidationType.INVITE_BACKOFFICE -> {
                if (loginAccount is BackofficeUserEntity) {
                    BackofficeUserContext.ModelMapper.toContext(
                        BackofficeUserContext(
                            loginAccount.firstName,
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                        )
                    )
                } else {
                    log.warn("Entity is not of userType Backoffice User!! Verification email will not be sent....")
                    null
                }
            }
            ValidationType.INVITE_PARTNER_CORPORATE -> {
                if (loginAccount is CorporateUserEntity && loginAccount.corporate.partner!=null) {
                    val partnerEntity = loginAccount.corporate.partner
                    val partnerRootUser = partnerEntity?.partnerUsers?.filter { it.id == partnerEntity.rootUserId }?.get(0)
                    PartnerInviteContext.ModelMapper.toContext(
                        PartnerInviteContext(
                            loginAccount.firstName,
                            "${partnerRootUser?.firstName} ${partnerRootUser?.lastName}",
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                            partnerEntity?.name,
                            "${partnerRootUser?.firstName} ${partnerRootUser?.lastName}"


                        )
                    )
                } else {
                    log.warn("Entity is not of userType CorporateUser or Is not invited by partner. Partner invitation email will not be sent....")
                    null
                }
            }
            ValidationType.INVITE_PARTNER_EXPERT -> {
                if (loginAccount is ExpertUserEntity && loginAccount.companyProfile?.associatedPartners!!.isNotEmpty()) {
                    PartnerInviteContext.ModelMapper.toContext(
                        PartnerInviteContext(
                            loginAccount.firstName,
                            "${loginAccount.firstName} ${loginAccount.lastName}",
                            signUpTokenUrl(webUrl, entity.code),
                            awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                            //TODO update below to use company_type column from expert_company_profile table
                            loginAccount.companyProfile?.associatedPartners?.get(0)!!.name
                        )
                    )
                } else {
                    log.warn("Entity is not of userType PartnerUser!! Partner invitation email will not be sent....")
                    null
                }
            }
        }
    }
}