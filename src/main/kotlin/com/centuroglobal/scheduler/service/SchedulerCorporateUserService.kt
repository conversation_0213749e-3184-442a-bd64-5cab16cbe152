package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class SchedulerCorporateUserService(
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateRepository: CorporateRepository
) {
    @Transactional
    fun updateSubscriptionForUser(
        userId: Long,
        endTime: LocalDateTime,
        role: Role = Role.ROLE_CORPORATE_SUBSCRIBER,
        subscriptionActive: Boolean = true
    ) {
        val rootUser = corporateUserRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        // Update the corporate
        val corporate = rootUser.corporate
        corporate.subscriptionActive = subscriptionActive
        corporate.subscriptionExpiryDate = endTime
        corporateRepository.saveAndFlush(corporate)

        // Update role of all users of the corporate
        corporateUserRepository.updateRoleByCorporateId(rootUser.corporate.id!!, role.name)

    }

}