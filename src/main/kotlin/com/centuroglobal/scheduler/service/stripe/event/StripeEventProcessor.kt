package com.centuroglobal.scheduler.service.stripe.event

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import com.centuroglobal.shared.data.entity.stripe.StripeEventEntity
import com.centuroglobal.shared.data.entity.stripe.StripeSubscriptionEntity
import com.centuroglobal.shared.data.entity.stripe.StripeTransactionEntity
import com.centuroglobal.shared.data.enums.stripe.StripeEventJobCompletionStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventType
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.stripe.StripeAccountRepository
import com.centuroglobal.shared.repository.stripe.StripeEventRepository
import com.centuroglobal.shared.repository.stripe.StripeSubscriptionRepository
import com.centuroglobal.shared.repository.stripe.StripeTransactionRepository
import mu.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

interface StripeEventProcessor {
    fun processEvent(event: StripeEventEntity, account: StripeAccountEntity): StripeEventJobCompletionStatus
}

@Service
class StripeEventProcessorHelper(
    private val stripeEventRepository: StripeEventRepository,
    private val stripeTransactionRepository: StripeTransactionRepository,
    private val stripeSubscriptionRepository: StripeSubscriptionRepository,
    private val stripeAccountRepository: StripeAccountRepository
) {
    fun retrieveEvents(): Page<StripeEventEntity> {
        return stripeEventRepository.findAllByStatusAndTypeIn(
            StripeEventStatus.LOGGED,
            StripeEventType.values().toList(),
            PageRequest.of(0, 20, Sort.by(Sort.Direction.ASC, "stripeLoggedDate"))
        )
    }

    fun logTransaction(entity: StripeTransactionEntity) {
        stripeTransactionRepository.save(entity)
    }

    fun retrieveSubscription(
        subscriptionId: String,
        activeOnly: Boolean,
        event: StripeEventEntity
    ): StripeSubscriptionEntity? {
        val subscriptions =
            if (activeOnly) {
                stripeSubscriptionRepository.findAllBySubscriptionIdAndSubscriptionActiveOrderByCreatedDateDesc(
                    subscriptionId,
                    true
                )
            } else {
                stripeSubscriptionRepository.findAllBySubscriptionIdOrderByLastUpdatedDateDesc(
                    subscriptionId
                )
            }

        return if (subscriptions.isEmpty()) {
            val error = if (activeOnly) {
                ErrorCode.NO_ACTIVE_SUBSCRIPTION_FOUND(event.customerId, subscriptionId)
            } else {
                ErrorCode.NO_SUBSCRIPTION_FOUND(event.customerId, subscriptionId)
            }
            skipEventProcessing(error.errorMessage, event)
            null
        } else {
            subscriptions[0]
        }
    }

    fun saveSubscriptionOnErrorThrows(customerId: String, subscription: StripeSubscriptionEntity) {
        try {
            stripeSubscriptionRepository.saveAndFlush(subscription)
        } catch (ex: Exception) {
            log.error(
                "Unable to update subscription ${subscription.id} for customer $customerId",
                ex
            )
            throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
        }
    }

    fun retrieveAccount(event: StripeEventEntity): StripeAccountEntity? {
        val stripeAccount = stripeAccountRepository.findByCustomerId(event.customerId)
        return if (stripeAccount == null) {
            skipEventProcessing(
                "Unable to find StripeAccountEntity for this customer ${event.customerId}. This event will be skipped.",
                event
            )
            null
        } else {
            stripeAccount
        }
    }

    fun skipEventProcessing(message: String, event: StripeEventEntity) {
        event.status = StripeEventStatus.SKIPPED
        stripeEventRepository.save(event)
        log.warn(message)
    }
}