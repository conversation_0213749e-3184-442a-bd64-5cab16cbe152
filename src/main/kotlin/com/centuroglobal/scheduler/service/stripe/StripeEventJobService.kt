package com.centuroglobal.scheduler.service.stripe

import com.centuroglobal.scheduler.service.stripe.event.StripeEventProcessor
import com.centuroglobal.scheduler.service.stripe.event.StripeEventProcessorHelper
import com.centuroglobal.shared.data.entity.stripe.StripeEventEntity
import com.centuroglobal.shared.data.enums.stripe.StripeEventJobCompletionStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventType
import com.centuroglobal.shared.repository.stripe.StripeEventRepository
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StripeEventJobService(
    private val stripeEventRepository: StripeEventRepository,
    private val helper: StripeEventProcessorHelper,

    @Qualifier("stripeInvoiceEventProcessor")
    private val invoiceEventProcessor: StripeEventProcessor,

    @Qualifier("stripePaymentIntentEventProcessor")
    private val paymentIntentEventProcessor: StripeEventProcessor,

    @Qualifier("stripeSubscriptionEventProcessor")
    private val subscriptionEventProcessor: StripeEventProcessor,

    @Qualifier("stripeCheckoutSessionEventProcessor")
    private val checkoutSessionEventProcessor: StripeEventProcessor
) {

    @Transactional
    fun processEvents() {
        val page = helper.retrieveEvents()

        if (page.content.size > 0) {
            val stripeEventEntityList: MutableList<StripeEventEntity> = mutableListOf()

            page.toList().forEach {
                // get the correct eventProcessor, else exit loop
                val eventProcessor = retrieveProcessor(it) ?: return@forEach

                // make sure that the stripeAccountEntity exist, else exit loop
                val stripeAccount = helper.retrieveAccount(it) ?: return@forEach

                // process the event, exit loop if completion status is Skipped
                if (eventProcessor.processEvent(it, stripeAccount) == StripeEventJobCompletionStatus.SKIPPED) {
                    return@forEach
                }

                // set event status to PROCESSED
                it.status = StripeEventStatus.PROCESSED
                stripeEventEntityList.add(it)
            }

            if (stripeEventEntityList.isNotEmpty()) {
                stripeEventRepository.saveAll(stripeEventEntityList)
            }
        }
    }

    // In future, find a better way to do this routing...
    private fun retrieveProcessor(event: StripeEventEntity): StripeEventProcessor? {
        return when (event.type) {
            StripeEventType.CHECKOUT_SESSION_COMPLETED -> checkoutSessionEventProcessor
            StripeEventType.INVOICE_PAYMENT_SUCCEEDED, StripeEventType.INVOICE_PAYMENT_FAILED -> invoiceEventProcessor
            StripeEventType.PAYMENT_INTENT_SUCCEEDED, StripeEventType.PAYMENT_INTENT_PAYMENT_FAILED -> paymentIntentEventProcessor
            StripeEventType.CUSTOMER_SUBSCRIPTION_DELETED, StripeEventType.CUSTOMER_SUBSCRIPTION_UPDATED -> subscriptionEventProcessor
        }
    }
}
