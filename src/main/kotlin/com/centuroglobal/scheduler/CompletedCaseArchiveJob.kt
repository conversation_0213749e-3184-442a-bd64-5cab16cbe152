package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerCaseService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class CompletedCaseToArchiveJob (private val caseService: SchedulerCaseService) {

    @Scheduled(cron = "\${app.completed-case-archive.cron-schedule}")
    fun doCompletedCaseArchive() {
        log.info("******  CompletedCaseArchiveJob Job [STARTED] *********")
        caseService.updateCaseArchiveForCompletedStatus()
        log.info("******  CompletedCaseArchiveJob Job [ENDED] *********")
    }

}