package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerTaskWorkflowService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class TaskWorkflowJob(
    private val taskWorkflowService: SchedulerTaskWorkflowService

) {

    @Scheduled(cron = "\${app.task-workflow.cron-schedule}")
    fun generateBilling() {

        log.info("******  TaskWorkflowJob Job [STARTED] *********")

        taskWorkflowService.pendingAndDueToday()

        log.info("******  TaskWorkflowJob Job [ENDED] *********")
    }
}