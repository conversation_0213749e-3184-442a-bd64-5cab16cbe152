package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerTokenVerificationService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class TokenVerificationJob(private val tokenVerificationService: SchedulerTokenVerificationService) {

    @Scheduled(cron = "0 0 1 * * *") // every day 1am
    fun cleanupToken() {
        log.trace("******  cleanupToken Job [STARTED] *********")
        tokenVerificationService.cleanUp()
        log.trace("******  cleanupToken Job [ENDED] *********")
    }

    @Scheduled(cron = "\${app.verification.cron-schedule}")
    fun sendVerificationEmails() {
        log.trace("****** sendVerificationEmails Job [STARTED] *********")
        tokenVerificationService.sendVerificationEmails()
        log.trace("****** sendVerificationEmails Job [ENDED] *********")
    }
}
