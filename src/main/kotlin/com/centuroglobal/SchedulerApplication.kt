package com.centuroglobal

import org.springframework.boot.Banner
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.EnableAspectJAutoProxy

@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableFeignClients
class SchedulerApplication

fun main(args: Array<String>) {
    runApplication<SchedulerApplication>(*args) {
        setBannerMode(Banner.Mode.OFF)
    }
}