package com.centuroglobal.data.payload.lead

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

data class CreateLeadRequest(

    @field:NotBlank
    val title: String,

    @Schema()
    @field:NotBlank
    val description: String,

    @Schema( required = true)
    val expertiseId: List<Int>,

    @Schema( required = true)
    @field:Pattern(regexp = "[A-Za-z]{2}")
    val countryCode: String,

    @Schema()
    val regionId: Int?
)
