package com.centuroglobal.data.payload.auth

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema

@JsonIgnoreProperties(ignoreUnknown = true)
data class TokenRequest(
    @Schema(required = true, allowableValues = ["password", "refresh_token"])
    @JsonProperty("grant_type")
    val grantType: String,
    @Schema(description =  "Specify the scope of the token required", allowableValues = ["app"])
    val scope: String?,
    @Schema(description =  "Required if grantType is refresh_token")
    @JsonProperty("refresh_token")
    val refreshToken: String?,
    @Schema(description =  "Required if grantType is password")
    val username: String?,
    @Schema(description =  "Required if grantType is password")
    val password: String?,
    @JsonProperty("mfa_code")
    val mfaCode: String?
)