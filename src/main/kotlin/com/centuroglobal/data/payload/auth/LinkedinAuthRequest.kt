package com.centuroglobal.data.payload.auth

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema

@JsonIgnoreProperties(ignoreUnknown = true)
data class LinkedinAuthRequest(
    @Schema(description =  "Required for linkedin login")
    val code: String,

    val callerId: String,

    val redirectURL: String,

    val keepMeInformed: String?=null
)
