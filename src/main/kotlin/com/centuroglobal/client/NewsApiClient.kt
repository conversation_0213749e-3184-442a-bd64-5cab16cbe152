package com.centuroglobal.client

import com.centuroglobal.data.payload.news.NewsList
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(name = "NewsApiClient", url = "https://api.thenewsapi.com/v1/news/top")
interface NewsApiClient {

    @RequestMapping
    fun findAllNews(
        @RequestParam(name = "api_token") apiToken: String,
        @RequestParam("search") search: String?,
        @RequestParam("limit") limit: Int
    ): NewsList
}