# CENTURO GLOBAL SERVER #



## RUNNING THE PROJECT ##

### IDE ###

Recommended IDE is Intellij `2019.2` or later.

### Running the Database ###

Easiest way is to start the database as a docker container.


```bash
docker-compose -f docker-compose.db.yml up -d 
```

The default port is `3306` and the `root` password is `pass1234`.

### Starting the Application ###

Recommended to run inside your IDE during development. 

### Factory Data ###

There are some factory data that is part of the data migration scripts.

For factory generated login data, the password is `P@ssword123`.



### Some URLs ###

* http://localhost:8080 - Root url will redirect to the swagger documentation page
* http://localhost:8080/actuator - springboot actuator 

## STACK ##

### LANGUAGE AND COMPILERS ###

* Programming Language - Kotlin & Java
* Java is compiled using JDK 11 (Can be downgraded to JDK 8 if needed)
* Kotlin is compiled using JDK 8
* Runtime is JDK 11
* Gradle is 7.4

### SPRING FRAMEWORK ###

* Springboot 2.5.11
* Async Web MVC (No Webflux for now)
* Spring Security
* Embedded Web Container - Undertow
* Database Migration tool - flyway
* Spring JPA / Hibernate
  * Preferred no entity relationship in domain classes
  * 1-to-1 allowed.
  * 1-to-many, many-to-1, many-to-many to be avoided.

### DATABASE ###

* MariaDB 10.6 - Dev
* RDS - Prod
* Default engine - InnoDB
* Default charset - utf8mb4

### LOGGING ###

* io.github.microutils:kotlin-logging to wrap slf4j

### DOCUMENTATION ###

* Springfox Swagger

### TESTING ###

* Groovy/Spock
* Wiremock
* Test Container

## GUIDELINES ##

### ASYC MVC WORKFLOW ###

`Controller -> AsyncFacade -> Service -> Repository`

Unless due to special business/technical requirement, all response should be from the `Response<T>` class.

Since we are using async mvc, controllers should return `DeferredResult`. So, the correct return from each controller method should be `DeferredResult<Response<T>>`.

There should be minimal or no business logic in the controller layer.

Facade layer is there to provide the `Async` entry point.

Orchestrator classes may be introduced to orchestrate complex business logic that span across multiple services.

Transactional boundary should starts from the orchestrator or service layer. Never starts from controller and unless due to specific requirement, it should also not be in the repository layer.

Using async mvc, the thread accepting the http request (Http NIO thread) will be different from the thread (worker thread) that is executing the task. This means that it is technically impossible to have the transactional boundary starts at the controller level as a transaction boundary cannot span across 2 different threads. You will not be able to inject or autowire any request scope type of beans in the service layer.

### ERROR AND EXCEPTION HANDLING ###

Define every individual error in `ErrorCode.kt`.

In service, orchestrator, facade etc... `try-catch` wrapped and rethrow exceptions as `ApplicationException`.

It is preferred to throw `ApplicationException` rather than passing `Error` around.

Global controller advice is there to intercept `ApplicationException` and return the appropriate response.

### CONSTANT CLASSES ###

`object` was meant to be used as a singleton class. Off topic, but declaring as an `object` instead of `class`, annotations e.g. `@PostConstruct` doesn't work.

Anyway, to implement constant class, we will be using object as follows:

```kotlin
object ErrorCode {
    // Generic Errors
    val BAD_REQUEST: Error = Error(httpStatus = HttpStatus.BAD_REQUEST, errorCode = "HTTP_400", errorMessage = "Bad Request")
    val UNAUTHORIZED: Error = Error(httpStatus = HttpStatus.UNAUTHORIZED, errorCode = "HTTP_401", errorMessage = "Unauthorized")
}
```

### UTILITY FINAL STATIC CLASSES ###

We will be implementing like using companion object. An example implementation is in `ResponseUtil.kt`.

```kotlin
class SomeUtil {
    companion object {

        @JvmStatic
        fun doTask(objectMapper: ObjectMapper) {
            // do some stuff
        }
    }
}
```

### DATA CLASSES ###

Enums should be added under the package `com.centuroglobal.shared.data.enums`.

Entity models should be under the package `com.centuroglobal.shared.data.entity` and should be suffix with `Entity`.

Request/Response should be under the package `com.centuroglobal.data.payload`.

POJOs (or in this case POKO) should be under the package `com.centuroglobal.shared.data.pojo`.

Configuration properties should be under the package `com.centuroglobal.data.properties`.

All Rest request payload should be suffix with `Request`.

All Rest responses should return with type `Response<T>` where `T` is a payload and cannot be an entity.

Right now, no data mapping framework had been identified. Mappings are converted to entity in the payload data class.

Entities should not exist outside the service layer. Meaning all params in/out of service layer methods should only be payload.

### SLF4J LOGGING WRAPPER ###

The logger should be implemented as a top level `val`.

``` kotlin
private val log = KotlinLogging.logger {}

@Service
class UserAccountService(private val userAccountRepository: UserAccountRepository) {

    @Transactional(readOnly = true)
    fun retrieveAll(): List<UserAccount> {
        log.debug("Attempt to retrieve All User Accounts ....")
        return emptyList()
    }
}
```

### NAMING CONVENTIONS ###

* Database
  * Table and columns names to be lowercase and snake case  
  * Table name should be singular

## REFERENCES ##

* <https://kotlinlang.org/docs/reference/>
* <https://kotlinlang.org/docs/reference/java-to-kotlin-interop.html>
* <https://phauer.com/2017/idiomatic-kotlin-best-practices/>
* <https://www.baeldung.com/kotlin-java-static-final>
* <https://www.baeldung.com/spring-security-method-security>

