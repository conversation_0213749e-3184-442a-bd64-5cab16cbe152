version: '3.3'
services:
  cg-db:
    container_name: cg-db
    image: mariadb:10.6
    volumes:
      - ./data/db:/var/lib/mysql
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: pass1234
      MYSQL_DATABASE: centuro_global
      MYSQL_ROOT_HOST: '%'
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "--silent", "-ppass1234"]
      interval: 5s
      timeout: 10s
      retries: 3
