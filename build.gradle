plugins {
	id 'org.springframework.boot' version '3.3.0'
	id 'io.spring.dependency-management' version '1.1.5'
	id 'java'
	id 'groovy'
	id 'idea'
	id 'jacoco'
	id 'org.jetbrains.kotlin.jvm' version '2.0.20'
	id 'org.jetbrains.kotlin.plugin.spring' version '2.0.20'
	id 'org.jetbrains.kotlin.plugin.noarg' version '2.0.20'
	id 'maven-publish'
	id "org.sonarqube" version "2.8"
}

noArg {
	annotation("jakarta.persistence.Entity")
	annotation("jakarta.persistence.MappedSuperclass")
	annotation("jakarta.persistence.Embeddable")
}

bootJar {
	enabled = false
}
jar {
	enabled = true
}

dependencyManagement {
	imports {
		mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2023.0.1'
	}
}

group = 'com.centuroglobal'
version = '1.8.10-SNAPSHOT'

sourceCompatibility = '22'

ext {
	nexusRepoUsername = System.getenv("NEXUS_REPO_USERNAME")
	nexusRepoPassword = System.getenv("NEXUS_REPO_PASSWORD")
	awsSdkVersion = '2.20.67'
	testcontainersVersion = System.getenv("TESTCONTAINERS_VERSION")?:'1.15.0-rc2'
}

configurations {
	compile.exclude module: 'spring-boot-starter-tomcat'
	developmentOnly
	runtimeClasspath {
		extendsFrom developmentOnly
	}
}


java {
	withSourcesJar()
	withJavadocJar()
}
publishing {
	publications {
		maven(MavenPublication) {
			from components.java
		}
	}
	repositories {
		maven {
			url 'https://centuro-global-162682813712.d.codeartifact.eu-west-2.amazonaws.com/maven/cg-shared-lib/'
			credentials {
				username "aws"
				password System.getenv("CODEARTIFACT_AUTH_TOKEN")
			}
		}
	}
	print("Token"+System.getenv("CODEARTIFACT_AUTH_TOKEN"))
}


repositories {
	mavenCentral()

	flatDir {
		dirs 'libs'
	}
}

dependencies {
	implementation 'org.springframework.cloud:spring-cloud-starter-config'
	implementation 'org.springframework.cloud:spring-cloud-config-server'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	// Spring boot 3.x is Jakarta EE
	implementation 'com.stripe:stripe-java:20.13.0'
	implementation 'jakarta.persistence:jakarta.persistence-api'
	implementation 'jakarta.servlet:jakarta.servlet-api'
	implementation "software.amazon.awssdk:sdk-core:$awsSdkVersion"
	implementation 'io.micrometer:micrometer-registry-prometheus'
	implementation "software.amazon.awssdk:secretsmanager:$awsSdkVersion"
	implementation "software.amazon.awssdk:s3:$awsSdkVersion"
	implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.2'
	implementation 'org.jetbrains.kotlin:kotlin-reflect'
	implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.2'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.0.2'
	implementation 'io.github.microutils:kotlin-logging:2.1.21'
	implementation 'commons-io:commons-io:2.6'
	implementation 'org.springframework:spring-test'
	implementation 'org.apache.commons:commons-compress:1.21'
	implementation 'commons-io:commons-io:2.11.0'
	implementation 'commons-codec:commons-codec:1.15'
	implementation 'io.jsonwebtoken:jjwt:0.9.1'
	implementation 'org.apache.tika:tika-core:1.24.1'
	implementation 'com.google.code.gson:gson:2.9.0'
	implementation 'com.googlecode.libphonenumber:libphonenumber:8.12.45'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'com.github.ben-manes.caffeine:caffeine'
	implementation 'io.github.openfeign:feign-okhttp:10.12'
	testImplementation 'com.google.code.gson:gson:2.9.0'
	implementation ('org.xhtmlrenderer:flying-saucer-pdf:9.1.22') {
		exclude group: 'org.bouncycastle'
	}
	// https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api
	implementation group: 'javax.xml.bind', name: 'jaxb-api', version: '2.1'
}

test{
	useJUnitPlatform()
}

sourceSets {
	main.kotlin.srcDirs += 'src/main/kotlin'
	main.java.srcDirs += 'src/main/java'
}

//tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
//	kotlinOptions {
//		freeCompilerArgs += "-Xjsr305=strict"
//		jvmTarget = "21"
//		suppressWarnings = true
//	}
//}

sonarqube {
	properties {
		property 'sonar.coverage.exclusions',
				"""             
                    **/facade/stripe/*.kt,
                    **/service/stripe/StripeEventLogService.kt,
                    
                """
	}
}