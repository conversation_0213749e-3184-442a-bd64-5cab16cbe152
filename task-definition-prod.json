{"containerDefinitions": [{"name": "cg-schedular-prod", "image": "162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-schedular-prod-ecs:latest", "cpu": 0, "portMappings": [{"name": "cg-schedular-prod-80-tcp", "containerPort": 80, "hostPort": 0, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::cg-goglobal-uat/env/prod/java.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/cg-schedular-server-ecs-prod", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs"}}, "readonlyRootFilesystem": false}], "family": "cg-schedular-prod", "taskRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "cpu": "150", "memory": "750", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}