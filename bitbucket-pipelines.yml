image: openjdk:22-jdk-slim

clone:
  depth: full

definitions:
  caches:
    gradlewrapper: ~/.gradle/wrapper
    sonar: ~/.sonar/cache
  # services:
  #   docker:
  #     image: docker:dind

  steps:
  - step: &CodeArtifact-Token
      name: CodeArtifact-Token
      image: atlassian/pipelines-awscli
      script:
      - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
      - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
      - export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
      - echo $AWS_DEFAULT_REGION
      - aws codeartifact get-authorization-token --domain centuro-global --domain-owner 162682813712  --query authorizationToken --output text > pass.txt
      - value=$(<pass.txt)
      - echo $value
      - echo "export CODEARTIFACT_AUTH_TOKEN=$value" > set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - printenv >> set_env.sh
      - cat set_env.sh
      artifacts:
      - set_env.sh

  - step: &Build
      name: Build
      runs-on:
      - "self.hosted"
      - "linux"
      size: 1x
      # size: 2x
      caches:
      - gradle
      - gradlewrapper
      - sonar
      services:
      - docker
      script:
      - cat set_env.sh
      - source set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - export TESTCONTAINERS_RYUK_DISABLED=true
      - export TESTCONTAINERS_VERSION=1.14.1
      - echo "info.git.branch=$BITBUCKET_TAG$BITBUCKET_BRANCH" > src/main/resources/application.properties
      - echo "info.git.commit.id=$BITBUCKET_COMMIT_SHORT" >> src/main/resources/application.properties
      - echo "info.git.commit.time=$(date --utc +%FT%TZ)" >> src/main/resources/application.properties
      - echo java -version
      - bash ./gradlew clean
      - bash ./gradlew build
      # - bash ./gradlew sonarqube
      # - bash ./gradlew test jacocoTestReport sonarqube
      - mv build/libs/cg-server-0.0.1-SNAPSHOT.jar build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar

      artifacts:
      - build/libs/**

  - step: &Build-qa
      name: Build-qa
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 1x
      # size: 2x
      caches:
      - gradle
      - gradlewrapper
      - sonar
      services:
      - docker
      script:
      - cat set_env.sh
      - source set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - export TESTCONTAINERS_RYUK_DISABLED=true
      - export TESTCONTAINERS_VERSION=1.14.1
      - echo "info.git.branch=$BITBUCKET_TAG$BITBUCKET_BRANCH" > src/main/resources/application.properties
      - echo "info.git.commit.id=$BITBUCKET_COMMIT_SHORT" >> src/main/resources/application.properties
      - echo "info.git.commit.time=$(date --utc +%FT%TZ)" >> src/main/resources/application.properties
      - echo java -version
      - echo $HOSTNAME
      - bash ./gradlew clean
      - bash ./gradlew build
      # - bash ./gradlew sonarqube -x test
      #      - bash ./gradlew test jacocoTestReport sonarqube
      - mv build/libs/cg-server-0.0.1-SNAPSHOT.jar build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar
      artifacts:
      - build/libs/**

  - step: &Build-prod
      name: "Build PROD"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 2x
      caches:
      - gradle
      - gradlewrapper
      - sonar
      services:
      - docker
      script:
      - cat set_env.sh
      - source set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - export TESTCONTAINERS_RYUK_DISABLED=true
      - export TESTCONTAINERS_VERSION=1.14.1
      - echo "info.git.branch=$BITBUCKET_TAG" > src/main/resources/application.properties
      - echo "info.git.commit.id=$BITBUCKET_COMMIT_SHORT" >> src/main/resources/application.properties
      - echo "info.git.commit.time=$(date --utc +%FT%TZ)" >> src/main/resources/application.properties
      - bash ./gradlew clean
      - bash ./gradlew build -x test
      - mv build/libs/cg-server-0.0.1-SNAPSHOT.jar build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar
      artifacts:
      - build/libs/**

  - step: &Build-prod-ecs
      name: "Build PROD"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 2x
      caches:
      - gradle
      - gradlewrapper
      - sonar
      services:
      - docker
      script:
      - cat set_env.sh
      - source set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - export TESTCONTAINERS_RYUK_DISABLED=true
      - export TESTCONTAINERS_VERSION=1.14.1
      - echo "info.git.branch=$BITBUCKET_TAG" > src/main/resources/application.properties
      - echo "info.git.commit.id=$BITBUCKET_COMMIT_SHORT" >> src/main/resources/application.properties
      - echo "info.git.commit.time=$(date --utc +%FT%TZ)" >> src/main/resources/application.properties
      - bash ./gradlew clean
      - bash ./gradlew build -x test
      - mv build/libs/cg-server-0.0.1-SNAPSHOT.jar build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar
      artifacts:
      - build/libs/**

  - step: &Build-containers-on-staging
      name: "Build containers on staging"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ubuntu"
          SERVER: "uat-api-goglobal.centuroglobal.com"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $STAGING_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
          DEBUG: "true"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ubuntu"
          SERVER: "uat-api-goglobal.centuroglobal.com"
          SSH_KEY: $STAGING_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh staging && ./build_containers.sh staging $BITBUCKET_BRANCH"

  - step: &Build-containers-on-qa
      name: "Build containers on qa"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ubuntu"
          SERVER: "qat-api-goglobal.centuroglobal.com"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $QAT_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
          DEBUG: "true"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ubuntu"
          SERVER: "qat-api-goglobal.centuroglobal.com"
          SSH_KEY: $QAT_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh qat && ./build_containers.sh qat $BITBUCKET_BRANCH"

  - step: &Build-containers-on-qat-ecs
      name: "Build containers on qat ecs"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ec2-user"
          SERVER: "ec2-3-10-139-220.eu-west-2.compute.amazonaws.com"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $QAT_ECS_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
          DEBUG: "true"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ec2-user"
          SERVER: "ec2-3-10-139-220.eu-west-2.compute.amazonaws.com"
          SSH_KEY: $QAT_ECS_SSH_KEY
          MODE: "command"
          COMMAND: "echo $HOSTNAME && cd /data/cg-deploy && cd scripts && ./build_containers.sh qat $BITBUCKET_BRANCH"
  - step: &Build-containers-on-uat-ecs
      name: "Build containers on uat ecs"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ec2-user"
          SERVER: "*************"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $UAT_ECS_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
          DEBUG: "true"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ec2-user"
          SERVER: "*************"
          SSH_KEY: $UAT_ECS_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh staging && ./build_containers.sh staging $BITBUCKET_BRANCH"
  - step: &Build-containers-on-prod
      name: "Build containers on Prod ecs"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ec2-user"
          SERVER: "**********"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $PROD_ECS_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
          DEBUG: "true"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ec2-user"
          SERVER: "**********"
          SSH_KEY: $PROD_ECS_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh prod && ./build_containers.sh prod $BITBUCKET_TAG"

  - step: &Deploy-to-staging
      name: "Deploy to Staging"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 1x
      script:
      # - pipe: atlassian/scp-deploy:0.3.11
      #   variables:
      #     USER: 'ubuntu'
      #     SERVER: 'uat-api-goglobal.centuroglobal.com'
      #     REMOTE_PATH: '/data/cg-deploy/data/temp/'
      #     SSH_KEY: $STAGING_SSH_KEY
      #     LOCAL_PATH: '$BITBUCKET_CLONE_DIR/build/libs/*'
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ubuntu"
          SERVER: "uat-api-goglobal.centuroglobal.com"
          SSH_KEY: $STAGING_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./deploy_containers.sh staging"
          # COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh staging && ./deploy_java.sh staging $BITBUCKET_BRANCH"
          # COMMAND: "cd /data/cg-deploy && git pull && cd scripts && ./update_env.sh staging && ./deploy_java.sh staging $BITBUCKET_BRANCH"

  - step: &Deploy-to-qa
      name: "Deploy to QA"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 1x
      script:
      # - pipe: atlassian/scp-deploy:0.3.11
      #   variables:
      #     USER: 'ubuntu'
      #     SERVER: 'uat-api-goglobal.centuroglobal.com'
      #     REMOTE_PATH: '/data/cg-deploy/data/temp/'
      #     SSH_KEY: $QAT_SSH_KEY
      #     LOCAL_PATH: '$BITBUCKET_CLONE_DIR/build/libs/*'
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ubuntu"
          SERVER: "qat-api-goglobal.centuroglobal.com"
          SSH_KEY: $QAT_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./deploy_containers.sh qat"
          # COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh staging && ./deploy_java.sh staging $BITBUCKET_BRANCH"
          # COMMAND: "cd /data/cg-deploy && git pull && cd scripts && ./update_env.sh staging && ./deploy_java.sh staging $BITBUCKET_BRANCH"

  - step: &Deploy-to-prod
      name: "Deploy to PROD"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      size: 1x
      script:
      - pipe: atlassian/scp-deploy:0.3.11
        variables:
          USER: "ubuntu"
          SERVER: "api-goglobal.centuroglobal.com"
          REMOTE_PATH: "/data/cg-deploy/data/temp/"
          SSH_KEY: $PROD_SSH_KEY
          LOCAL_PATH: "$BITBUCKET_CLONE_DIR/build/libs/*"
      - pipe: atlassian/ssh-run:0.2.5
        variables:
          SSH_USER: "ubuntu"
          SERVER: "api-goglobal.centuroglobal.com"
          SSH_KEY: $PROD_SSH_KEY
          MODE: "command"
          COMMAND: "cd /data/cg-deploy && cd scripts && ./update_env.sh prod && ./deploy_java.sh prod $BITBUCKET_TAG"
  - step: &Deploy-to-ecs-uat
      name: "Deploy to UAT ECS"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      image: atlassian/pipelines-awscli
      script:
      - pipe: atlassian/aws-ecs-deploy:1.7.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
          CLUSTER_NAME: "UAT-ECS-GO-GLOBAL"
          SERVICE_NAME: "cg-api-server-ecs-uat"
          TASK_DEFINITION: "task-definition-uat.json"

  - step: &Deploy-to-ecs-qat
      name: "Deploy to QAT ECS"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      image: atlassian/pipelines-awscli
      script:
      - pipe: atlassian/aws-ecs-deploy:1.7.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
          CLUSTER_NAME: "QAT-ECS-GO-GLOBAL-1"
          SERVICE_NAME: "cg-api-server-ecs-qat"
          TASK_DEFINITION: "task-definition-qat.json"

  - step: &Deploy-to-prod-ecs
      name: "Deploy to Prod ECS"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      image: atlassian/pipelines-awscli
      script:
      - pipe: atlassian/aws-ecs-deploy:1.7.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
          CLUSTER_NAME: "PROD-ECS-GO-GLOBAL"
          SERVICE_NAME: "cg-api-server-ecs-prod"
          TASK_DEFINITION: "task-definition-prod.json"
  - step: &Build-on-DinD-uat
      name: "Build on DinD uat"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      services:
      - docker
      clone:
        lfs: true
        skip-ssl-verify: true
      script:
      - cat set_env.sh
      - source set_env.sh
      - ls -l -R $BITBUCKET_CLONE_DIR
      - apt-get update && apt-get install -y awscli
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - echo "last commit id is:" ${BITBUCKET_COMMIT_SHORT}
      - aws secretsmanager get-secret-value --secret-id cg-goglobal/staging --query SecretString --output text > java.env
      - cp build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar $BITBUCKET_CLONE_DIR/app.jar
      - ls -l $BITBUCKET_CLONE_DIR | grep app.jar
      - docker build -t cg/cg-server:${BITBUCKET_COMMIT_SHORT} .
      - aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin 162682813712.dkr.ecr.eu-west-2.amazonaws.com || force_exit "Build Containers failed. Failed to login to ECR!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-uat:${BITBUCKET_COMMIT_SHORT} || force_exit "Build Containers failed. Failed to tag cg-web-api-uat!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-uat:latest || force_exit "Build Containers failed. Failed to tag cg-web-api-uat as latest!!!!"
      - docker push  --all-tags 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-uat

  - step: &Build-on-DinD  
      name: "build on dind and push to ECR"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      services:
      - docker
      clone:
        lfs: true
        skip-ssl-verify: true      
      script:
      - cat set_env.sh
      - source set_env.sh
      - ls -l -R $BITBUCKET_CLONE_DIR
      - apt-get update && apt-get install -y awscli
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - echo "last commit id is:" ${BITBUCKET_COMMIT_SHORT}
      - aws secretsmanager get-secret-value --secret-id cg-goglobal-qatecs --query SecretString --output text > java.env
      - cp build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar $BITBUCKET_CLONE_DIR/app.jar
      - ls -l $BITBUCKET_CLONE_DIR | grep app.jar
      - docker build -t cg/cg-server:${BITBUCKET_COMMIT_SHORT} .
      - aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin 162682813712.dkr.ecr.eu-west-2.amazonaws.com || force_exit "Build Containers failed. Failed to login to ECR!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-server-qat-ecs:${BITBUCKET_COMMIT_SHORT} || force_exit "Build Containers failed. Failed to tag cg-server-qat!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-server-qat-ecs:latest || force_exit "Build Containers failed. Failed to tag cg-server-qat as latest!!!!"
      - docker push  --all-tags 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-server-qat-ecs

  - step: &Build-on-DinD-prod
      name: "Build on DinD prod"
      runs-on:
      - "self.hosted"
      - "linux"
      - "bbrunner"
      services:
      - docker
      clone:
        lfs: true
        skip-ssl-verify: true
      script:
      - cat set_env.sh
      - source set_env.sh
      - ls -l -R $BITBUCKET_CLONE_DIR
      - apt-get update && apt-get install -y awscli
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - echo "last commit id is:" ${BITBUCKET_COMMIT_SHORT}
      - aws secretsmanager get-secret-value --secret-id cg-goglobal/prod --query SecretString --output text > java.env
      - cp build/libs/cg-server-$BITBUCKET_COMMIT_SHORT.jar $BITBUCKET_CLONE_DIR/app.jar
      - ls -l $BITBUCKET_CLONE_DIR | grep app.jar
      - docker build -t cg/cg-server:${BITBUCKET_COMMIT_SHORT} .
      - aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin 162682813712.dkr.ecr.eu-west-2.amazonaws.com || force_exit "Build Containers failed. Failed to login to ECR!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-prod:${BITBUCKET_COMMIT_SHORT} || force_exit "Build Containers failed. Failed to tag cg-web-api-prod!!!!"
      - docker tag cg/cg-server:${BITBUCKET_COMMIT_SHORT} 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-prod:latest || force_exit "Build Containers failed. Failed to tag cg-web-api-prod as latest!!!!"
      - docker push  --all-tags 162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-prod

pipelines:
  branches:
    #    '**':
    #      - step: *Build
    #      - step:
    #          <<: *Deploy-to-staging
    #          trigger: manual

    dev:
    - step: *CodeArtifact-Token
    - step: *Build
    - step: *Build-containers-on-staging
    - step: *Deploy-to-staging
      # - step:
      #     <<: *Deploy-to-staging
      #     trigger: manual
    #    feature/*:
    #      - step: *Build

    qa:
    - step: *CodeArtifact-Token
    - step: *Build-qa
    - step: *Build-containers-on-qa
    - step: *Deploy-to-qa
      # - step:
      #     <<: *Deploy-to-qa
      #     trigger: manual
    #    feature/*:
    #      - step: *Build

    qat:
    - step: *CodeArtifact-Token
    - step: *Build-qa
    # - step: *Build-containers-on-qat-ecs
    - step: *Build-on-DinD
    - step: *Deploy-to-ecs-qat
  
    uat:
    - step: *CodeArtifact-Token
    - step: *Build
    #- step: *Build-containers-on-staging
    #- step: *Deploy-to-staging
    # - step: *Build-containers-on-uat-ecs
    - step: *Build-on-DinD-uat
    - step: *Deploy-to-ecs-uat

  tags:
    release-*:
    - step: *CodeArtifact-Token
    - step: *Build-prod
    - step: *Deploy-to-prod
    release-ecs-*:
    - step: *CodeArtifact-Token
    - step: *Build-prod-ecs
    # - step: *Build-containers-on-prod
    - step: *Build-on-DinD-prod
    - step: *Deploy-to-prod-ecs