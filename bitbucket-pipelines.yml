image: openjdk:22-jdk-slim

clone:
  depth: full

definitions:
  caches:
    gradlewrapper: ~/.gradle/wrapper
    sonar: ~/.sonar/cache

  steps:
  - step: &CodeArtifact-Token
      name: CodeArtifact-Token
      image: atlassian/pipelines-awscli
      script:
      - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
      - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
      - export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
      - echo $AWS_DEFAULT_REGION
      - aws codeartifact get-authorization-token --domain centuro-global --domain-owner 162682813712  --query authorizationToken --output text > pass.txt
      - value=$(<pass.txt)
      - echo $value
      - echo "export CODEARTIFACT_AUTH_TOKEN=$value" > set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - printenv >> set_env.sh
      - cat set_env.sh
      artifacts:
      - set_env.sh

  - step: &Build-qa
      name: Build-qa
      runs-on:
      - "self.hosted"
      - "linux"
      size: 1x
      caches:
      - gradle
      - gradlewrapper
      - sonar
      services:
      - docker
      script:
      - cat set_env.sh
      - source set_env.sh
      - echo $CODEARTIFACT_AUTH_TOKEN
      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-10)
      - export TESTCONTAINERS_RYUK_DISABLED=true
      - echo $TESTCONTAINERS_RYUK_DISABLED
      - export TESTCONTAINERS_VERSION=1.14.1
      - echo "info.git.branch=$BITBUCKET_TAG$BITBUCKET_BRANCH" > src/main/resources/application.properties
      - echo "info.git.commit.id=$BITBUCKET_COMMIT_SHORT" >> src/main/resources/application.properties
      - echo "info.git.commit.time=$(date --utc +%FT%TZ)" >> src/main/resources/application.properties
      - echo java -version
      - bash ./gradlew clean
      - bash ./gradlew publish
      # - bash ./gradlew sonarqube -x test
      #      - bash ./gradlew test jacocoTestReport sonarqube
      #- mv build/libs/cg-schedular-0.0.1-SNAPSHOT.jar build/libs/cg-schedular-$BITBUCKET_COMMIT_SHORT.jar
      #artifacts:
      #- build/libs/**

pipelines:
  branches:
    qat:
    - step: *CodeArtifact-Token
    - step: *Build-qa
    uat:
    - step: *CodeArtifact-Token
    - step: *Build-qa
